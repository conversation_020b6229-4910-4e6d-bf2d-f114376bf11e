using System.Collections.Concurrent;

namespace SmaTrendFollower.Services;

/// <summary>
/// High-performance rolling statistics calculator for real-time anomaly detection.
/// Maintains a sliding window of values and computes mean, standard deviation, and z-scores efficiently.
/// Thread-safe implementation using lock-free operations where possible.
/// </summary>
public sealed class RollingStats
{
    private readonly int _windowSize;
    private readonly Queue<double> _values = new();
    private readonly object _lock = new();
    private double _sum = 0.0;
    private double _sumOfSquares = 0.0;

    /// <summary>
    /// Initializes a new rolling statistics calculator
    /// </summary>
    /// <param name="windowSize">Size of the rolling window (default: 200 samples)</param>
    public RollingStats(int windowSize = 200)
    {
        if (windowSize <= 0)
            throw new ArgumentException("Window size must be positive", nameof(windowSize));
        
        _windowSize = windowSize;
    }

    /// <summary>
    /// Adds a new value to the rolling window and updates statistics
    /// </summary>
    /// <param name="value">The value to add</param>
    public void Add(double value)
    {
        if (double.IsNaN(value) || double.IsInfinity(value))
            return; // Skip invalid values

        lock (_lock)
        {
            _values.Enqueue(value);
            _sum += value;
            _sumOfSquares += value * value;

            // Remove oldest value if window is full
            if (_values.Count > _windowSize)
            {
                var oldValue = _values.Dequeue();
                _sum -= oldValue;
                _sumOfSquares -= oldValue * oldValue;
            }
        }
    }

    /// <summary>
    /// Gets the current mean of values in the window
    /// </summary>
    public double Mean
    {
        get
        {
            lock (_lock)
            {
                return _values.Count == 0 ? 0.0 : _sum / _values.Count;
            }
        }
    }

    /// <summary>
    /// Gets the current standard deviation of values in the window
    /// </summary>
    public double StandardDeviation
    {
        get
        {
            lock (_lock)
            {
                if (_values.Count < 2)
                    return 1.0; // Default to 1.0 to avoid division by zero

                var mean = _sum / _values.Count;
                var variance = (_sumOfSquares - _sum * mean) / (_values.Count - 1);
                return Math.Sqrt(Math.Max(0, variance)); // Ensure non-negative
            }
        }
    }

    /// <summary>
    /// Calculates the z-score for a given value
    /// </summary>
    /// <param name="value">The value to calculate z-score for</param>
    /// <returns>Z-score (standard deviations from mean)</returns>
    public double CalculateZScore(double value)
    {
        var std = StandardDeviation;
        if (std == 0.0)
            return 0.0; // No variation in data

        return (value - Mean) / std;
    }

    /// <summary>
    /// Gets the current number of values in the window
    /// </summary>
    public int Count
    {
        get
        {
            lock (_lock)
            {
                return _values.Count;
            }
        }
    }

    /// <summary>
    /// Checks if the window has enough data for reliable statistics
    /// </summary>
    public bool HasSufficientData => Count >= Math.Min(20, _windowSize / 10);

    /// <summary>
    /// Clears all values from the rolling window
    /// </summary>
    public void Clear()
    {
        lock (_lock)
        {
            _values.Clear();
            _sum = 0.0;
            _sumOfSquares = 0.0;
        }
    }

    /// <summary>
    /// Gets a snapshot of current statistics
    /// </summary>
    public RollingStatsSnapshot GetSnapshot()
    {
        lock (_lock)
        {
            return new RollingStatsSnapshot(
                Count: _values.Count,
                Mean: Mean,
                StandardDeviation: StandardDeviation,
                WindowSize: _windowSize,
                HasSufficientData: HasSufficientData
            );
        }
    }
}

/// <summary>
/// Immutable snapshot of rolling statistics at a point in time
/// </summary>
public readonly record struct RollingStatsSnapshot(
    int Count,
    double Mean,
    double StandardDeviation,
    int WindowSize,
    bool HasSufficientData
);
