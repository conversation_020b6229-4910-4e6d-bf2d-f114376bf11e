using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Text.Json;

namespace SmaTrendFollower.Models;

// MarketRegime enum moved to MLModels.cs for ML Phase 3 implementation

/// <summary>
/// Model for trailing stop-loss data stored in Redis cache.
/// Used for fast retrieval during live trading to maintain stop levels.
/// </summary>
public class RedisTrailingStop
{
    /// <summary>
    /// Stock symbol (e.g., "AAPL", "TSLA")
    /// </summary>
    public string Symbol { get; set; } = string.Empty;

    /// <summary>
    /// Current trailing stop price level
    /// </summary>
    public decimal StopPrice { get; set; }

    /// <summary>
    /// Entry price when position was opened
    /// </summary>
    public decimal EntryPrice { get; set; }

    /// <summary>
    /// Current ATR value used for stop calculation
    /// </summary>
    public decimal CurrentAtr { get; set; }

    /// <summary>
    /// Highest price seen since entry (for trailing calculation)
    /// </summary>
    public decimal HighWaterMark { get; set; }

    /// <summary>
    /// Position quantity (positive for long positions)
    /// </summary>
    public decimal Quantity { get; set; }

    /// <summary>
    /// When this stop level was last updated
    /// </summary>
    public DateTime LastUpdated { get; set; }

    /// <summary>
    /// Date when position was opened
    /// </summary>
    public DateTime EntryDate { get; set; }

    /// <summary>
    /// Alpaca order ID for the current stop-loss order (if any)
    /// </summary>
    public string? OrderId { get; set; }

    /// <summary>
    /// Serialize to JSON for Redis storage
    /// </summary>
    public string ToJson()
    {
        return JsonSerializer.Serialize(this);
    }

    /// <summary>
    /// Deserialize from JSON stored in Redis
    /// </summary>
    public static RedisTrailingStop? FromJson(string json)
    {
        if (string.IsNullOrEmpty(json))
            return null;

        try
        {
            return JsonSerializer.Deserialize<RedisTrailingStop>(json);
        }
        catch
        {
            return null;
        }
    }

    /// <summary>
    /// Generate Redis key for this trailing stop
    /// </summary>
    public static string GetRedisKey(string symbol) => $"stop:{symbol}";
}

/// <summary>
/// Model for tracking daily trading signals to prevent duplicate trades.
/// Stored in Redis with daily expiration.
/// </summary>
public class RedisSignalFlag
{
    /// <summary>
    /// Stock symbol (e.g., "AAPL", "TSLA")
    /// </summary>
    public string Symbol { get; set; } = string.Empty;

    /// <summary>
    /// Trading date (YYYY-MM-DD format)
    /// </summary>
    public string TradingDate { get; set; } = string.Empty;

    /// <summary>
    /// Whether a signal was triggered for this symbol today
    /// </summary>
    public bool SignalTriggered { get; set; }

    /// <summary>
    /// When the signal was first triggered
    /// </summary>
    public DateTime? TriggeredAt { get; set; }

    /// <summary>
    /// Signal strength or score (optional)
    /// </summary>
    public decimal? SignalStrength { get; set; }

    /// <summary>
    /// Additional metadata about the signal
    /// </summary>
    public string? Metadata { get; set; }

    /// <summary>
    /// Serialize to JSON for Redis storage
    /// </summary>
    public string ToJson()
    {
        return JsonSerializer.Serialize(this);
    }

    /// <summary>
    /// Deserialize from JSON stored in Redis
    /// </summary>
    public static RedisSignalFlag? FromJson(string json)
    {
        if (string.IsNullOrEmpty(json))
            return null;

        try
        {
            return JsonSerializer.Deserialize<RedisSignalFlag>(json);
        }
        catch
        {
            return null;
        }
    }

    /// <summary>
    /// Generate Redis key for this signal flag
    /// </summary>
    public static string GetRedisKey(string symbol, DateTime date) => $"signal:{symbol}:{date:yyyyMMdd}";

    /// <summary>
    /// Generate Redis key for this signal flag using string date
    /// </summary>
    public static string GetRedisKey(string symbol, string dateString) => $"signal:{symbol}:{dateString}";
}

/// <summary>
/// Model for daily trading throttle/block flags.
/// Used to prevent trading specific symbols on certain days.
/// </summary>
public class RedisThrottleFlag
{
    /// <summary>
    /// Stock symbol (e.g., "AAPL", "TSLA")
    /// </summary>
    public string Symbol { get; set; } = string.Empty;

    /// <summary>
    /// Trading date (YYYY-MM-DD format)
    /// </summary>
    public string TradingDate { get; set; } = string.Empty;

    /// <summary>
    /// Whether trading is blocked for this symbol today
    /// </summary>
    public bool IsBlocked { get; set; }

    /// <summary>
    /// Reason for the block (e.g., "earnings", "volatility", "manual")
    /// </summary>
    public string? BlockReason { get; set; }

    /// <summary>
    /// When the block was set
    /// </summary>
    public DateTime BlockedAt { get; set; }

    /// <summary>
    /// Who/what set the block
    /// </summary>
    public string? BlockedBy { get; set; }

    /// <summary>
    /// Serialize to JSON for Redis storage
    /// </summary>
    public string ToJson()
    {
        return JsonSerializer.Serialize(this);
    }

    /// <summary>
    /// Deserialize from JSON stored in Redis
    /// </summary>
    public static RedisThrottleFlag? FromJson(string json)
    {
        if (string.IsNullOrEmpty(json))
            return null;

        try
        {
            return JsonSerializer.Deserialize<RedisThrottleFlag>(json);
        }
        catch
        {
            return null;
        }
    }

    /// <summary>
    /// Generate Redis key for this throttle flag
    /// </summary>
    public static string GetRedisKey(string symbol, DateTime date) => $"block:{symbol}:{date:yyyyMMdd}";

    /// <summary>
    /// Generate Redis key for this throttle flag using string date
    /// </summary>
    public static string GetRedisKey(string symbol, string dateString) => $"block:{symbol}:{dateString}";
}

/// <summary>
/// Configuration for Redis cache warming operations
/// </summary>
public class RedisWarmingConfig
{
    /// <summary>
    /// Essential symbols to always warm (e.g., SPY, QQQ, major positions)
    /// </summary>
    public string[] EssentialSymbols { get; set; } = Array.Empty<string>();

    /// <summary>
    /// Number of days of historical stop data to load
    /// </summary>
    public int HistoricalDays { get; set; } = 7;

    /// <summary>
    /// Whether to warm signal flags for today
    /// </summary>
    public bool WarmSignalFlags { get; set; } = true;

    /// <summary>
    /// Whether to warm throttle flags
    /// </summary>
    public bool WarmThrottleFlags { get; set; } = true;

    /// <summary>
    /// Default TTL for cached items (in hours)
    /// </summary>
    public int DefaultTtlHours { get; set; } = 24;

    /// <summary>
    /// Maximum number of symbols to process concurrently
    /// </summary>
    public int MaxConcurrency { get; set; } = 10;

    /// <summary>
    /// Default configuration for production use
    /// </summary>
    public static RedisWarmingConfig Default => new()
    {
        EssentialSymbols = new[] { "SPY", "QQQ", "IWM", "VIX", "TLT", "GLD", "AAPL", "MSFT", "GOOGL", "AMZN", "TSLA", "NVDA" },
        HistoricalDays = 7,
        WarmSignalFlags = true,
        WarmThrottleFlags = true,
        DefaultTtlHours = 24,
        MaxConcurrency = 10
    };
}

/// <summary>
/// Model for market regime data stored in Redis cache.
/// Used for fast retrieval during live trading to adapt strategy behavior.
/// </summary>
public class RedisMarketRegime
{
    /// <summary>
    /// Current market regime classification
    /// </summary>
    public MarketRegime Regime { get; set; }

    /// <summary>
    /// When this regime was detected
    /// </summary>
    public DateTime DetectedAt { get; set; }

    /// <summary>
    /// SPY 200-day SMA slope used in detection
    /// </summary>
    public decimal SmaSlope { get; set; }

    /// <summary>
    /// Average True Range (ATR) used in detection
    /// </summary>
    public decimal AverageAtr { get; set; }

    /// <summary>
    /// Return-to-drawdown ratio used in detection
    /// </summary>
    public decimal ReturnToDrawdownRatio { get; set; }

    /// <summary>
    /// Confidence score for the regime detection (0-1)
    /// </summary>
    public decimal Confidence { get; set; }

    /// <summary>
    /// Additional metadata about the detection
    /// </summary>
    public string? Metadata { get; set; }

    /// <summary>
    /// Serialize to JSON for Redis storage
    /// </summary>
    public string ToJson()
    {
        return JsonSerializer.Serialize(this);
    }

    /// <summary>
    /// Deserialize from JSON stored in Redis
    /// </summary>
    public static RedisMarketRegime? FromJson(string json)
    {
        if (string.IsNullOrEmpty(json))
            return null;

        try
        {
            return JsonSerializer.Deserialize<RedisMarketRegime>(json);
        }
        catch
        {
            return null;
        }
    }

    /// <summary>
    /// Generate Redis key for market regime data
    /// </summary>
    public static string GetRedisKey() => "regime:today";

    /// <summary>
    /// Generate Redis key for historical regime data
    /// </summary>
    public static string GetRedisKey(DateTime date) => $"regime:{date:yyyyMMdd}";
}

/// <summary>
/// Model for dynamic universe data stored in Redis cache.
/// Contains filtered symbols and metadata about the universe generation process.
/// </summary>
public class RedisUniverse
{
    /// <summary>
    /// List of qualified symbols that passed all filters
    /// </summary>
    public List<string> Symbols { get; set; } = new();

    /// <summary>
    /// When this universe was generated
    /// </summary>
    public DateTime GeneratedAt { get; set; }

    /// <summary>
    /// Number of candidate symbols that were evaluated
    /// </summary>
    public int CandidateCount { get; set; }

    /// <summary>
    /// Number of symbols that qualified after filtering
    /// </summary>
    public int QualifiedCount { get; set; }

    /// <summary>
    /// Filter criteria used for universe generation
    /// </summary>
    public UniverseFilterCriteria FilterCriteria { get; set; } = new();

    /// <summary>
    /// Performance metrics for the universe generation process
    /// </summary>
    public UniverseGenerationMetrics Metrics { get; set; } = new();

    /// <summary>
    /// Additional metadata about the universe generation
    /// </summary>
    public string? Metadata { get; set; }

    /// <summary>
    /// Serialize to JSON for Redis storage
    /// </summary>
    public string ToJson()
    {
        return JsonSerializer.Serialize(this);
    }

    /// <summary>
    /// Deserialize from JSON stored in Redis
    /// </summary>
    public static RedisUniverse? FromJson(string json)
    {
        if (string.IsNullOrEmpty(json))
            return null;

        try
        {
            return JsonSerializer.Deserialize<RedisUniverse>(json);
        }
        catch
        {
            return null;
        }
    }

    /// <summary>
    /// Generate Redis key for current universe data
    /// </summary>
    public static string GetRedisKey() => "universe:today";

    /// <summary>
    /// Generate Redis key for historical universe data
    /// </summary>
    public static string GetRedisKey(DateTime date) => $"universe:{date:yyyyMMdd}";
}

/// <summary>
/// Model for Polygon symbol data stored in Redis cache.
/// Contains full symbol list fetched from Polygon /v3/reference/tickers endpoint.
/// </summary>
public class RedisPolygonSymbolList
{
    /// <summary>
    /// List of all symbols from Polygon
    /// </summary>
    public List<PolygonSymbolInfo> Symbols { get; set; } = new();

    /// <summary>
    /// When this symbol list was fetched
    /// </summary>
    public DateTime FetchedAt { get; set; }

    /// <summary>
    /// Total number of symbols fetched
    /// </summary>
    public int TotalCount { get; set; }

    /// <summary>
    /// Number of API calls made to fetch all symbols
    /// </summary>
    public int ApiCallCount { get; set; }

    /// <summary>
    /// Time taken to fetch all symbols
    /// </summary>
    public TimeSpan FetchDuration { get; set; }

    /// <summary>
    /// Additional metadata about the fetch process
    /// </summary>
    public string? Metadata { get; set; }

    /// <summary>
    /// Serialize to JSON for Redis storage
    /// </summary>
    public string ToJson()
    {
        return JsonSerializer.Serialize(this);
    }

    /// <summary>
    /// Deserialize from JSON stored in Redis
    /// </summary>
    public static RedisPolygonSymbolList? FromJson(string json)
    {
        if (string.IsNullOrEmpty(json))
            return null;

        try
        {
            return JsonSerializer.Deserialize<RedisPolygonSymbolList>(json);
        }
        catch
        {
            return null;
        }
    }

    /// <summary>
    /// Generate Redis key for symbol list cache
    /// </summary>
    public static string GetRedisKey() => "polygon:symbols:full";

    /// <summary>
    /// Generate Redis key for weekly symbol list cache
    /// </summary>
    public static string GetRedisKey(DateTime weekStart) => $"polygon:symbols:{weekStart:yyyyMMdd}";
}

/// <summary>
/// Model for daily filtered universe candidates stored in Redis cache.
/// Contains top ~200 symbols filtered by volume, volatility, and price.
/// </summary>
public class RedisUniverseCandidates
{
    /// <summary>
    /// List of candidate symbols that passed daily filters
    /// </summary>
    public List<UniverseCandidate> Candidates { get; set; } = new();

    /// <summary>
    /// When this candidate list was generated
    /// </summary>
    public DateTime GeneratedAt { get; set; }

    /// <summary>
    /// Number of symbols evaluated for candidacy
    /// </summary>
    public int EvaluatedCount { get; set; }

    /// <summary>
    /// Number of symbols that qualified as candidates
    /// </summary>
    public int CandidateCount { get; set; }

    /// <summary>
    /// Filter criteria used for candidate selection
    /// </summary>
    public CandidateFilterCriteria FilterCriteria { get; set; } = new();

    /// <summary>
    /// Performance metrics for the candidate generation process
    /// </summary>
    public CandidateGenerationMetrics Metrics { get; set; } = new();

    /// <summary>
    /// Additional metadata about the candidate generation
    /// </summary>
    public string? Metadata { get; set; }

    /// <summary>
    /// Serialize to JSON for Redis storage
    /// </summary>
    public string ToJson()
    {
        return JsonSerializer.Serialize(this);
    }

    /// <summary>
    /// Deserialize from JSON stored in Redis
    /// </summary>
    public static RedisUniverseCandidates? FromJson(string json)
    {
        if (string.IsNullOrEmpty(json))
            return null;

        try
        {
            return JsonSerializer.Deserialize<RedisUniverseCandidates>(json);
        }
        catch
        {
            return null;
        }
    }

    /// <summary>
    /// Generate Redis key for current candidates
    /// </summary>
    public static string GetRedisKey() => "universe:candidates";

    /// <summary>
    /// Generate Redis key for historical candidates
    /// </summary>
    public static string GetRedisKey(DateTime date) => $"universe:candidates:{date:yyyyMMdd}";
}

/// <summary>
/// Filter criteria used for dynamic universe generation
/// </summary>
public class UniverseFilterCriteria
{
    /// <summary>
    /// Minimum stock price threshold
    /// </summary>
    public decimal MinPrice { get; set; } = 10.0m;

    /// <summary>
    /// Minimum average daily volume threshold
    /// </summary>
    public long MinAverageVolume { get; set; } = 1_000_000;

    /// <summary>
    /// Minimum daily volatility threshold (as percentage)
    /// </summary>
    public decimal MinVolatilityPercent { get; set; } = 2.0m;

    /// <summary>
    /// Number of days to analyze for volume and volatility calculations
    /// </summary>
    public int AnalysisPeriodDays { get; set; } = 20;

    /// <summary>
    /// Maximum number of symbols to include in universe
    /// </summary>
    public int? MaxSymbols { get; set; }

    /// <summary>
    /// Additional custom filters applied
    /// </summary>
    public List<string> CustomFilters { get; set; } = new();
}

/// <summary>
/// Model for VIX data stored in Redis cache.
/// Used for fast retrieval during live trading to reduce API calls.
/// </summary>
public class RedisVixData
{
    /// <summary>
    /// Current VIX value
    /// </summary>
    public decimal Value { get; set; }

    /// <summary>
    /// Data source that provided this VIX value
    /// </summary>
    public string DataSource { get; set; } = string.Empty;

    /// <summary>
    /// When this VIX value was retrieved
    /// </summary>
    public DateTime RetrievedAt { get; set; }

    /// <summary>
    /// When this VIX value was last updated by the source
    /// </summary>
    public DateTime? SourceTimestamp { get; set; }

    /// <summary>
    /// Quality score of the data (0-1, where 1 is highest quality)
    /// </summary>
    public decimal QualityScore { get; set; }

    /// <summary>
    /// Whether this is a synthetic/calculated VIX value
    /// </summary>
    public bool IsSynthetic { get; set; }

    /// <summary>
    /// Additional metadata about the VIX retrieval
    /// </summary>
    public string? Metadata { get; set; }

    /// <summary>
    /// Number of API calls made to retrieve this value
    /// </summary>
    public int ApiCallsUsed { get; set; }

    /// <summary>
    /// Serialize to JSON for Redis storage
    /// </summary>
    public string ToJson()
    {
        return JsonSerializer.Serialize(this);
    }

    /// <summary>
    /// Deserialize from JSON stored in Redis
    /// </summary>
    public static RedisVixData? FromJson(string json)
    {
        if (string.IsNullOrEmpty(json))
            return null;

        try
        {
            return JsonSerializer.Deserialize<RedisVixData>(json);
        }
        catch
        {
            return null;
        }
    }

    /// <summary>
    /// Generate Redis key for current VIX data
    /// </summary>
    public static string GetRedisKey() => "vix:current";

    /// <summary>
    /// Generate Redis key for historical VIX data
    /// </summary>
    public static string GetRedisKey(DateTime date) => $"vix:{date:yyyyMMdd}";

    /// <summary>
    /// Generate Redis key for VIX data by source
    /// </summary>
    public static string GetRedisKey(string source) => $"vix:source:{source}";
}

/// <summary>
/// Performance metrics for universe generation process
/// </summary>
public class UniverseGenerationMetrics
{
    /// <summary>
    /// Total time taken to generate the universe
    /// </summary>
    public TimeSpan GenerationTime { get; set; }

    /// <summary>
    /// Number of API calls made during generation
    /// </summary>
    public int ApiCallCount { get; set; }

    /// <summary>
    /// Number of symbols that failed processing due to errors
    /// </summary>
    public int ErrorCount { get; set; }

    /// <summary>
    /// Breakdown of filter results
    /// </summary>
    public Dictionary<string, int> FilterBreakdown { get; set; } = new();

    /// <summary>
    /// Average processing time per symbol
    /// </summary>
    public TimeSpan AverageProcessingTime { get; set; }
}

/// <summary>
/// Information about a symbol from Polygon API
/// </summary>
public class PolygonSymbolInfo
{
    /// <summary>
    /// Symbol ticker (e.g., "AAPL")
    /// </summary>
    public string Ticker { get; set; } = string.Empty;

    /// <summary>
    /// Company name
    /// </summary>
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// Market type (e.g., "stocks", "crypto", "fx")
    /// </summary>
    public string Market { get; set; } = string.Empty;

    /// <summary>
    /// Locale (e.g., "us", "global")
    /// </summary>
    public string Locale { get; set; } = string.Empty;

    /// <summary>
    /// Primary exchange
    /// </summary>
    public string PrimaryExchange { get; set; } = string.Empty;

    /// <summary>
    /// Symbol type (e.g., "CS" for common stock)
    /// </summary>
    public string Type { get; set; } = string.Empty;

    /// <summary>
    /// Whether the symbol is actively traded
    /// </summary>
    public bool Active { get; set; }

    /// <summary>
    /// Currency code
    /// </summary>
    public string CurrencyName { get; set; } = string.Empty;

    /// <summary>
    /// Composite FIGI identifier
    /// </summary>
    public string? Cik { get; set; }

    /// <summary>
    /// Share class FIGI identifier
    /// </summary>
    public string? CompositeFigi { get; set; }

    /// <summary>
    /// Share class FIGI identifier
    /// </summary>
    public string? ShareClassFigi { get; set; }

    /// <summary>
    /// Last updated timestamp
    /// </summary>
    public DateTime? LastUpdatedUtc { get; set; }
}

/// <summary>
/// Universe candidate with ranking metrics
/// </summary>
public class UniverseCandidate
{
    /// <summary>
    /// Symbol ticker
    /// </summary>
    public string Symbol { get; set; } = string.Empty;

    /// <summary>
    /// Current price
    /// </summary>
    public decimal Price { get; set; }

    /// <summary>
    /// Average daily volume (20-day)
    /// </summary>
    public long AverageVolume { get; set; }

    /// <summary>
    /// Realized volatility percentage (20-day)
    /// </summary>
    public decimal VolatilityPercent { get; set; }

    /// <summary>
    /// Market capitalization
    /// </summary>
    public decimal? MarketCap { get; set; }

    /// <summary>
    /// Ranking score (higher is better)
    /// </summary>
    public decimal RankingScore { get; set; }

    /// <summary>
    /// When this candidate data was last updated
    /// </summary>
    public DateTime LastUpdated { get; set; }

    /// <summary>
    /// Additional metadata
    /// </summary>
    public Dictionary<string, object> Metadata { get; set; } = new();
}

/// <summary>
/// Filter criteria for candidate selection
/// </summary>
public class CandidateFilterCriteria
{
    /// <summary>
    /// Minimum price threshold
    /// </summary>
    public decimal MinPrice { get; set; } = 10.0m;

    /// <summary>
    /// Minimum average daily volume
    /// </summary>
    public long MinAverageVolume { get; set; } = 1_000_000;

    /// <summary>
    /// Minimum volatility percentage
    /// </summary>
    public decimal MinVolatilityPercent { get; set; } = 2.0m;

    /// <summary>
    /// Maximum number of candidates to select
    /// </summary>
    public int MaxCandidates { get; set; } = 200;

    /// <summary>
    /// Analysis period in days for volume and volatility calculations
    /// </summary>
    public int AnalysisPeriodDays { get; set; } = 20;

    /// <summary>
    /// Minimum market cap (optional)
    /// </summary>
    public decimal? MinMarketCap { get; set; }

    /// <summary>
    /// Excluded exchanges (optional)
    /// </summary>
    public List<string> ExcludedExchanges { get; set; } = new();

    /// <summary>
    /// Excluded symbol types (optional)
    /// </summary>
    public List<string> ExcludedTypes { get; set; } = new();
}

/// <summary>
/// Performance metrics for candidate generation process
/// </summary>
public class CandidateGenerationMetrics
{
    /// <summary>
    /// Total time taken to generate candidates
    /// </summary>
    public TimeSpan GenerationTime { get; set; }

    /// <summary>
    /// Number of API calls made during generation
    /// </summary>
    public int ApiCallCount { get; set; }

    /// <summary>
    /// Number of symbols that failed processing due to errors
    /// </summary>
    public int ErrorCount { get; set; }

    /// <summary>
    /// Breakdown of filter results
    /// </summary>
    public Dictionary<string, int> FilterBreakdown { get; set; } = new();

    /// <summary>
    /// Average processing time per symbol
    /// </summary>
    public TimeSpan AverageProcessingTime { get; set; }

    /// <summary>
    /// Data freshness metrics
    /// </summary>
    public Dictionary<string, DateTime> DataFreshness { get; set; } = new();
}
