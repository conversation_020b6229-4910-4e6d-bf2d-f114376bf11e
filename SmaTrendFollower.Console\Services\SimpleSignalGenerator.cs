using SmaTrendFollower.Models;
using SmaTrendFollower.Console.Extensions;
using SmaTrendFollower.Monitoring;
using Microsoft.Extensions.Logging;
using System.Diagnostics;

namespace SmaTrendFollower.Services;

/// <summary>
/// Simple signal generator for testing purposes.
/// Implements basic SMA momentum strategy without complex dependencies.
/// </summary>
public sealed class SimpleSignalGenerator : ISignalGenerator
{
    private readonly IMarketDataService _marketDataService;
    private readonly IUniverseProvider _universeProvider;
    private readonly ILogger<SimpleSignalGenerator> _logger;
    private readonly AnomalyDetectorService? _anomalyDetector;

    public SimpleSignalGenerator(
        IMarketDataService marketDataService,
        IUniverseProvider universeProvider,
        ILogger<SimpleSignalGenerator> logger,
        AnomalyDetectorService? anomalyDetector = null)
    {
        _marketDataService = marketDataService;
        _universeProvider = universeProvider;
        _logger = logger;
        _anomalyDetector = anomalyDetector; // Optional dependency
    }

    public async Task<IEnumerable<TradingSignal>> RunAsync(int topN = 10)
    {
        var sw = Stopwatch.StartNew();

        try
        {
            _logger.LogInformation("Starting simple signal generation for top {TopN} signals", topN);

            // Get universe of symbols
            var symbols = await _universeProvider.GetSymbolsAsync();
            var symbolList = symbols.ToList();

            if (symbolList.Count == 0)
            {
                _logger.LogWarning("No symbols available from universe provider");
                MetricsRegistry.SignalsTotal.WithLabels("simple", "false").Inc();
                return Enumerable.Empty<TradingSignal>();
            }

            // Update universe size metric
            MetricsRegistry.CurrentUniverseSize.Set(symbolList.Count);

            _logger.LogInformation("Screening {Count} symbols for trading signals", symbolList.Count);

            var signals = new List<TradingSignal>();

            // Process each symbol
            foreach (var symbol in symbolList)
            {
                try
                {
                    // Check if trading is halted for this symbol
                    if (_anomalyDetector != null && await _anomalyDetector.IsTradingHaltedAsync(symbol))
                    {
                        _logger.LogDebug("Skipping signal generation for {Symbol} - trading halted due to anomaly", symbol);
                        continue;
                    }

                    var signal = await ProcessSymbol(symbol);
                    if (signal != null)
                    {
                        signals.Add(signal.Value);
                        // Record signal generated
                        MetricsRegistry.SignalsTotal.WithLabels("simple", "true").Inc();
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogWarning(ex, "Failed to process symbol {Symbol}", symbol);
                    MetricsRegistry.ApplicationErrors.WithLabels("SimpleSignalGenerator", "ProcessSymbol").Inc();
                }
            }

            // Filter and rank signals
            var validSignals = signals
                .Where(s => s.Price > 0 && s.Atr > 0)
                .Where(s => s.Atr / s.Price < 0.03m) // ATR/Price < 3% (volatility throttle)
                .Where(s => !string.IsNullOrEmpty(s.Symbol))
                .OrderByDescending(s => s.SixMonthReturn)
                .Take(topN)
                .ToList();

            _logger.LogInformation("Generated {Count} trading signals from {Total} symbols",
                validSignals.Count, symbolList.Count);

            // Record signal generation latency
            MetricsRegistry.SignalLatencyMs.Observe(sw.Elapsed.TotalMilliseconds);

            return validSignals;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in signal generation");
            MetricsRegistry.ApplicationErrors.WithLabels("SimpleSignalGenerator", "RunAsync").Inc();
            return Enumerable.Empty<TradingSignal>();
        }
        finally
        {
            sw.Stop();
        }
    }

    private async Task<TradingSignal?> ProcessSymbol(string symbol)
    {
        // Get 250 days of data for technical analysis
        var startDate = DateTime.UtcNow.AddDays(-300);
        var endDate = DateTime.UtcNow;

        var response = await _marketDataService.GetStockBarsAsync(symbol, startDate, endDate);
        var bars = response.Items.ToList();

        if (bars.Count < 200) // Need enough data for SMA200
        {
            _logger.LogDebug("Insufficient data for {Symbol}: {Count} bars", symbol, bars.Count);
            return null;
        }

        var currentPrice = bars.Last().Close;
        var sma50 = (decimal)bars.GetSma50();
        var sma200 = (decimal)bars.GetSma200();
        var atr14 = (decimal)bars.GetAtr14();
        var sixMonthReturn = (decimal)bars.GetTotalReturn(126); // ~6 months

        // Validate calculated values
        if (currentPrice <= 0 || sma50 <= 0 || sma200 <= 0 || atr14 <= 0)
        {
            _logger.LogDebug("Invalid calculated values for {Symbol}", symbol);
            return null;
        }

        // Filter: close > sma50 && close > sma200 (uptrend filter)
        if (currentPrice > sma50 && currentPrice > sma200)
        {
            return new TradingSignal(symbol, currentPrice, atr14, sixMonthReturn);
        }

        return null; // Signal doesn't meet criteria
    }
}
