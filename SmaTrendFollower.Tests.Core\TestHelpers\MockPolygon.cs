using RichardSzalay.MockHttp;
using System.Net;
using System.Text.Json;

namespace SmaTrendFollower.Tests.Core.TestHelpers;

/// <summary>
/// Helper class for mocking Polygon.io API endpoints in tests.
/// Wraps MockHttp to provide convenient methods for stubbing common Polygon responses.
/// </summary>
public class MockPolygon : IDisposable
{
    private readonly MockHttpMessageHandler _mockHttp;
    public HttpClient HttpClient { get; }

    public MockPolygon()
    {
        _mockHttp = new MockHttpMessageHandler();
        HttpClient = _mockHttp.ToHttpClient();
        HttpClient.BaseAddress = new Uri("https://api.polygon.io/");
    }

    /// <summary>
    /// Mocks the /v2/aggs/ticker/{symbol}/range endpoint for stock bars
    /// </summary>
    /// <param name="symbol">Stock symbol</param>
    /// <param name="bars">Array of bar data to return</param>
    /// <param name="statusCode">HTTP status code to return</param>
    public MockedRequest MockStockBars(string symbol, object[] bars, HttpStatusCode statusCode = HttpStatusCode.OK)
    {
        var response = new
        {
            ticker = symbol,
            status = "OK",
            request_id = Guid.NewGuid().ToString(),
            count = bars.Length,
            results = bars
        };

        return _mockHttp
            .When($"https://api.polygon.io/v2/aggs/ticker/{symbol}/range/*")
            .Respond(statusCode, "application/json", JsonSerializer.Serialize(response));
    }

    /// <summary>
    /// Mocks the /v2/last/trade/{symbol} endpoint for last trade data
    /// </summary>
    /// <param name="symbol">Stock symbol</param>
    /// <param name="price">Last trade price</param>
    /// <param name="timestamp">Trade timestamp</param>
    /// <param name="statusCode">HTTP status code to return</param>
    public MockedRequest MockLastTrade(string symbol, decimal price, long timestamp, HttpStatusCode statusCode = HttpStatusCode.OK)
    {
        var response = new
        {
            status = "OK",
            request_id = Guid.NewGuid().ToString(),
            results = new
            {
                T = symbol,
                p = price,
                t = timestamp,
                s = 100,
                x = 4,
                c = new int[] { },
                f = timestamp,
                q = 1234567890
            }
        };

        return _mockHttp
            .When($"https://api.polygon.io/v2/last/trade/{symbol}")
            .Respond(statusCode, "application/json", JsonSerializer.Serialize(response));
    }

    /// <summary>
    /// Mocks the /v3/reference/tickers endpoint for universe data
    /// </summary>
    /// <param name="tickers">Array of ticker objects</param>
    /// <param name="statusCode">HTTP status code to return</param>
    public MockedRequest MockTickers(object[] tickers, HttpStatusCode statusCode = HttpStatusCode.OK)
    {
        var response = new
        {
            status = "OK",
            request_id = Guid.NewGuid().ToString(),
            count = tickers.Length,
            results = tickers,
            next_url = (string?)null
        };

        return _mockHttp
            .When("https://api.polygon.io/v3/reference/tickers*")
            .Respond(statusCode, "application/json", JsonSerializer.Serialize(response));
    }

    /// <summary>
    /// Mocks the /v2/snapshot/locale/us/markets/stocks/tickers endpoint
    /// </summary>
    /// <param name="snapshots">Array of snapshot data</param>
    /// <param name="statusCode">HTTP status code to return</param>
    public MockedRequest MockSnapshots(object[] snapshots, HttpStatusCode statusCode = HttpStatusCode.OK)
    {
        var response = new
        {
            status = "OK",
            request_id = Guid.NewGuid().ToString(),
            count = snapshots.Length,
            results = snapshots
        };

        return _mockHttp
            .When("https://api.polygon.io/v2/snapshot/locale/us/markets/stocks/tickers*")
            .Respond(statusCode, "application/json", JsonSerializer.Serialize(response));
    }

    /// <summary>
    /// Mocks rate limit responses (429 status code)
    /// </summary>
    /// <param name="endpoint">Endpoint pattern to mock</param>
    public MockedRequest MockRateLimit(string endpoint)
    {
        return _mockHttp
            .When(endpoint)
            .Respond(HttpStatusCode.TooManyRequests, "application/json", 
                JsonSerializer.Serialize(new { error = "Rate limit exceeded" }));
    }

    /// <summary>
    /// Mocks server error responses (500 status code)
    /// </summary>
    /// <param name="endpoint">Endpoint pattern to mock</param>
    public MockedRequest MockServerError(string endpoint)
    {
        return _mockHttp
            .When(endpoint)
            .Respond(HttpStatusCode.InternalServerError, "application/json",
                JsonSerializer.Serialize(new { error = "Internal server error" }));
    }

    /// <summary>
    /// Mocks authentication error responses (401 status code)
    /// </summary>
    /// <param name="endpoint">Endpoint pattern to mock</param>
    public MockedRequest MockAuthError(string endpoint)
    {
        return _mockHttp
            .When(endpoint)
            .Respond(HttpStatusCode.Unauthorized, "application/json",
                JsonSerializer.Serialize(new { error = "Unauthorized" }));
    }

    /// <summary>
    /// Creates a sample stock bar object for testing
    /// </summary>
    /// <param name="timestamp">Bar timestamp</param>
    /// <param name="open">Open price</param>
    /// <param name="high">High price</param>
    /// <param name="low">Low price</param>
    /// <param name="close">Close price</param>
    /// <param name="volume">Volume</param>
    /// <returns>Bar object</returns>
    public static object CreateSampleBar(long timestamp, decimal open, decimal high, decimal low, decimal close, long volume)
    {
        return new
        {
            t = timestamp,
            o = open,
            h = high,
            l = low,
            c = close,
            v = volume,
            vw = (open + high + low + close) / 4, // Volume weighted average price
            n = 1000 // Number of transactions
        };
    }

    /// <summary>
    /// Creates a sample ticker object for testing
    /// </summary>
    /// <param name="symbol">Stock symbol</param>
    /// <param name="name">Company name</param>
    /// <param name="market">Market (e.g., "stocks")</param>
    /// <param name="active">Whether ticker is active</param>
    /// <returns>Ticker object</returns>
    public static object CreateSampleTicker(string symbol, string name, string market = "stocks", bool active = true)
    {
        return new
        {
            ticker = symbol,
            name = name,
            market = market,
            locale = "us",
            primary_exchange = "XNAS",
            type = "CS",
            active = active,
            currency_name = "usd",
            cik = "0001234567",
            composite_figi = "BBG000000000",
            share_class_figi = "BBG000000001",
            last_updated_utc = DateTime.UtcNow.ToString("yyyy-MM-ddTHH:mm:ssZ")
        };
    }

    /// <summary>
    /// Verifies that no unexpected HTTP requests were made
    /// </summary>
    public void VerifyNoOutstandingExpectation()
    {
        _mockHttp.VerifyNoOutstandingExpectation();
    }

    /// <summary>
    /// Resets all configured mocks
    /// </summary>
    public void Reset()
    {
        _mockHttp.Clear();
    }

    public void Dispose()
    {
        HttpClient?.Dispose();
        _mockHttp?.Dispose();
    }
}
