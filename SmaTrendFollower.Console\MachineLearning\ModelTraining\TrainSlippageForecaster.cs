using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Microsoft.ML;
using Microsoft.ML.Data;
using Microsoft.ML.Trainers.LightGbm;
using SmaTrendFollower.Configuration;
using SmaTrendFollower.Data;
using SmaTrendFollower.MachineLearning.DataPrep;
using SmaTrendFollower.Models;
using System.Globalization;

namespace SmaTrendFollower.MachineLearning.ModelTraining;

/// <summary>
/// Console application for training LightGBM slippage forecasting model.
/// Trains on historical fill data to predict execution slippage in basis points.
/// </summary>
public class TrainSlippageForecaster
{
    // Commented out to avoid multiple entry point warnings
    /*
    public static async Task<int> Main(string[] args)
    {
        System.Console.WriteLine("=== SmaTrendFollower Slippage Forecaster Training ===");
        System.Console.WriteLine($"Started at: {DateTime.UtcNow:yyyy-MM-dd HH:mm:ss} UTC");

        try
        {
            // Build host with DI container
            var host = CreateHostBuilder(args).Build();
            
            using var scope = host.Services.CreateScope();
            var trainer = scope.ServiceProvider.GetRequiredService<SlippageForecasterTrainer>();
            
            // Train the model
            var success = await trainer.TrainModelAsync();
            
            System.Console.WriteLine($"Training completed at: {DateTime.UtcNow:yyyy-MM-dd HH:mm:ss} UTC");
            return success ? 0 : 1;
        }
        catch (Exception ex)
        {
            System.Console.WriteLine($"Training failed: {ex.Message}");
            System.Console.WriteLine($"Stack trace: {ex.StackTrace}");
            return 1;
        }
    }
    */

    private static IHostBuilder CreateHostBuilder(string[] args) =>
        Host.CreateDefaultBuilder(args)
            .ConfigureAppConfiguration((context, config) =>
            {
                config.AddJsonFile("appsettings.json", optional: false)
                      .AddJsonFile($"appsettings.{context.HostingEnvironment.EnvironmentName}.json", optional: true)
                      .AddEnvironmentVariables()
                      .AddCommandLine(args);
            })
            .ConfigureServices((context, services) =>
            {
                // Add core infrastructure
                services.AddCoreInfrastructure();
                services.AddDataServices();
                services.AddMachineLearningServices();
                
                // Add trainer service
                services.AddScoped<SlippageForecasterTrainer>();
            })
            .ConfigureLogging(logging =>
            {
                logging.ClearProviders();
                logging.AddConsole();
                logging.SetMinimumLevel(LogLevel.Information);
            });
}

/// <summary>
/// Service for training slippage forecasting models using LightGBM
/// </summary>
public class SlippageForecasterTrainer
{
    private readonly IFeatureExportService _featureExportService;
    private readonly ILogger<SlippageForecasterTrainer> _logger;
    private readonly IConfiguration _configuration;
    private readonly MLContext _mlContext;

    public SlippageForecasterTrainer(
        IFeatureExportService featureExportService,
        ILogger<SlippageForecasterTrainer> logger,
        IConfiguration configuration)
    {
        _featureExportService = featureExportService;
        _logger = logger;
        _configuration = configuration;
        _mlContext = new MLContext(seed: 42);
    }

    public async Task<bool> TrainModelAsync()
    {
        try
        {
            var config = GetTrainingConfig();
            _logger.LogInformation("Starting slippage forecaster training with config: {@Config}", config);

            // Step 1: Export training data
            var csvPath = Path.Combine("Model", "slippage.csv");
            var fromDate = DateTime.UtcNow.AddDays(-config.TrainingDataDays);
            var toDate = DateTime.UtcNow.AddDays(-1); // Exclude today for complete data

            _logger.LogInformation("Exporting slippage data from {FromDate:yyyy-MM-dd} to {ToDate:yyyy-MM-dd}...", 
                fromDate, toDate);

            await _featureExportService.ExportSlippageCsvAsync(csvPath, fromDate, toDate);

            if (!File.Exists(csvPath))
            {
                _logger.LogError("Slippage data export failed - CSV file not found: {Path}", csvPath);
                return false;
            }

            // Step 2: Load and validate data
            var data = _mlContext.Data.LoadFromTextFile<SlippageRow>(csvPath, hasHeader: true, separatorChar: ',');
            var dataView = _mlContext.Data.CreateEnumerable<SlippageRow>(data, reuseRowObject: false).ToList();

            if (!dataView.Any())
            {
                _logger.LogError("No training data found in CSV file");
                return false;
            }

            _logger.LogInformation("Loaded {Count} training samples", dataView.Count);

            // Step 3: Split data
            var trainTestSplit = _mlContext.Data.TrainTestSplit(data, testFraction: 1.0f - config.TrainTestSplit);
            var trainData = trainTestSplit.TrainSet;
            var testData = trainTestSplit.TestSet;

            // Step 4: Build training pipeline
            var pipeline = _mlContext.Transforms.Concatenate("Features", 
                    nameof(SlippageRow.SpreadPct),
                    nameof(SlippageRow.RankProb),
                    nameof(SlippageRow.ATR_Pct),
                    nameof(SlippageRow.VolumePct10d),
                    nameof(SlippageRow.Regime),
                    nameof(SlippageRow.Side),
                    nameof(SlippageRow.Hour))
                .Append(_mlContext.Regression.Trainers.LightGbm(
                    labelColumnName: nameof(SlippageRow.Label),
                    featureColumnName: "Features",
                    numberOfLeaves: config.NumberOfLeaves,
                    numberOfIterations: config.NumberOfTrees,
                    minimumExampleCountPerLeaf: 10,
                    learningRate: 0.1));

            // Step 5: Train model
            _logger.LogInformation("Training LightGBM model with {Trees} trees and {Leaves} leaves...", 
                config.NumberOfTrees, config.NumberOfLeaves);

            var stopwatch = System.Diagnostics.Stopwatch.StartNew();
            var model = pipeline.Fit(trainData);
            stopwatch.Stop();

            _logger.LogInformation("Model training completed in {Duration:F2} seconds", stopwatch.Elapsed.TotalSeconds);

            // Step 6: Evaluate model
            var predictions = model.Transform(testData);
            var metrics = _mlContext.Regression.Evaluate(predictions, labelColumnName: nameof(SlippageRow.Label));

            _logger.LogInformation("Model evaluation metrics:");
            _logger.LogInformation("  Mean Absolute Error: {MAE:F4} bps", metrics.MeanAbsoluteError);
            _logger.LogInformation("  Root Mean Squared Error: {RMSE:F4} bps", metrics.RootMeanSquaredError);
            _logger.LogInformation("  R-Squared: {RSquared:F4}", metrics.RSquared);

            // Step 7: Save model
            var modelPath = config.ModelOutputPath;
            var modelDirectory = Path.GetDirectoryName(modelPath);
            if (!string.IsNullOrEmpty(modelDirectory) && !Directory.Exists(modelDirectory))
            {
                Directory.CreateDirectory(modelDirectory);
            }

            _mlContext.Model.Save(model, trainData.Schema, modelPath);
            _logger.LogInformation("Model saved to: {Path}", modelPath);

            // Step 8: Save training metadata
            var metadataPath = Path.ChangeExtension(modelPath, ".json");
            var metadata = new SlippageModelMetadata
            {
                TrainingDate = DateTime.UtcNow,
                TrainingSamples = dataView.Count,
                MeanAbsoluteError = (float)metrics.MeanAbsoluteError,
                RootMeanSquaredError = (float)metrics.RootMeanSquaredError,
                RSquared = (float)metrics.RSquared,
                NumberOfTrees = config.NumberOfTrees,
                NumberOfLeaves = config.NumberOfLeaves,
                TrainingDataDays = config.TrainingDataDays
            };

            await File.WriteAllTextAsync(metadataPath, System.Text.Json.JsonSerializer.Serialize(metadata, new System.Text.Json.JsonSerializerOptions { WriteIndented = true }));
            _logger.LogInformation("Training metadata saved to: {Path}", metadataPath);

            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during slippage forecaster training");
            return false;
        }
    }

    private SlippageTrainingConfig GetTrainingConfig()
    {
        var section = _configuration.GetSection("SlippageTraining");
        return new SlippageTrainingConfig(
            MaxExperimentTimeSeconds: section.GetValue("MaxExperimentTimeSeconds", 300),
            TrainTestSplit: section.GetValue("TrainTestSplit", 0.8f),
            ModelOutputPath: section.GetValue("ModelOutputPath", "Model/slippage_model.zip"),
            NumberOfTrees: section.GetValue("NumberOfTrees", 200),
            NumberOfLeaves: section.GetValue("NumberOfLeaves", 32),
            TrainingDataDays: section.GetValue("TrainingDataDays", 60)
        );
    }
}

/// <summary>
/// Data row for slippage model training
/// </summary>
public class SlippageRow
{
    [LoadColumn(0)] public float SpreadPct { get; set; }
    [LoadColumn(1)] public float RankProb { get; set; }
    [LoadColumn(2)] public float ATR_Pct { get; set; }
    [LoadColumn(3)] public float VolumePct10d { get; set; }
    [LoadColumn(4)] public float Regime { get; set; }
    [LoadColumn(5)] public uint Side { get; set; }
    [LoadColumn(6)] public int Hour { get; set; }
    [LoadColumn(7)] public float Label { get; set; } // Slippage in basis points
}

/// <summary>
/// Metadata about trained slippage model
/// </summary>
public record SlippageModelMetadata
{
    public DateTime TrainingDate { get; init; }
    public int TrainingSamples { get; init; }
    public float MeanAbsoluteError { get; init; }
    public float RootMeanSquaredError { get; init; }
    public float RSquared { get; init; }
    public int NumberOfTrees { get; init; }
    public int NumberOfLeaves { get; init; }
    public int TrainingDataDays { get; init; }
}
