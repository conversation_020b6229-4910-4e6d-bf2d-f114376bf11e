using Serilog;
using Serilog.Events;
using SmaTrendFollower.Infrastructure;
using SmaTrendFollower.Monitoring;

namespace SmaTrendFollower.Console;

/// <summary>
/// Test program to verify the new DiscordBotSink functionality
/// </summary>
public static class TestDiscordBotSink
{
    public static async Task RunAsync()
    {
        // Get Discord configuration from environment variables
        var botToken = Environment.GetEnvironmentVariable("DISCORD_BOT_TOKEN");
        var channelId = Environment.GetEnvironmentVariable("DISCORD_CHANNEL_ID");

        System.Console.WriteLine("🤖 Testing Discord Bot-Token Alert Sink");
        System.Console.WriteLine("═══════════════════════════════════════");

        if (string.IsNullOrEmpty(botToken) || string.IsNullOrEmpty(channelId))
        {
            System.Console.WriteLine("❌ Discord not configured - missing DISCORD_BOT_TOKEN or DISCORD_CHANNEL_ID");
            System.Console.WriteLine("Please set these environment variables in your .env file:");
            System.Console.WriteLine("  DISCORD_BOT_TOKEN=your_bot_token_here");
            System.Console.WriteLine("  DISCORD_CHANNEL_ID=your_channel_id_here");
            return;
        }

        System.Console.WriteLine($"✅ Bot Token: {botToken.Substring(0, 20)}...");
        System.Console.WriteLine($"✅ Channel ID: {channelId}");

        // Configure Serilog with the new DiscordBotSink
        var loggerConfig = new LoggerConfiguration()
            .MinimumLevel.Information()
            .WriteTo.Console()
            .WriteTo.File("logs/discord-bot-sink-test-.log",
                          rollingInterval: RollingInterval.Day,
                          retainedFileCountLimit: 7);

        // Add the new DiscordBotSink directly
        loggerConfig.WriteTo.Sink(new DiscordBotSink(botToken, channelId));

        Log.Logger = loggerConfig.CreateLogger();

        try
        {
            System.Console.WriteLine("\n🧪 Starting Discord Bot Sink Test...");
            
            Log.Information("🧪 Testing Discord bot sink - this should NOT appear in Discord (Info level)");
            await Task.Delay(1000);

            Log.Warning("⚠️ Testing Discord bot sink - WARNING level message");
            await Task.Delay(2000);

            Log.Error("❌ Testing Discord bot sink - ERROR level message");
            await Task.Delay(2000);

            // Test with trading-like messages
            Log.Warning("🟢 **BUY** AAPL | Quantity: 100 | Price: $150.25");
            await Task.Delay(2000);

            Log.Warning("QuoteVolatilityGuard: NVDA halted 2 min (z=2.4)");
            await Task.Delay(2000);

            Log.Error("AnomalyDetector: Spread anomaly detected for TSLA (z=3.2)");
            await Task.Delay(2000);

            System.Console.WriteLine("\n✅ Test completed! Check your Discord channel for messages.");
            System.Console.WriteLine("Expected messages:");
            System.Console.WriteLine("  - WARNING: Testing Discord bot sink - WARNING level message");
            System.Console.WriteLine("  - ERROR: Testing Discord bot sink - ERROR level message");
            System.Console.WriteLine("  - WARNING: BUY AAPL message");
            System.Console.WriteLine("  - WARNING: QuoteVolatilityGuard message");
            System.Console.WriteLine("  - ERROR: AnomalyDetector message");
            System.Console.WriteLine("\nThe INFO level message should NOT appear in Discord.");

            System.Console.WriteLine("\n📊 Metrics are being tracked by Prometheus MetricsRegistry:");
            System.Console.WriteLine("  - discord_messages_total{level} - Counter of Discord messages by log level");
            System.Console.WriteLine("  - discord_sink_errors_total - Counter of Discord sink errors");
        }
        catch (Exception ex)
        {
            System.Console.WriteLine($"\n❌ Test failed: {ex.Message}");
            Log.Error(ex, "Discord bot sink test failed");
        }
        finally
        {
            Log.CloseAndFlush();
        }
    }
}
