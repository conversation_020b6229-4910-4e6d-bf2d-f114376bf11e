using FluentAssertions;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using SmaTrendFollower.Configuration;
using SmaTrendFollower.Models;
using SmaTrendFollower.Services;
using Xunit;
using SmaTrendFollower.Tests.TestHelpers;

namespace SmaTrendFollower.Tests.Integration;

/// <summary>
/// Integration tests for Phase 6 services
/// Tests the interaction between VWAPMonitorService, TickVolatilityGuard, 
/// RealTimeBreakoutSignal, and MicrostructurePatternDetector
/// </summary>
public class Phase6ServicesIntegrationTests : IDisposable
{
    private readonly ServiceProvider _serviceProvider;
    private readonly IVWAPMonitorService _vwapMonitor;
    private readonly ITickVolatilityGuard _volatilityGuard;
    private readonly IRealTimeBreakoutSignal _breakoutSignal;
    private readonly IMicrostructurePatternDetector _microstructureDetector;

    public Phase6ServicesIntegrationTests()
    {
        var services = new ServiceCollection();
        
        // Add configuration
        var configuration = new ConfigurationBuilder()
            .AddInMemoryCollection(new Dictionary<string, string?>
            {
                ["Alpaca:ApiKey"] = "test-key",
                ["Alpaca:SecretKey"] = "test-secret",
                ["Alpaca:BaseUrl"] = "https://paper-api.alpaca.markets",
                ["Polygon:ApiKey"] = "test-polygon-key",
                ["Redis:ConnectionString"] = "localhost:6379",
                ["VWAPMonitor:RollingMinutes"] = "30",
                ["VWAPMonitor:MinTradesRequired"] = "10",
                ["VWAPMonitor:RequireTrendingRegime"] = "true",
                ["VolatilityGuard:VolatilityWindowMinutes"] = "5",
                ["VolatilityGuard:BaseStdDevThreshold"] = "3.0",
                ["VolatilityGuard:EnableDynamicThresholds"] = "true",
                ["BreakoutSignal:LookbackPeriodMinutes"] = "60",
                ["BreakoutSignal:MinBreakoutPercent"] = "0.5",
                ["BreakoutSignal:RequireBidSupport"] = "true",
                ["MicrostructurePattern:TickSequenceLength"] = "10",
                ["MicrostructurePattern:MinConsecutiveTicks"] = "3",
                ["MicrostructurePattern:RequireVolumeConfirmation"] = "true"
            })
            .Build();

        // Add all required services
        services.AddSingleton<IConfiguration>(configuration);
        services.AddLogging(builder => builder.AddConsole().SetMinimumLevel(LogLevel.Debug));
        
        // Add core infrastructure
        services.AddCoreInfrastructure();
        services.AddDataServices();
        services.AddMarketDataServices();
        services.AddTradingServices(); // This includes IMarketRegimeService
        services.AddEnhancedTradingServices(); // This includes Phase 6 services

        _serviceProvider = services.BuildServiceProvider();
        
        // Get Phase 6 services
        _vwapMonitor = _serviceProvider.GetRequiredService<IVWAPMonitorService>();
        _volatilityGuard = _serviceProvider.GetRequiredService<ITickVolatilityGuard>();
        _breakoutSignal = _serviceProvider.GetRequiredService<IRealTimeBreakoutSignal>();
        _microstructureDetector = _serviceProvider.GetRequiredService<IMicrostructurePatternDetector>();
    }

    [Fact]
    public void Phase6Services_ShouldBeRegisteredAndResolvable()
    {
        // Assert
        _vwapMonitor.Should().NotBeNull();
        _volatilityGuard.Should().NotBeNull();
        _breakoutSignal.Should().NotBeNull();
        _microstructureDetector.Should().NotBeNull();
    }

    [Fact]
    public void Phase6Services_ShouldHaveCorrectInitialStates()
    {
        // Assert
        _vwapMonitor.GetStatus().Should().Be(VWAPMonitorStatus.Stopped);
        _volatilityGuard.GetStatus().Should().Be(VolatilityGuardStatus.Stopped);
        _breakoutSignal.GetStatus().Should().Be(BreakoutMonitorStatus.Stopped);
        _microstructureDetector.GetStatus().Should().Be(MicrostructureDetectionStatus.Stopped);

        _vwapMonitor.GetMonitoredSymbols().Should().BeEmpty();
        _volatilityGuard.GetMonitoredSymbols().Should().BeEmpty();
        _breakoutSignal.GetMonitoredSymbols().Should().BeEmpty();
        _microstructureDetector.GetMonitoredSymbols().Should().BeEmpty();
    }

    [Fact]
    public async Task StartAllPhase6Services_WithSameSymbols_ShouldStartSuccessfully()
    {
        // Arrange
        var symbols = new[] { "AAPL", "MSFT", "GOOGL" };

        // Act
        await _vwapMonitor.StartMonitoringAsync(symbols);
        await _volatilityGuard.StartMonitoringAsync(symbols);
        await _breakoutSignal.StartMonitoringAsync(symbols);
        await _microstructureDetector.StartDetectionAsync(symbols);

        // Assert
        _vwapMonitor.GetStatus().Should().Be(VWAPMonitorStatus.Active);
        _volatilityGuard.GetStatus().Should().Be(VolatilityGuardStatus.Active);
        _breakoutSignal.GetStatus().Should().Be(BreakoutMonitorStatus.Active);
        _microstructureDetector.GetStatus().Should().Be(MicrostructureDetectionStatus.Active);

        _vwapMonitor.GetMonitoredSymbols().Should().BeEquivalentTo(symbols);
        _volatilityGuard.GetMonitoredSymbols().Should().BeEquivalentTo(symbols);
        _breakoutSignal.GetMonitoredSymbols().Should().BeEquivalentTo(symbols);
        _microstructureDetector.GetMonitoredSymbols().Should().BeEquivalentTo(symbols);
    }

    [Fact]
    public async Task AddSymbolsToAllServices_ShouldMaintainConsistency()
    {
        // Arrange
        var initialSymbols = new[] { "AAPL", "MSFT" };
        var additionalSymbols = new[] { "GOOGL", "TSLA" };

        // Start with initial symbols
        await _vwapMonitor.StartMonitoringAsync(initialSymbols);
        await _volatilityGuard.StartMonitoringAsync(initialSymbols);
        await _breakoutSignal.StartMonitoringAsync(initialSymbols);
        await _microstructureDetector.StartDetectionAsync(initialSymbols);

        // Act - Add additional symbols
        await _vwapMonitor.AddSymbolsAsync(additionalSymbols);
        await _volatilityGuard.StartMonitoringAsync(additionalSymbols); // Note: VolatilityGuard doesn't have AddSymbolsAsync
        await _breakoutSignal.AddSymbolsAsync(additionalSymbols);
        await _microstructureDetector.AddSymbolsAsync(additionalSymbols);

        // Assert
        var expectedSymbols = initialSymbols.Concat(additionalSymbols);
        _vwapMonitor.GetMonitoredSymbols().Should().BeEquivalentTo(expectedSymbols);
        _breakoutSignal.GetMonitoredSymbols().Should().BeEquivalentTo(expectedSymbols);
        _microstructureDetector.GetMonitoredSymbols().Should().BeEquivalentTo(expectedSymbols);
    }

    [Fact]
    public async Task VolatilityGuard_BlockingTrade_ShouldAffectOtherServices()
    {
        // Arrange
        var symbol = "AAPL";
        var symbols = new[] { symbol };

        await _volatilityGuard.StartMonitoringAsync(symbols);

        // Act - Block trading for the symbol
        await _volatilityGuard.OverrideBlockAsync(symbol, true, "Integration test block");

        // Assert
        _volatilityGuard.IsTradingBlocked(symbol).Should().BeTrue();
        _volatilityGuard.IsAnyTradingBlocked().Should().BeTrue();
        _volatilityGuard.GetBlockedSymbols().Should().Contain(symbol);
    }

    [Fact]
    public async Task ConfigurationUpdates_ShouldApplyToAllServices()
    {
        // Arrange
        var symbols = new[] { "AAPL" };
        
        await _vwapMonitor.StartMonitoringAsync(symbols);
        await _volatilityGuard.StartMonitoringAsync(symbols);
        await _breakoutSignal.StartMonitoringAsync(symbols);
        await _microstructureDetector.StartDetectionAsync(symbols);

        // Act - Update configurations
        var vwapConfig = new VWAPMonitorConfig(
            RollingMinutes: 45,
            MinTradesRequired: 15,
            RequireTrendingRegime: false
        );

        var volatilityConfig = new VolatilityGuardConfig(
            VolatilityWindowMinutes: 10,
            BaseStdDevThreshold: 4.0m,
            EnableDynamicThresholds: false
        );

        var breakoutConfig = new BreakoutSignalConfig(
            LookbackPeriodMinutes: 90,
            MinBreakoutPercent: 1.0m,
            RequireBidSupport: false
        );

        var microstructureConfig = new MicrostructurePatternConfig(
            TickSequenceLength: 15,
            MinConsecutiveTicks: 5,
            RequireVolumeConfirmation: false
        );

        await _vwapMonitor.UpdateParametersAsync(vwapConfig);
        await _volatilityGuard.UpdateConfigurationAsync(volatilityConfig);
        await _breakoutSignal.UpdateConfigurationAsync(breakoutConfig);
        await _microstructureDetector.UpdateConfigurationAsync(microstructureConfig);

        // Assert - No exceptions should be thrown
        // Configuration updates are internal, so we verify they complete successfully
    }

    [Fact]
    public async Task StopAllPhase6Services_ShouldStopCleanly()
    {
        // Arrange
        var symbols = new[] { "AAPL", "MSFT" };
        
        await _vwapMonitor.StartMonitoringAsync(symbols);
        await _volatilityGuard.StartMonitoringAsync(symbols);
        await _breakoutSignal.StartMonitoringAsync(symbols);
        await _microstructureDetector.StartDetectionAsync(symbols);

        // Act
        await _vwapMonitor.StopMonitoringAsync();
        await _volatilityGuard.StopMonitoringAsync();
        await _breakoutSignal.StopMonitoringAsync();
        await _microstructureDetector.StopDetectionAsync();

        // Assert
        _vwapMonitor.GetStatus().Should().Be(VWAPMonitorStatus.Stopped);
        _volatilityGuard.GetStatus().Should().Be(VolatilityGuardStatus.Stopped);
        _breakoutSignal.GetStatus().Should().Be(BreakoutMonitorStatus.Stopped);
        _microstructureDetector.GetStatus().Should().Be(MicrostructureDetectionStatus.Stopped);

        _vwapMonitor.GetMonitoredSymbols().Should().BeEmpty();
        _volatilityGuard.GetMonitoredSymbols().Should().BeEmpty();
        _breakoutSignal.GetMonitoredSymbols().Should().BeEmpty();
        _microstructureDetector.GetMonitoredSymbols().Should().BeEmpty();
    }

    [Theory]
    [InlineData("AAPL")]
    [InlineData("MSFT")]
    [InlineData("GOOGL")]
    public async Task Phase6Services_WithDifferentSymbols_ShouldHandleIndependently(string symbol)
    {
        // Arrange
        var symbols = new[] { symbol };

        // Act
        await _vwapMonitor.StartMonitoringAsync(symbols);
        await _volatilityGuard.StartMonitoringAsync(symbols);
        await _breakoutSignal.StartMonitoringAsync(symbols);
        await _microstructureDetector.StartDetectionAsync(symbols);

        // Assert
        _vwapMonitor.GetMonitoredSymbols().Should().Contain(symbol);
        _volatilityGuard.GetMonitoredSymbols().Should().Contain(symbol);
        _breakoutSignal.GetMonitoredSymbols().Should().Contain(symbol);
        _microstructureDetector.GetMonitoredSymbols().Should().Contain(symbol);

        // Verify data access methods don't throw
        var vwapData = await _vwapMonitor.GetCurrentVWAPAsync(symbol);
        var volatilityMetrics = await _volatilityGuard.GetVolatilityMetricsAsync(symbol);
        var breakoutStatus = await _breakoutSignal.GetBreakoutStatusAsync(symbol);
        var microstructureAnalysis = await _microstructureDetector.GetMicrostructureAnalysisAsync(symbol);

        // These may be null due to lack of real data, but should not throw exceptions
        // vwapData, volatilityMetrics, breakoutStatus, microstructureAnalysis can be null
    }

    public void Dispose()
    {
        _vwapMonitor?.Dispose();
        _volatilityGuard?.Dispose();
        _breakoutSignal?.Dispose();
        _microstructureDetector?.Dispose();
        _serviceProvider?.Dispose();
    }
}
