using Xunit;
using FluentAssertions;
using SmaTrendFollower.Models;
using SmaTrendFollower.Extensions;
using SmaTrendFollower.Services;
using Alpaca.Markets;
using Moq;
using Microsoft.Extensions.Logging;
using SmaTrendFollower.Tests.TestHelpers;

namespace SmaTrendFollower.Tests.Models;

public class EnumConflictResolutionTests
{
    [TestTimeout(TestTimeouts.Unit)]
    [Trait("Category", TestCategories.Network)]
    [Fact]
    public void SmaOrderSide_ToAlpacaOrderSide_ShouldConvertCorrectly()
    {
        // Arrange & Act & Assert
        SmaOrderSide.Buy.ToAlpacaOrderSide().Should().Be(OrderSide.Buy);
        SmaOrderSide.Sell.ToAlpacaOrderSide().Should().Be(OrderSide.Sell);
    }

    [TestTimeout(TestTimeouts.Unit)]
    [Trait("Category", TestCategories.Network)]
    [Fact]
    public void AlpacaOrderSide_ToSmaOrderSide_ShouldConvertCorrectly()
    {
        // Arrange & Act & Assert
        OrderSide.Buy.ToSmaOrderSide().Should().Be(SmaOrderSide.Buy);
        OrderSide.Sell.ToSmaOrderSide().Should().Be(SmaOrderSide.Sell);
    }

    [TestTimeout(TestTimeouts.Unit)]
    [Trait("Category", TestCategories.Network)]
    [Fact]
    public void SmaOrderType_ToAlpacaOrderType_ShouldConvertCorrectly()
    {
        // Arrange & Act & Assert
        SmaOrderType.Market.ToAlpacaOrderType().Should().Be(OrderType.Market);
        SmaOrderType.Limit.ToAlpacaOrderType().Should().Be(OrderType.Limit);
        SmaOrderType.Stop.ToAlpacaOrderType().Should().Be(OrderType.Stop);
        SmaOrderType.StopLimit.ToAlpacaOrderType().Should().Be(OrderType.StopLimit);
        SmaOrderType.TrailingStop.ToAlpacaOrderType().Should().Be(OrderType.TrailingStop);
    }

    [TestTimeout(TestTimeouts.Unit)]
    [Trait("Category", TestCategories.Network)]
    [Fact]
    public void AlpacaOrderType_ToSmaOrderType_ShouldConvertCorrectly()
    {
        // Arrange & Act & Assert
        OrderType.Market.ToSmaOrderType().Should().Be(SmaOrderType.Market);
        OrderType.Limit.ToSmaOrderType().Should().Be(SmaOrderType.Limit);
        OrderType.Stop.ToSmaOrderType().Should().Be(SmaOrderType.Stop);
        OrderType.StopLimit.ToSmaOrderType().Should().Be(SmaOrderType.StopLimit);
        OrderType.TrailingStop.ToSmaOrderType().Should().Be(SmaOrderType.TrailingStop);
    }

    [TestTimeout(TestTimeouts.Unit)]
    [Trait("Category", TestCategories.Network)]
    [Fact]
    public void SmaEventImpactLevel_ToPositionSizeMultiplier_ShouldReturnCorrectValues()
    {
        // Arrange & Act & Assert
        SmaEventImpactLevel.Low.ToPositionSizeMultiplier().Should().Be(1.0m);
        SmaEventImpactLevel.Medium.ToPositionSizeMultiplier().Should().Be(0.75m);
        SmaEventImpactLevel.High.ToPositionSizeMultiplier().Should().Be(0.5m);
        SmaEventImpactLevel.Critical.ToPositionSizeMultiplier().Should().Be(0.25m);
        SmaEventImpactLevel.Unknown.ToPositionSizeMultiplier().Should().Be(0.5m);
    }

    [TestTimeout(TestTimeouts.Unit)]
    [Trait("Category", TestCategories.Network)]
    [Fact]
    public void SmaEventTradingAction_AllowsNewPositions_ShouldReturnCorrectValues()
    {
        // Arrange & Act & Assert
        SmaEventTradingAction.ContinueNormal.AllowsNewPositions().Should().BeTrue();
        SmaEventTradingAction.ReducePositionSize.AllowsNewPositions().Should().BeTrue();
        SmaEventTradingAction.IncreaseMonitoring.AllowsNewPositions().Should().BeTrue();
        SmaEventTradingAction.ApplyTighterStops.AllowsNewPositions().Should().BeTrue();
        SmaEventTradingAction.AvoidNewPositions.AllowsNewPositions().Should().BeFalse();
        SmaEventTradingAction.CloseExistingPositions.AllowsNewPositions().Should().BeFalse();
        SmaEventTradingAction.NoAction.AllowsNewPositions().Should().BeTrue();
    }

    [TestTimeout(TestTimeouts.Unit)]
    [Trait("Category", TestCategories.Network)]
    [Fact]
    public void SmaEventTradingAction_ToStopLossMultiplier_ShouldReturnCorrectValues()
    {
        // Arrange & Act & Assert
        SmaEventTradingAction.ContinueNormal.ToStopLossMultiplier().Should().Be(1.0m);
        SmaEventTradingAction.ApplyTighterStops.ToStopLossMultiplier().Should().Be(0.75m);
        SmaEventTradingAction.CloseExistingPositions.ToStopLossMultiplier().Should().Be(0.5m);
    }

    [TestTimeout(TestTimeouts.Unit)]
    [Trait("Category", TestCategories.Network)]
    [Fact]
    public void SmaSecFilingType_ToImpactLevel_ShouldReturnCorrectValues()
    {
        // Arrange & Act & Assert
        SmaSecFilingType.Form8K.ToImpactLevel().Should().Be(SmaEventImpactLevel.High);
        SmaSecFilingType.Form10K.ToImpactLevel().Should().Be(SmaEventImpactLevel.Medium);
        SmaSecFilingType.Form10Q.ToImpactLevel().Should().Be(SmaEventImpactLevel.Medium);
        SmaSecFilingType.FormS1.ToImpactLevel().Should().Be(SmaEventImpactLevel.High);
        SmaSecFilingType.ProxyStatement.ToImpactLevel().Should().Be(SmaEventImpactLevel.Low);
    }

    [TestTimeout(TestTimeouts.Unit)]
    [Trait("Category", TestCategories.Network)]
    [Fact]
    public void SmaSecFilingType_RequiresImmediateAttention_ShouldReturnCorrectValues()
    {
        // Arrange & Act & Assert
        SmaSecFilingType.Form8K.RequiresImmediateAttention().Should().BeTrue();
        SmaSecFilingType.FormS1.RequiresImmediateAttention().Should().BeTrue();
        SmaSecFilingType.Form4.RequiresImmediateAttention().Should().BeTrue();
        SmaSecFilingType.Form10K.RequiresImmediateAttention().Should().BeFalse();
        SmaSecFilingType.ProxyStatement.RequiresImmediateAttention().Should().BeFalse();
    }

    [TestTimeout(TestTimeouts.Unit)]
    [Trait("Category", TestCategories.Network)]
    [Fact]
    public void SmaMarketEventType_ToImpactLevel_ShouldReturnCorrectValues()
    {
        // Arrange & Act & Assert
        SmaMarketEventType.EarningsAnnouncement.ToImpactLevel().Should().Be(SmaEventImpactLevel.High);
        SmaMarketEventType.MergerAcquisition.ToImpactLevel().Should().Be(SmaEventImpactLevel.Critical);
        SmaMarketEventType.FdaApproval.ToImpactLevel().Should().Be(SmaEventImpactLevel.Critical);
        SmaMarketEventType.DividendDeclaration.ToImpactLevel().Should().Be(SmaEventImpactLevel.Low);
        SmaMarketEventType.ConferenceCall.ToImpactLevel().Should().Be(SmaEventImpactLevel.Low);
    }

    [TestTimeout(TestTimeouts.Unit)]
    [Trait("Category", TestCategories.Network)]
    [Fact]
    public void SmaMarketEventType_CausesHighVolatility_ShouldReturnCorrectValues()
    {
        // Arrange & Act & Assert
        SmaMarketEventType.EarningsAnnouncement.CausesHighVolatility().Should().BeTrue();
        SmaMarketEventType.MergerAcquisition.CausesHighVolatility().Should().BeTrue();
        SmaMarketEventType.FdaApproval.CausesHighVolatility().Should().BeTrue();
        SmaMarketEventType.DividendDeclaration.CausesHighVolatility().Should().BeFalse();
        SmaMarketEventType.ConferenceCall.CausesHighVolatility().Should().BeFalse();
    }

    [TestTimeout(TestTimeouts.Unit)]
    [Trait("Category", TestCategories.Network)]
    [Fact]
    public void SmaEventTimePeriod_ToTimeSpan_ShouldReturnCorrectValues()
    {
        // Arrange & Act & Assert
        SmaEventTimePeriod.Next24Hours.ToTimeSpan().Should().Be(TimeSpan.FromDays(1));
        SmaEventTimePeriod.Next3Days.ToTimeSpan().Should().Be(TimeSpan.FromDays(3));
        SmaEventTimePeriod.NextWeek.ToTimeSpan().Should().Be(TimeSpan.FromDays(7));
        SmaEventTimePeriod.Past24Hours.ToTimeSpan().Should().Be(TimeSpan.FromDays(-1));
        SmaEventTimePeriod.PastWeek.ToTimeSpan().Should().Be(TimeSpan.FromDays(-7));
    }

    [TestTimeout(TestTimeouts.Unit)]
    [Trait("Category", TestCategories.Network)]
    [Fact]
    public void SmaEventTimePeriod_ToDateRange_ShouldReturnCorrectRange()
    {
        // Arrange
        var referenceDate = new DateTime(2024, 6, 20, 12, 0, 0, DateTimeKind.Utc);

        // Act
        var (start, end) = SmaEventTimePeriod.Next3Days.ToDateRange(referenceDate);

        // Assert
        start.Should().Be(referenceDate);
        end.Should().Be(referenceDate.AddDays(3));
    }

    [TestTimeout(TestTimeouts.Unit)]
    [Trait("Category", TestCategories.Network)]
    [Fact]
    public void SmaEventTimePeriod_ToDateRange_PastPeriod_ShouldReturnCorrectRange()
    {
        // Arrange
        var referenceDate = new DateTime(2024, 6, 20, 12, 0, 0, DateTimeKind.Utc);

        // Act
        var (start, end) = SmaEventTimePeriod.Past3Days.ToDateRange(referenceDate);

        // Assert
        start.Should().Be(referenceDate.AddDays(-3));
        end.Should().Be(referenceDate);
    }

    [TestTimeout(TestTimeouts.Unit)]
    [Trait("Category", TestCategories.Network)]
    [Fact]
    public void EnumExtensions_GetDescription_ShouldReturnCorrectDescription()
    {
        // Arrange & Act & Assert
        SmaSecFilingType.Form10K.GetDescription().Should().Be("10-K Annual Report");
        SmaMarketEventType.EarningsAnnouncement.GetDescription().Should().Be("Earnings Announcement");
        SmaEventTradingAction.AvoidNewPositions.GetDescription().Should().Be("Avoid New Positions");
    }

    [TestTimeout(TestTimeouts.Unit)]
    [Trait("Category", TestCategories.Network)]
    [Fact]
    public void EnumExtensions_ParseFromDescription_ShouldReturnCorrectEnum()
    {
        // Arrange & Act & Assert
        EnumExtensions.ParseFromDescription<SmaSecFilingType>("10-K Annual Report").Should().Be(SmaSecFilingType.Form10K);
        EnumExtensions.ParseFromDescription<SmaMarketEventType>("Earnings Announcement").Should().Be(SmaMarketEventType.EarningsAnnouncement);
    }

    [TestTimeout(TestTimeouts.Unit)]
    [Trait("Category", TestCategories.Network)]
    [Fact]
    public void EnumExtensions_GetEnumDescriptions_ShouldReturnAllDescriptions()
    {
        // Act
        var descriptions = EnumExtensions.GetEnumDescriptions<SmaEventImpactLevel>();

        // Assert
        descriptions.Should().NotBeEmpty();
        descriptions.Should().ContainKey(SmaEventImpactLevel.Low);
        descriptions.Should().ContainKey(SmaEventImpactLevel.High);
        descriptions[SmaEventImpactLevel.Low].Should().Be("Low Impact");
        descriptions[SmaEventImpactLevel.High].Should().Be("High Impact");
    }

    [TestTimeout(TestTimeouts.Unit)]
    [Trait("Category", TestCategories.Network)]
    [Fact]
    public void EnumExtensions_IsValidEnumValue_ShouldReturnCorrectResult()
    {
        // Arrange & Act & Assert
        SmaEventImpactLevel.Low.IsValidEnumValue().Should().BeTrue();
        SmaEventImpactLevel.High.IsValidEnumValue().Should().BeTrue();
        ((SmaEventImpactLevel)999).IsValidEnumValue().Should().BeFalse();
    }

    [TestTimeout(TestTimeouts.Unit)]
    [Trait("Category", TestCategories.Network)]
    [Fact]
    public void EnumExtensions_GetSafeEnumValue_ShouldReturnCorrectValue()
    {
        // Arrange
        var validEnum = SmaEventImpactLevel.High;
        var invalidEnum = (SmaEventImpactLevel)999;
        var defaultValue = SmaEventImpactLevel.Unknown;

        // Act & Assert
        validEnum.GetSafeEnumValue(defaultValue).Should().Be(SmaEventImpactLevel.High);
        invalidEnum.GetSafeEnumValue(defaultValue).Should().Be(SmaEventImpactLevel.Unknown);
    }

    [TestTimeout(TestTimeouts.Unit)]
    [Trait("Category", TestCategories.Network)]
    [Fact]
    public void MarketEvent_ShouldInitializeCorrectly()
    {
        // Arrange & Act
        var marketEvent = new MarketEvent
        {
            Symbol = "AAPL",
            EventType = SmaMarketEventType.EarningsAnnouncement,
            EventDate = DateTime.UtcNow.AddDays(2),
            ImpactLevel = SmaEventImpactLevel.High,
            Description = "Q2 Earnings Release",
            Source = "Test"
        };

        // Assert
        marketEvent.Symbol.Should().Be("AAPL");
        marketEvent.EventType.Should().Be(SmaMarketEventType.EarningsAnnouncement);
        marketEvent.ImpactLevel.Should().Be(SmaEventImpactLevel.High);
        marketEvent.Description.Should().Be("Q2 Earnings Release");
        marketEvent.Metadata.Should().NotBeNull();
    }

    [TestTimeout(TestTimeouts.Unit)]
    [Trait("Category", TestCategories.Network)]
    [Fact]
    public void SecFiling_ShouldInitializeCorrectly()
    {
        // Arrange & Act
        var secFiling = new SmaTrendFollower.Models.SecFiling
        {
            Symbol = "MSFT",
            FilingType = SmaSecFilingType.Form8K,
            FilingDate = DateTime.UtcNow.AddHours(-6),
            Description = "Current Report",
            Url = "https://sec.gov/test"
        };

        // Assert
        secFiling.Symbol.Should().Be("MSFT");
        secFiling.FilingType.Should().Be(SmaSecFilingType.Form8K);
        secFiling.Description.Should().Be("Current Report");
        secFiling.Url.Should().Be("https://sec.gov/test");
        secFiling.Metadata.Should().NotBeNull();
    }

    [TestTimeout(TestTimeouts.Unit)]
    [Trait("Category", TestCategories.Network)]
    [Fact]
    public void EventTradingRecommendation_ShouldInitializeCorrectly()
    {
        // Arrange & Act
        var recommendation = new EventTradingRecommendation
        {
            Symbol = "GOOGL",
            Action = SmaEventTradingAction.ReducePositionSize,
            Reason = "High impact events detected",
            Confidence = 0.85m,
            RecommendedPositionSizeMultiplier = 0.5m,
            FilterType = SmaEventFilterType.ExcludeHighImpact
        };

        // Assert
        recommendation.Symbol.Should().Be("GOOGL");
        recommendation.Action.Should().Be(SmaEventTradingAction.ReducePositionSize);
        recommendation.Confidence.Should().Be(0.85m);
        recommendation.RecommendedPositionSizeMultiplier.Should().Be(0.5m);
        recommendation.FilterType.Should().Be(SmaEventFilterType.ExcludeHighImpact);
    }
}

public class SecEventFilterServiceTests
{
    private readonly Mock<ILogger<SecEventFilterService>> _mockLogger;
    private readonly Mock<IHttpClientFactory> _mockHttpClientFactory;
    private readonly SecEventFilterService _service;

    public SecEventFilterServiceTests()
    {
        _mockLogger = new Mock<ILogger<SecEventFilterService>>();
        _mockHttpClientFactory = new Mock<IHttpClientFactory>();
        _service = new SecEventFilterService(_mockLogger.Object, _mockHttpClientFactory.Object);
    }

    [TestTimeout(TestTimeouts.Unit)]
    [Trait("Category", TestCategories.Network)]
    [Fact]
    public async Task GetUpcomingEventsAsync_ShouldReturnEvents()
    {
        // Act
        var events = await _service.GetUpcomingEventsAsync("AAPL", SmaEventTimePeriod.Next3Days);

        // Assert
        events.Should().NotBeNull();
        // Note: This will return mock data in the current implementation
    }

    [TestTimeout(TestTimeouts.Unit)]
    [Trait("Category", TestCategories.Network)]
    [Fact]
    public async Task GetRecentFilingsAsync_ShouldReturnFilings()
    {
        // Act
        var filings = await _service.GetRecentFilingsAsync("MSFT", SmaEventTimePeriod.Past24Hours);

        // Assert
        filings.Should().NotBeNull();
        // Note: This will return mock data in the current implementation
    }

    [TestTimeout(TestTimeouts.Unit)]
    [Trait("Category", TestCategories.Network)]
    [Fact]
    public async Task GetTradingRecommendationAsync_ShouldReturnRecommendation()
    {
        // Act
        var recommendation = await _service.GetTradingRecommendationAsync("GOOGL", SmaEventFilterType.ExcludeEarnings);

        // Assert
        recommendation.Should().NotBeNull();
        recommendation.Symbol.Should().Be("GOOGL");
        recommendation.FilterType.Should().Be(SmaEventFilterType.ExcludeEarnings);
        recommendation.Action.Should().BeOneOf(
            SmaEventTradingAction.ContinueNormal,
            SmaEventTradingAction.AvoidNewPositions,
            SmaEventTradingAction.ReducePositionSize,
            SmaEventTradingAction.ApplyTighterStops);
    }

    [TestTimeout(TestTimeouts.Unit)]
    [Trait("Category", TestCategories.Network)]
    [Fact]
    public async Task HasHighImpactEventsAsync_ShouldReturnBoolean()
    {
        // Act
        var hasHighImpact = await _service.HasHighImpactEventsAsync("TSLA", SmaEventTimePeriod.Next3Days);

        // Assert
        Assert.IsType<bool>(hasHighImpact);
    }

    [TestTimeout(TestTimeouts.Unit)]
    [Trait("Category", TestCategories.Network)]
    [Fact]
    public async Task GetEventImpactLevelAsync_ShouldReturnImpactLevel()
    {
        // Arrange
        var targetDate = DateTime.UtcNow.AddDays(1);

        // Act
        var impactLevel = await _service.GetEventImpactLevelAsync("NVDA", targetDate);

        // Assert
        impactLevel.Should().BeOneOf(
            SmaEventImpactLevel.Low,
            SmaEventImpactLevel.Medium,
            SmaEventImpactLevel.High,
            SmaEventImpactLevel.Critical,
            SmaEventImpactLevel.Unknown);
    }
}
