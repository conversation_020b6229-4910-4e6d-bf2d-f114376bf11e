using Microsoft.Extensions.Logging;
using Quartz;
using Quartz.Spi;
using SmaTrendFollower.Services;

namespace SmaTrendFollower.Scheduling;

/// <summary>
/// Quartz job that handles weekly symbol fetching and daily universe filtering
/// </summary>
[DisallowConcurrentExecution]
public class UniverseJobs : IJob
{
    private readonly IUniverseFetcherService _universeFetcherService;
    private readonly IDynamicUniverseFilterJob _dynamicUniverseFilterJob;
    private readonly ILogger<UniverseJobs> _logger;

    public UniverseJobs(
        IUniverseFetcherService universeFetcherService,
        IDynamicUniverseFilterJob dynamicUniverseFilterJob,
        ILogger<UniverseJobs> logger)
    {
        _universeFetcherService = universeFetcherService ?? throw new ArgumentNullException(nameof(universeFetcherService));
        _dynamicUniverseFilterJob = dynamicUniverseFilterJob ?? throw new ArgumentNullException(nameof(dynamicUniverseFilterJob));
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
    }

    public async Task Execute(IJobExecutionContext context)
    {
        var jobKey = context.JobDetail.Key;
        var triggerKey = context.Trigger.Key;
        
        _logger.LogInformation("UniverseJobs started - Job: {JobKey}, Trigger: {TriggerKey}", jobKey, triggerKey);

        try
        {
            var cancellationToken = context.CancellationToken;
            
            // Check if we need to fetch symbols (weekly on Sunday OR if cache is missing)
            var shouldFetchSymbols = await ShouldFetchSymbolsAsync(cancellationToken);
            
            if (shouldFetchSymbols)
            {
                _logger.LogInformation("Fetching fresh symbol list from Polygon API");
                await _universeFetcherService.FetchAllSymbolsAsync(cancellationToken);
                _logger.LogInformation("Symbol list fetch completed");
            }
            else
            {
                _logger.LogInformation("Symbol cache is valid, skipping fetch");
            }

            // Always run daily universe filtering at 08:30 ET
            await _dynamicUniverseFilterJob.RunOnceAsync(cancellationToken);
        }
        catch (OperationCanceledException)
        {
            _logger.LogWarning("UniverseJobs was cancelled");
            throw;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error executing UniverseJobs");
            throw;
        }

        _logger.LogInformation("UniverseJobs completed successfully");
    }

    /// <summary>
    /// Determine if we should fetch symbols from Polygon API
    /// </summary>
    private async Task<bool> ShouldFetchSymbolsAsync(CancellationToken cancellationToken)
    {
        try
        {
            // Check if today is Sunday (weekly refresh day)
            var today = DateTime.Now.DayOfWeek;
            if (today == DayOfWeek.Sunday)
            {
                _logger.LogInformation("Today is Sunday - weekly symbol refresh day");
                return true;
            }

            // Check if cache is missing or invalid
            var isCacheValid = await _universeFetcherService.IsCacheValidAsync(cancellationToken);
            if (!isCacheValid)
            {
                _logger.LogInformation("Symbol cache is invalid or missing");
                return true;
            }

            // Check if we have any cached symbols
            var cachedSymbols = await _universeFetcherService.GetCachedSymbolsAsync(cancellationToken);
            if (cachedSymbols == null || cachedSymbols.Count == 0)
            {
                _logger.LogInformation("No cached symbols found");
                return true;
            }

            _logger.LogInformation("Symbol cache is valid with {Count} symbols", cachedSymbols.Count);
            return false;
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Error checking symbol cache status, will fetch fresh symbols");
            return true;
        }
    }
}

/// <summary>
/// Job factory for creating UniverseJobs with dependency injection
/// </summary>
public class UniverseJobFactory : IJobFactory
{
    private readonly IServiceProvider _serviceProvider;

    public UniverseJobFactory(IServiceProvider serviceProvider)
    {
        _serviceProvider = serviceProvider;
    }

    public IJob NewJob(TriggerFiredBundle bundle, IScheduler scheduler)
    {
        var jobType = bundle.JobDetail.JobType;
        return (IJob)_serviceProvider.GetService(jobType)!;
    }

    public void ReturnJob(IJob job)
    {
        // No cleanup needed for DI-managed jobs
    }
}
