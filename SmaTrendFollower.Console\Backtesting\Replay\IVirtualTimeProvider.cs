namespace SmaTrendFollower.Backtesting.Replay;

/// <summary>
/// Interface for providing time during backtesting that can be controlled
/// </summary>
public interface IVirtualTimeProvider
{
    /// <summary>
    /// Gets the current virtual time (UTC)
    /// </summary>
    DateTime UtcNow { get; }

    /// <summary>
    /// Sets the virtual time for backtesting
    /// </summary>
    /// <param name="virtualTime">The virtual time to set</param>
    void SetVirtualTime(DateTime virtualTime);

    /// <summary>
    /// Resets to real time
    /// </summary>
    void ResetToRealTime();

    /// <summary>
    /// Indicates if virtual time is active
    /// </summary>
    bool IsVirtualTimeActive { get; }
}

/// <summary>
/// Implementation of virtual time provider for backtesting
/// </summary>
public class VirtualTimeProvider : IVirtualTimeProvider
{
    private DateTime? _virtualTime;
    private readonly object _lock = new();

    public DateTime UtcNow
    {
        get
        {
            lock (_lock)
            {
                return _virtualTime ?? DateTime.UtcNow;
            }
        }
    }

    public bool IsVirtualTimeActive
    {
        get
        {
            lock (_lock)
            {
                return _virtualTime.HasValue;
            }
        }
    }

    public void SetVirtualTime(DateTime virtualTime)
    {
        lock (_lock)
        {
            _virtualTime = virtualTime;
        }
    }

    public void ResetToRealTime()
    {
        lock (_lock)
        {
            _virtualTime = null;
        }
    }
}
