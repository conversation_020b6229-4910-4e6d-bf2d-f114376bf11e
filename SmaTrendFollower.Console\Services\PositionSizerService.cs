using Microsoft.ML;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Configuration;
using StackExchange.Redis;
using SmaTrendFollower.Services;
using SmaTrendFollower.Monitoring;

namespace SmaTrendFollower.Services;

/// <summary>
/// ML-powered position sizing service that dynamically calculates position sizes
/// based on signal quality, market conditions, and risk factors using LightGBM regression.
/// </summary>
public sealed class PositionSizerService : IDisposable
{
    private readonly MLContext _mlContext;
    private readonly ILogger<PositionSizerService> _logger;
    private readonly string _modelPath;
    private readonly IDatabase? _redis;
    private PredictionEngine<PositionSizingInput, PositionSizingOutput>? _predictionEngine;
    private long _modelVersion = 0;
    private bool _disposed;

    public PositionSizerService(
        ILogger<PositionSizerService> logger, 
        IConfiguration configuration,
        OptimizedRedisConnectionService? redisService = null)
    {
        _mlContext = new MLContext(seed: 42);
        _logger = logger;
        _modelPath = configuration.GetValue<string>("ML:PositionModelPath") ?? "Model/position_model.zip";

        // Initialize Redis connection for hot-reload functionality
        if (redisService != null)
        {
            try
            {
                _redis = redisService.GetDatabaseAsync().Result;
                _logger.LogInformation("PositionSizerService initialized with Redis hot-reload support");
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Failed to initialize Redis connection for PositionSizerService, hot-reload disabled");
            }
        }

        // Load model on startup
        _ = Task.Run(LoadModelAsync);
    }

    /// <summary>
    /// Calculates optimal position size based on ML model prediction
    /// </summary>
    /// <param name="equity">Current account equity</param>
    /// <param name="rankProb">Signal ranking probability (0.0 to 1.0)</param>
    /// <param name="atrPct">ATR as percentage of close price</param>
    /// <param name="spreadPct">Average spread as percentage of price</param>
    /// <returns>Position size in dollars</returns>
    public decimal CalculatePositionSize(decimal equity, float rankProb, float atrPct, float spreadPct)
    {
        // Check for model updates before prediction
        ReloadIfChanged();

        if (_predictionEngine == null)
        {
            _logger.LogWarning("Position sizing model not loaded, using fallback calculation");
            return CalculateFallbackPositionSize(equity, rankProb, atrPct);
        }

        try
        {
            var input = new PositionSizingInput
            {
                RankProb = rankProb,
                ATR_Pct = atrPct,
                AvgSpreadPct = spreadPct
            };

            var prediction = _predictionEngine.Predict(input);
            var equityPctRisk = prediction.Score;

            // Apply safety constraints
            equityPctRisk = Math.Clamp(equityPctRisk, 0f, 0.05f); // Cap at 5% of equity

            var positionSize = equity * (decimal)equityPctRisk;

            // Record metrics
            MetricsRegistry.PositionSizePct.WithLabels("ml_model").Set((double)equityPctRisk);

            _logger.LogDebug("ML position sizing: Equity={Equity:C}, RankProb={RankProb:F3}, " +
                           "ATR_Pct={ATR_Pct:F4}, SpreadPct={SpreadPct:F6}, " +
                           "PredictedRisk={PredictedRisk:P3}, PositionSize={PositionSize:C}",
                equity, rankProb, atrPct, spreadPct, equityPctRisk, positionSize);

            return positionSize > 0 ? positionSize : 0m;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in ML position sizing, using fallback");
            return CalculateFallbackPositionSize(equity, rankProb, atrPct);
        }
    }

    /// <summary>
    /// Fallback position sizing when ML model is unavailable
    /// </summary>
    private decimal CalculateFallbackPositionSize(decimal equity, float rankProb, float atrPct)
    {
        // Simple rule-based fallback
        var baseRiskPct = 0.01m; // 1% base risk
        
        // Adjust based on signal quality
        var signalMultiplier = (decimal)Math.Clamp(rankProb * 2.0f, 0.5f, 2.0f);
        
        // Adjust based on volatility (higher ATR = lower position)
        var volatilityMultiplier = Math.Clamp(1.0m / (1.0m + (decimal)atrPct * 10), 0.3m, 1.0m);
        
        var adjustedRiskPct = baseRiskPct * signalMultiplier * volatilityMultiplier;
        adjustedRiskPct = Math.Clamp(adjustedRiskPct, 0.002m, 0.03m); // 0.2% to 3%

        var positionSize = equity * adjustedRiskPct;

        MetricsRegistry.PositionSizePct.WithLabels("fallback").Set((double)adjustedRiskPct);

        _logger.LogDebug("Fallback position sizing: Equity={Equity:C}, RankProb={RankProb:F3}, " +
                       "ATR_Pct={ATR_Pct:F4}, RiskPct={RiskPct:P3}, PositionSize={PositionSize:C}",
            equity, rankProb, atrPct, adjustedRiskPct, positionSize);

        return positionSize;
    }

    /// <summary>
    /// Checks Redis for model version updates and reloads if changed
    /// </summary>
    private void ReloadIfChanged()
    {
        if (_redis == null) return;

        try
        {
            var versionTicks = _redis.StringGet("model:position:version");
            if (!versionTicks.HasValue) return;

            long version = (long)versionTicks;
            if (version == _modelVersion) return;

            // Model version changed, reload
            var model = _mlContext.Model.Load(_modelPath, out _);
            _predictionEngine?.Dispose();
            _predictionEngine = _mlContext.Model.CreatePredictionEngine<PositionSizingInput, PositionSizingOutput>(model);
            _modelVersion = version;

            _logger.LogInformation("PositionSizerService hot-reloaded model (version {Version})", version);
            MetricsRegistry.MLModelVersion.Set(version);
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Failed to check/reload position sizing model version from Redis");
        }
    }

    /// <summary>
    /// Loads the ML model from disk
    /// </summary>
    private async Task LoadModelAsync()
    {
        try
        {
            if (!File.Exists(_modelPath))
            {
                _logger.LogWarning("Position sizing model file not found: {ModelPath}", _modelPath);
                return;
            }

            await Task.Run(() =>
            {
                var model = _mlContext.Model.Load(_modelPath, out _);
                _predictionEngine = _mlContext.Model.CreatePredictionEngine<PositionSizingInput, PositionSizingOutput>(model);
                
                // Update version in Redis if available
                if (_redis != null)
                {
                    var version = File.GetLastWriteTimeUtc(_modelPath).Ticks;
                    _redis.StringSet("model:position:version", version, RedisKeyConstants.RedisKeyTTL.MLModel);
                    _modelVersion = version;
                    MetricsRegistry.MLModelVersion.Set(version);
                }
            });

            _logger.LogInformation("Position sizing model loaded successfully from {ModelPath}", _modelPath);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to load position sizing model from {ModelPath}", _modelPath);
        }
    }

    /// <summary>
    /// Reloads the model from disk (useful for hot-swapping updated models)
    /// </summary>
    public async Task ReloadModelAsync()
    {
        _logger.LogInformation("Reloading position sizing model...");
        
        // Dispose current prediction engine
        _predictionEngine?.Dispose();
        _predictionEngine = null;

        // Reload model
        await LoadModelAsync();
    }

    /// <summary>
    /// Gets model information and status
    /// </summary>
    public PositionSizerInfo GetModelInfo()
    {
        var isLoaded = _predictionEngine != null;
        var modelExists = File.Exists(_modelPath);
        var lastModified = modelExists ? File.GetLastWriteTimeUtc(_modelPath) : (DateTime?)null;

        return new PositionSizerInfo(
            IsLoaded: isLoaded,
            ModelPath: _modelPath,
            ModelExists: modelExists,
            LastModified: lastModified,
            ModelVersion: _modelVersion,
            RedisEnabled: _redis != null
        );
    }

    public void Dispose()
    {
        if (_disposed) return;

        _predictionEngine?.Dispose();
        _disposed = true;
    }
}

/// <summary>
/// Input features for position sizing ML model
/// </summary>
public class PositionSizingInput
{
    public float RankProb { get; set; }
    public float ATR_Pct { get; set; }
    public float AvgSpreadPct { get; set; }
}

/// <summary>
/// Output from position sizing ML model
/// </summary>
public class PositionSizingOutput
{
    public float Score { get; set; }
}

/// <summary>
/// Information about the position sizer model
/// </summary>
public record PositionSizerInfo(
    bool IsLoaded,
    string ModelPath,
    bool ModelExists,
    DateTime? LastModified,
    long ModelVersion,
    bool RedisEnabled
);
