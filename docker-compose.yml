

volumes:
  vault-data:
  redis-data:
  prometheus-data:
  grafana-data:
  gitea-data:
  actions-runner-data:

services:
  vault:
    image: hashicorp/vault:1.16
    container_name: vault
    restart: unless-stopped
    environment:
      VAULT_DEV_ROOT_TOKEN_ID: root
      VAULT_DEV_LISTEN_ADDRESS: "0.0.0.0:8200"
    ports: ["8200:8200"]
    volumes: [ vault-data:/vault/file ]
    deploy:
      resources:
        limits:
          cpus: "0.5"
          memory: 512m
    cpuset: "6"

  redis:
    image: redis:7-alpine
    container_name: redis
    restart: unless-stopped
    command: ["redis-server", "--save", "900", "1", "--save", "300", "10"]
    volumes: [ redis-data:/data ]
    ports: ["6379:6379"]
    deploy:
      resources:
        limits:
          cpus: "1"
          memory: 2g
    cpuset: "4"

  prometheus:
    image: prom/prometheus:v2.52.0
    container_name: prometheus
    restart: unless-stopped
    volumes:
      - ./monitoring/prometheus/prometheus.yml:/etc/prometheus/prometheus.yml
      - ./monitoring/prometheus/alerts.yml:/etc/prometheus/alerts.yml
      - prometheus-data:/prometheus
    ports: ["9090:9090"]
    deploy:
      resources:
        limits:
          cpus: "1"
          memory: 4g
    cpuset: "5"

  grafana:
    image: grafana/grafana:11.0.0
    container_name: grafana
    restart: unless-stopped
    depends_on: [ prometheus ]
    environment:
      GF_INSTALL_PLUGINS: "grafana-piechart-panel"
    volumes:
      - grafana-data:/var/lib/grafana
      - ./monitoring/grafana/dashboards:/var/lib/grafana/dashboards
      - ./monitoring/grafana/provisioning/dashboards:/etc/grafana/provisioning/dashboards
    ports: ["3000:3000"]
    deploy:
      resources:
        limits:
          cpus: "0.5"
          memory: 1g
    cpuset: "5"
    cpu_shares: 128

  gitea:
    image: gitea/gitea:1.22
    container_name: gitea
    restart: unless-stopped
    environment:
      USER_UID: 1000
      USER_GID: 1000
      GITEA__server__DOMAIN: gitea.local
      GITEA__server__ROOT_URL: http://gitea.local:3001/
    volumes: [ gitea-data:/data ]
    ports: ["3001:3000","2222:22"]
    deploy:
      resources:
        limits:
          cpus: "0.5"
          memory: 1g
    cpuset: "7"

  actions-runner:
    image: myoung34/github-runner:latest
    container_name: actions-runner
    restart: unless-stopped
    depends_on: [ gitea ]
    environment:
      RUNNER_NAME: local-runner
      RUNNER_WORKDIR: /runner/_work
      RUNNER_SCOPE: repo
      GITHUB_URL: https://github.com/patco1/SmaTrendFollower
      RUNNER_TOKEN: ${GITHUB_RUNNER_TOKEN:-dummy}
    volumes: [ actions-runner-data:/runner ]
    deploy:
      resources:
        limits:
          cpus: "2"
          memory: 6g
    cpuset: "6-7"
    cpu_shares: 512

  bot:
    build: .
    container_name: sma-trend-follower
    restart: unless-stopped
    depends_on: [ redis, vault ]
    environment:
      ASPNETCORE_ENVIRONMENT: Production
      VAULT_ADDR:  http://vault:8200
      VAULT_TOKEN: root
      PROM_INSTANCE: bot-01
      VAULT_SECRET_PATH: sma
      Redis__ConnectionString: redis:6379
      REDIS_CONNECTION_STRING: redis:6379
      Redis__UniverseCacheConnection: redis:6379
      APCA_API_ENV: live                   # Set to 'live' for live trading
      POLYGON_API_KEY: ********************************
      POLY_API_KEY: ********************************
      ALPACA_API_KEY_ID: AKGBPW5HD8LVI5C6NJUJ
      ALPACA_SECRET: MdrZZtKbFtLQWNuggQMDqN12loiOheHWjSiWN2CM
      APCA_API_KEY_ID: AKGBPW5HD8LVI5C6NJUJ
      APCA_API_SECRET_KEY: MdrZZtKbFtLQWNuggQMDqN12loiOheHWjSiWN2CM
      APCA_API_SECRET: MdrZZtKbFtLQWNuggQMDqN12loiOheHWjSiWN2CM
      DISCORD_BOT_TOKEN: MTM4NTA1OTI3MDMzNjMxNTQ1NA.GtTKUd.fuCC2ZI-H-tTLZl41YF3gZj-w3gPbv_Xep8NoE
      DISCORD_CHANNEL_ID: 1385057459814797383
      APCA_API_KEY_ID_PAPER: PK0AM3WB1CES3YBQPGR0
      APCA_API_SECRET_KEY_PAPER: 2O4bJsHpGjyYU6FvQ956kIJaLePdBihZveKutbtf
      APCA_API_KEY_ID_LIVE: AKGBPW5HD8LVI5C6NJUJ
      APCA_API_SECRET_KEY_LIVE: MdrZZtKbFtLQWNuggQMDqN12loiOheHWjSiWN2CM
      OPENAI_API_KEY: ********************************************************************************************************************************************************************
      GEMINI_API_KEY: ${GEMINI_API_KEY}
    ports: ["5000:5000"]
    deploy:
      resources:
        limits:
          cpus: "4"           # container gets 4 cores worth of time
          memory: 8g
        reservations:
          cpus: "2"
          memory: 4g
    cpuset: "0-3"             # pin to first 4 threads