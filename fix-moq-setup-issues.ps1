#!/usr/bin/env pwsh

# Fix Moq setup issues in test files
Write-Host "Fixing Moq setup issues in test files..." -ForegroundColor Yellow

$testFiles = Get-ChildItem -Path "SmaTrendFollower.Tests" -Recurse -Filter "*.cs" | Where-Object { $_.Name -like "*Tests.cs" }

foreach ($file in $testFiles) {
    $content = Get-Content $file.FullName -Raw
    $originalContent = $content
    
    Write-Host "Processing $($file.Name)..." -ForegroundColor Cyan
    
    # Fix GetIndexBarsAsync ReturnsAsync issues
    $content = $content -replace '\.ReturnsAsync\(([^)]+)\.Select\(b => new IndexBar\([^)]+\)\)\);', '.ReturnsAsync($1.Select(b => new IndexBar(b.Timestamp, b.Open, b.High, b.Low, b.Close, b.Volume)).ToList());'
    
    # Fix simple ReturnsAsync with IIndexBar collections
    $content = $content -replace '\.ReturnsAsync\(([^)]+bars)\);', '.ReturnsAsync($1.Select(b => new IndexBar(b.Timestamp, b.Open, b.High, b.<PERSON>, b.Close, b.Volume)).ToList());'
    
    # Fix empty IIndexBar list returns
    $content = $content -replace '\.ReturnsAsync\(new List<IIndexBar>\(\)\);', '.ReturnsAsync(new List<IndexBar>());'
    
    # Fix BeCloseTo issues with nullable double
    $content = $content -replace '\.BeCloseTo\(([^,]+), ([^)]+)\);', '.BeCloseTo((double)$1, $2);'
    
    # Fix OrderType vs SmaOrderType issues
    $content = $content -replace 'OrderType\.([A-Za-z]+)', 'SmaOrderType.$1'
    $content = $content -replace '== OrderType\.', '== SmaOrderType.'
    
    # Fix IBar.Timestamp property access (should be TimeUtc)
    $content = $content -replace '\.Timestamp', '.TimeUtc'
    
    # Fix PolygonBar constructor issues (7 parameters to proper constructor)
    $content = $content -replace 'new PolygonBar\(([^,]+), ([^,]+), ([^,]+), ([^,]+), ([^,]+), ([^,]+), ([^)]+)\)', 'new PolygonBar { Open = $2, High = $3, Low = $4, Close = $5, Volume = $6, TimeUtc = $1 }'
    
    # Fix PolygonBarWrapper constructor (needs symbol parameter)
    $content = $content -replace 'new PolygonBarWrapper\(([^)]+)\)', 'new PolygonBarWrapper($1, "TEST")'
    
    # Fix Redis CommandFlags parameter issues
    $content = $content -replace ', CommandFlags\.[A-Za-z]+\)', ', (int)CommandFlags.None)'
    
    # Fix GetBarsAsync method name (should be GetStockBarsAsync)
    $content = $content -replace '\.GetBarsAsync\(', '.GetStockBarsAsync('
    
    # Fix IVWAPData type issues
    $content = $content -replace 'IVWAPData', 'var'
    
    # Fix expression tree issues with optional arguments
    $content = $content -replace 'It\.IsAny<([^>]+)>\(\)', 'It.IsAny<$1>()'
    
    if ($content -ne $originalContent) {
        Set-Content $file.FullName -Value $content -NoNewline
        Write-Host "  Fixed Moq issues in $($file.Name)" -ForegroundColor Green
    } else {
        Write-Host "  No changes needed in $($file.Name)" -ForegroundColor Gray
    }
}

Write-Host "Moq setup fixes completed!" -ForegroundColor Green
