using SmaTrendFollower.Services;
using Alpaca.Markets;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using DotNetEnv;

namespace SmaTrendFollower.Console;

public static class AccountChecker
{
    public static async Task CheckAccountAsync()
    {
        // Load environment variables from the correct path
        var envPath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, ".env");
        if (!File.Exists(envPath))
        {
            // Try relative path
            envPath = ".env";
        }

        System.Console.WriteLine($"Looking for .env file at: {Path.GetFullPath(envPath)}");
        System.Console.WriteLine($"File exists: {File.Exists(envPath)}");

        if (File.Exists(envPath))
        {
            Env.Load(envPath);
            System.Console.WriteLine("Environment variables loaded from .env file");

            // Force reload by reading the file manually if DotNetEnv isn't working
            var lines = File.ReadAllLines(envPath);
            foreach (var line in lines)
            {
                if (line.StartsWith("#") || string.IsNullOrWhiteSpace(line)) continue;

                var parts = line.Split('=', 2);
                if (parts.Length == 2)
                {
                    Environment.SetEnvironmentVariable(parts[0].Trim(), parts[1].Trim());
                }
            }
        }
        else
        {
            System.Console.WriteLine("Warning: .env file not found. Make sure environment variables are set.");
        }

        // Debug: Check if environment variables are set
        var keyId = Environment.GetEnvironmentVariable("APCA_API_KEY_ID");
        var secretKey = Environment.GetEnvironmentVariable("APCA_API_SECRET_KEY");
        System.Console.WriteLine($"APCA_API_KEY_ID set: {!string.IsNullOrEmpty(keyId)}");
        System.Console.WriteLine($"APCA_API_SECRET_KEY set: {!string.IsNullOrEmpty(secretKey)}");
        System.Console.WriteLine();
        
        // Create a simple logger
        using var loggerFactory = LoggerFactory.Create(builder => builder.AddConsole());
        var logger = loggerFactory.CreateLogger<AlpacaClientFactory>();
        var rateLimitLogger = loggerFactory.CreateLogger<AlpacaRateLimitHelper>();

        try
        {
            // Create configuration
            var configuration = new ConfigurationBuilder()
                .AddJsonFile("appsettings.json", optional: true)
                .AddJsonFile($"appsettings.{Environment.GetEnvironmentVariable("ASPNETCORE_ENVIRONMENT") ?? "Production"}.json", optional: true)
                .AddEnvironmentVariables()
                .Build();

            // Create client factory
            var clientFactory = new AlpacaClientFactory(logger, rateLimitLogger, configuration);

            // Try both environments to see which one works
            System.Console.WriteLine("Testing Paper Trading Environment...");
            Environment.SetEnvironmentVariable("APCA_API_ENV", "paper");

            IAccount? account = null;
            string workingEnvironment = "";

            try
            {
                using var paperClient = clientFactory.CreateTradingClient();
                account = await paperClient.GetAccountAsync();
                workingEnvironment = "paper";
                System.Console.WriteLine("✓ Paper trading environment works!");
            }
            catch (Exception paperEx)
            {
                System.Console.WriteLine($"✗ Paper trading failed: {paperEx.Message}");

                System.Console.WriteLine("Testing Live Trading Environment...");
                Environment.SetEnvironmentVariable("APCA_API_ENV", "live");

                try
                {
                    using var liveClient = clientFactory.CreateTradingClient();
                    account = await liveClient.GetAccountAsync();
                    workingEnvironment = "live";
                    System.Console.WriteLine("✓ Live trading environment works!");
                }
                catch (Exception liveEx)
                {
                    System.Console.WriteLine($"✗ Live trading failed: {liveEx.Message}");
                    throw new InvalidOperationException("Neither paper nor live trading environments work with these credentials");
                }
            }

            if (account == null)
            {
                throw new InvalidOperationException("Could not retrieve account information");
            }
            
            System.Console.WriteLine($"=== ALPACA ACCOUNT STATUS ({workingEnvironment.ToUpper()}) ===");
            System.Console.WriteLine($"Account ID: {account.AccountId}");
            System.Console.WriteLine($"Status: {account.Status}");
            System.Console.WriteLine();

            System.Console.WriteLine("=== ACCOUNT EQUITY ===");
            System.Console.WriteLine($"Equity: {account.Equity:C}");
            System.Console.WriteLine($"Cash: {account.TradableCash:C}");
            System.Console.WriteLine($"Buying Power: {account.BuyingPower:C}");
            System.Console.WriteLine();
            
            System.Console.WriteLine("=== RISK CALCULATIONS ===");
            var equity = account.Equity ?? 0m;
            var riskDollars = Math.Min(equity * 0.01m, 1000m);
            System.Console.WriteLine($"Current Risk Capital (1% of equity, max $1000): {riskDollars:C}");
            System.Console.WriteLine($"Risk as % of equity: {(riskDollars / equity * 100):F2}%");
            System.Console.WriteLine();
            
            // Get current positions using the working environment
            using var workingClient = clientFactory.CreateTradingClient();
            var positions = await workingClient.ListPositionsAsync();
            var positionList = positions.ToList();
            
            System.Console.WriteLine("=== CURRENT POSITIONS ===");
            if (positionList.Any())
            {
                foreach (var position in positionList)
                {
                    var marketValue = position.MarketValue ?? 0m;
                    var unrealizedPnl = position.UnrealizedProfitLoss ?? 0m;
                    var unrealizedPnlPercent = position.UnrealizedProfitLossPercent ?? 0m;
                    
                    System.Console.WriteLine($"{position.Symbol}: {position.Quantity} shares");
                    System.Console.WriteLine($"  Market Value: {marketValue:C}");
                    System.Console.WriteLine($"  Unrealized P&L: {unrealizedPnl:C} ({unrealizedPnlPercent:P2})");
                    System.Console.WriteLine($"  Avg Cost: {position.AverageEntryPrice:C}");
                    System.Console.WriteLine();
                }
            }
            else
            {
                System.Console.WriteLine("No current positions");
            }
            
            // Get recent orders
            var orders = await workingClient.ListOrdersAsync(new ListOrdersRequest
            {
                OrderStatusFilter = OrderStatusFilter.All,
                LimitOrderNumber = 10
            });
            var orderList = orders.ToList();
            
            System.Console.WriteLine("=== RECENT ORDERS (Last 10) ===");
            if (orderList.Any())
            {
                foreach (var order in orderList.Take(10))
                {
                    System.Console.WriteLine($"{order.CreatedAtUtc:yyyy-MM-dd HH:mm} - {order.Symbol}");
                    System.Console.WriteLine($"  {order.OrderSide} {order.Quantity} @ {order.LimitPrice:C}");
                    System.Console.WriteLine($"  Status: {order.OrderStatus}");
                    System.Console.WriteLine();
                }
            }
            else
            {
                System.Console.WriteLine("No recent orders");
            }
        }
        catch (Exception ex)
        {
            System.Console.WriteLine($"Error checking account: {ex.Message}");
            System.Console.WriteLine($"Details: {ex}");
        }
    }
}
