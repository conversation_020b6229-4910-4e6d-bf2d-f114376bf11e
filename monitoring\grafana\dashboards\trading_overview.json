{"id": null, "title": "Trading Overview", "tags": ["sma-trend-follower", "trading"], "timezone": "browser", "refresh": "30s", "time": {"from": "now-6h", "to": "now"}, "panels": [{"id": 1, "type": "timeseries", "title": "Cumulative PnL", "targets": [{"expr": "daily_pnl_usd", "legendFormat": "Daily P&L ($)", "refId": "A"}, {"expr": "portfolio_value_usd", "legendFormat": "Portfolio Value ($)", "refId": "B"}], "datasource": {"type": "prometheus", "uid": "prometheus"}, "gridPos": {"x": 0, "y": 0, "w": 12, "h": 8}, "fieldConfig": {"defaults": {"unit": "currencyUSD", "custom": {"drawStyle": "line", "lineInterpolation": "linear", "lineWidth": 2, "fillOpacity": 10, "gradientMode": "none", "spanNulls": false, "insertNulls": false, "showPoints": "never", "pointSize": 5, "stacking": {"mode": "none", "group": "A"}, "axisPlacement": "auto", "axisLabel": "", "axisColorMode": "text", "scaleDistribution": {"type": "linear"}, "axisCenteredZero": false, "hideFrom": {"legend": false, "tooltip": false, "vis": false}, "thresholdsStyle": {"mode": "off"}}, "color": {"mode": "palette-classic"}, "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": -1000}]}}}, "options": {"tooltip": {"mode": "single", "sort": "none"}, "legend": {"displayMode": "visible", "placement": "bottom", "calcs": ["lastNotNull"]}}}, {"id": 2, "type": "stat", "title": "Universe Size", "targets": [{"expr": "universe_size", "legendFormat": "Symbols", "refId": "A"}], "datasource": {"type": "prometheus", "uid": "prometheus"}, "gridPos": {"x": 12, "y": 0, "w": 6, "h": 4}, "fieldConfig": {"defaults": {"unit": "short", "color": {"mode": "thresholds"}, "thresholds": {"mode": "absolute", "steps": [{"color": "red", "value": null}, {"color": "yellow", "value": 50}, {"color": "green", "value": 100}]}}}, "options": {"reduceOptions": {"values": false, "calcs": ["lastNotNull"], "fields": ""}, "orientation": "auto", "textMode": "auto", "colorMode": "background", "graphMode": "area", "justifyMode": "auto"}}, {"id": 3, "type": "stat", "title": "Current Regime", "targets": [{"expr": "market_regime", "legendFormat": "Regime", "refId": "A"}], "datasource": {"type": "prometheus", "uid": "prometheus"}, "gridPos": {"x": 12, "y": 4, "w": 6, "h": 4}, "fieldConfig": {"defaults": {"unit": "short", "color": {"mode": "thresholds"}, "thresholds": {"mode": "absolute", "steps": [{"color": "blue", "value": null}, {"color": "green", "value": 1}, {"color": "yellow", "value": 2}, {"color": "red", "value": 3}]}, "mappings": [{"options": {"0": {"text": "Sideways", "color": "blue"}, "1": {"text": "Trending Up", "color": "green"}, "2": {"text": "Trending Down", "color": "yellow"}, "3": {"text": "Panic", "color": "red"}}, "type": "value"}]}}, "options": {"reduceOptions": {"values": false, "calcs": ["lastNotNull"], "fields": ""}, "orientation": "auto", "textMode": "auto", "colorMode": "background", "graphMode": "none", "justifyMode": "auto"}}, {"id": 4, "type": "timeseries", "title": "Trading Activity", "targets": [{"expr": "rate(trades_total[5m])", "legendFormat": "Trades/min ({{side}})", "refId": "A"}, {"expr": "rate(signals_total[5m])", "legendFormat": "Signals/min", "refId": "B"}], "datasource": {"type": "prometheus", "uid": "prometheus"}, "gridPos": {"x": 0, "y": 8, "w": 12, "h": 6}, "fieldConfig": {"defaults": {"unit": "reqps", "custom": {"drawStyle": "line", "lineInterpolation": "linear", "lineWidth": 1, "fillOpacity": 10, "gradientMode": "none", "spanNulls": false, "insertNulls": false, "showPoints": "never", "pointSize": 5, "stacking": {"mode": "none", "group": "A"}, "axisPlacement": "auto", "axisLabel": "", "axisColorMode": "text", "scaleDistribution": {"type": "linear"}, "axisCenteredZero": false, "hideFrom": {"legend": false, "tooltip": false, "vis": false}, "thresholdsStyle": {"mode": "off"}}, "color": {"mode": "palette-classic"}}}, "options": {"tooltip": {"mode": "multi", "sort": "none"}, "legend": {"displayMode": "visible", "placement": "bottom"}}}, {"id": 5, "type": "stat", "title": "Current Positions", "targets": [{"expr": "current_positions", "legendFormat": "Open Positions", "refId": "A"}], "datasource": {"type": "prometheus", "uid": "prometheus"}, "gridPos": {"x": 12, "y": 8, "w": 6, "h": 3}, "fieldConfig": {"defaults": {"unit": "short", "color": {"mode": "thresholds"}, "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "yellow", "value": 8}, {"color": "red", "value": 10}]}}}, "options": {"reduceOptions": {"values": false, "calcs": ["lastNotNull"], "fields": ""}, "orientation": "auto", "textMode": "auto", "colorMode": "background", "graphMode": "area", "justifyMode": "auto"}}, {"id": 6, "type": "stat", "title": "Current VIX", "targets": [{"expr": "current_vix", "legendFormat": "VIX", "refId": "A"}], "datasource": {"type": "prometheus", "uid": "prometheus"}, "gridPos": {"x": 12, "y": 11, "w": 6, "h": 3}, "fieldConfig": {"defaults": {"unit": "short", "decimals": 2, "color": {"mode": "thresholds"}, "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "yellow", "value": 20}, {"color": "red", "value": 30}]}}}, "options": {"reduceOptions": {"values": false, "calcs": ["lastNotNull"], "fields": ""}, "orientation": "auto", "textMode": "auto", "colorMode": "background", "graphMode": "area", "justifyMode": "auto"}}], "schemaVersion": 37, "version": 1, "uid": "sma-trading-overview", "editable": true, "fiscalYearStartMonth": 0, "graphTooltip": 0, "links": [], "liveNow": false, "style": "dark", "templating": {"list": []}, "weekStart": ""}