using SmaTrendFollower.Models;

namespace SmaTrendFollower.Services;

/// <summary>
/// Interface for the wheel options strategy engine
/// Manages cash-secured puts and covered calls in a systematic cycle
/// </summary>
public interface IWheelStrategyEngine
{
    /// <summary>
    /// Evaluates and executes the next wheel cycle for a symbol
    /// </summary>
    Task<WheelCycleResult> RunWheelCycleAsync(string symbol, CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets current wheel positions for all symbols
    /// </summary>
    Task<IEnumerable<WheelPosition>> GetCurrentPositionsAsync();

    /// <summary>
    /// Gets current wheel position for a specific symbol
    /// </summary>
    Task<WheelPosition?> GetPositionAsync(string symbol);

    /// <summary>
    /// Manages existing wheel positions (rolling, closing, etc.)
    /// </summary>
    Task ManageExistingPositionsAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// Evaluates if a symbol is suitable for wheel strategy
    /// </summary>
    Task<bool> IsSymbolSuitableForWheelAsync(string symbol);

    /// <summary>
    /// Gets wheel strategy performance metrics
    /// </summary>
    Task<WheelPerformanceMetrics> GetPerformanceMetricsAsync();

    /// <summary>
    /// Updates wheel strategy configuration
    /// </summary>
    Task UpdateConfigurationAsync(WheelStrategyConfig config);
}

/// <summary>
/// Represents a wheel position for a symbol
/// </summary>
public record WheelPosition(
    string Symbol,
    WheelPositionType Type,
    decimal Quantity,
    decimal Strike,
    DateTime Expiration,
    string OptionSymbol,
    decimal Premium,
    decimal UnrealizedPnL,
    DateTime CreatedAt,
    WheelPositionStatus Status,
    decimal? AssignmentPrice = null,
    DateTime? AssignmentDate = null
);

/// <summary>
/// Result of a wheel cycle execution
/// </summary>
public record WheelCycleResult(
    string Symbol,
    WheelAction Action,
    bool Success,
    string Reason,
    decimal? Premium = null,
    string? OptionSymbol = null,
    decimal? Strike = null,
    DateTime? Expiration = null,
    decimal? Quantity = null
);

/// <summary>
/// Wheel strategy performance metrics
/// </summary>
public record WheelPerformanceMetrics(
    decimal TotalPremiumCollected,
    decimal UnrealizedPnL,
    decimal RealizedPnL,
    int ActivePositions,
    int CompletedCycles,
    decimal AnnualizedReturn,
    decimal WinRate,
    decimal AveragePremiumPerCycle,
    DateTime LastUpdated
);

/// <summary>
/// Configuration for wheel strategy
/// </summary>
public class WheelStrategyConfig
{
    public bool Enabled { get; set; } = true;
    public decimal MaxAllocationPercent { get; set; } = 0.20m;
    public decimal MinPremiumPercent { get; set; } = 0.01m;
    public int MinDaysToExpiration { get; set; } = 7;
    public int MaxDaysToExpiration { get; set; } = 45;
    public decimal MaxDeltaForPuts { get; set; } = 0.30m;
    public decimal MaxDeltaForCalls { get; set; } = 0.30m;
    public decimal MinLiquidity { get; set; } = 100m;
    public decimal MaxBidAskSpreadPercent { get; set; } = 0.05m;
    public bool EnableRolling { get; set; } = true;
    public decimal RollThreshold { get; set; } = 0.50m;
    public int MaxRollAttempts { get; set; } = 2;
    public bool RequireHighIV { get; set; } = true;
    public decimal MinIVPercentile { get; set; } = 30m;
    public IReadOnlyList<string>? AllowedSymbols { get; set; } = null;
    public IReadOnlyList<string>? ExcludedSymbols { get; set; } = null;
}

/// <summary>
/// Type of wheel position
/// </summary>
public enum WheelPositionType
{
    CashSecuredPut,
    CoveredCall
}

/// <summary>
/// Status of wheel position
/// </summary>
public enum WheelPositionStatus
{
    Active,
    Assigned,
    Expired,
    Closed,
    Rolled
}

/// <summary>
/// Action taken in wheel cycle
/// </summary>
public enum WheelAction
{
    SellCashSecuredPut,
    SellCoveredCall,
    RollPosition,
    ClosePosition,
    NoAction,
    WaitForAssignment,
    WaitForExpiration
}
