<!doctype html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>SmaTrendFollower - Back-Test Dashboard</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
            color: #333;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            padding: 30px;
        }
        h1 {
            color: #2c3e50;
            text-align: center;
            margin-bottom: 30px;
            border-bottom: 3px solid #3498db;
            padding-bottom: 10px;
        }
        h2 {
            color: #34495e;
            margin-top: 30px;
            margin-bottom: 15px;
        }
        .chart-container {
            text-align: center;
            margin: 30px 0;
            padding: 20px;
            background: #fafafa;
            border-radius: 6px;
            border: 1px solid #e0e0e0;
        }
        .chart-container img {
            max-width: 100%;
            height: auto;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        .metrics-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        .metric-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 8px;
            text-align: center;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }
        .metric-label {
            font-size: 14px;
            opacity: 0.9;
            margin-bottom: 8px;
        }
        .metric-value {
            font-size: 24px;
            font-weight: bold;
        }
        .positive { color: #27ae60; }
        .negative { color: #e74c3c; }
        .status {
            padding: 10px;
            border-radius: 4px;
            margin-bottom: 20px;
            text-align: center;
        }
        .status.loading {
            background: #f39c12;
            color: white;
        }
        .status.success {
            background: #27ae60;
            color: white;
        }
        .status.error {
            background: #e74c3c;
            color: white;
        }
        .refresh-info {
            text-align: center;
            color: #7f8c8d;
            font-size: 12px;
            margin-top: 20px;
        }
        .no-data {
            text-align: center;
            color: #7f8c8d;
            font-style: italic;
            padding: 40px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>📈 SmaTrendFollower Back-Test Dashboard</h1>
        
        <div id="status" class="status loading">Loading backtest results...</div>
        
        <div id="content" style="display: none;">
            <div class="chart-container">
                <h2>Equity Curve</h2>
                <img id="equity-chart" src="/backtest/curve.png" alt="Equity Curve" 
                     onerror="this.style.display='none'; document.getElementById('no-chart').style.display='block';">
                <div id="no-chart" class="no-data" style="display: none;">
                    Chart not available - run a backtest to generate equity curve
                </div>
            </div>

            <h2>Performance Summary</h2>
            <div class="metrics-grid">
                <div class="metric-card">
                    <div class="metric-label">Total P&L</div>
                    <div class="metric-value" id="pnl">–</div>
                </div>
                <div class="metric-card">
                    <div class="metric-label">Sharpe Ratio</div>
                    <div class="metric-value" id="sharpe">–</div>
                </div>
                <div class="metric-card">
                    <div class="metric-label">Max Drawdown</div>
                    <div class="metric-value" id="dd">–</div>
                </div>
                <div class="metric-card">
                    <div class="metric-label">Total Trades</div>
                    <div class="metric-value" id="trades">–</div>
                </div>
                <div class="metric-card">
                    <div class="metric-label">Win Rate</div>
                    <div class="metric-value" id="win">–</div>
                </div>
                <div class="metric-card">
                    <div class="metric-label">Final Capital</div>
                    <div class="metric-value" id="final-capital">–</div>
                </div>
            </div>

            <h2>Backtest Details</h2>
            <div class="metrics-grid">
                <div class="metric-card">
                    <div class="metric-label">Start Date</div>
                    <div class="metric-value" id="start-date">–</div>
                </div>
                <div class="metric-card">
                    <div class="metric-label">End Date</div>
                    <div class="metric-value" id="end-date">–</div>
                </div>
                <div class="metric-card">
                    <div class="metric-label">Initial Capital</div>
                    <div class="metric-value" id="initial-capital">–</div>
                </div>
                <div class="metric-card">
                    <div class="metric-label">Symbols</div>
                    <div class="metric-value" id="symbols">–</div>
                </div>
            </div>
        </div>

        <div id="no-data" class="no-data" style="display: none;">
            No backtest results available. Run a backtest to see results here.
        </div>

        <div class="refresh-info">
            Dashboard auto-refreshes every 5 seconds | Last updated: <span id="last-update">–</span>
        </div>
    </div>

    <script>
        let refreshInterval;

        async function refreshData() {
            try {
                const response = await fetch('/Backtest/summary');
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}`);
                }
                
                const data = await response.json();
                
                if (data.trades === 0 && data.totalPnl === 0) {
                    showNoData();
                    return;
                }

                updateDisplay(data);
                showContent();
                updateStatus('success', 'Data loaded successfully');
                
            } catch (error) {
                console.error('Error fetching data:', error);
                updateStatus('error', `Error loading data: ${error.message}`);
            }
            
            document.getElementById('last-update').textContent = new Date().toLocaleTimeString();
        }

        function updateDisplay(data) {
            // Performance metrics
            document.getElementById('pnl').textContent = formatCurrency(data.totalPnl);
            document.getElementById('pnl').className = 'metric-value ' + (data.totalPnl >= 0 ? 'positive' : 'negative');
            
            document.getElementById('sharpe').textContent = data.sharpe.toFixed(2);
            document.getElementById('dd').textContent = (data.maxDrawdown * 100).toFixed(1) + '%';
            document.getElementById('trades').textContent = data.trades.toLocaleString();
            document.getElementById('win').textContent = (data.winRate * 100).toFixed(1) + '%';
            document.getElementById('final-capital').textContent = formatCurrency(data.finalCapital);

            // Backtest details
            document.getElementById('start-date').textContent = formatDate(data.startDate);
            document.getElementById('end-date').textContent = formatDate(data.endDate);
            document.getElementById('initial-capital').textContent = formatCurrency(data.initialCapital);
            document.getElementById('symbols').textContent = data.symbols.join(', ');

            // Refresh chart with cache busting
            const chart = document.getElementById('equity-chart');
            chart.src = `/backtest/curve.png?t=${Date.now()}`;
        }

        function formatCurrency(value) {
            return new Intl.NumberFormat('en-US', {
                style: 'currency',
                currency: 'USD',
                minimumFractionDigits: 0,
                maximumFractionDigits: 0
            }).format(value);
        }

        function formatDate(dateString) {
            return new Date(dateString).toLocaleDateString('en-US', {
                year: 'numeric',
                month: 'short',
                day: 'numeric'
            });
        }

        function updateStatus(type, message) {
            const status = document.getElementById('status');
            status.className = `status ${type}`;
            status.textContent = message;
        }

        function showContent() {
            document.getElementById('content').style.display = 'block';
            document.getElementById('no-data').style.display = 'none';
        }

        function showNoData() {
            document.getElementById('content').style.display = 'none';
            document.getElementById('no-data').style.display = 'block';
            updateStatus('success', 'No backtest results found');
        }

        // Initialize
        window.onload = function() {
            refreshData();
            refreshInterval = setInterval(refreshData, 5000);
        };

        // Cleanup on page unload
        window.onbeforeunload = function() {
            if (refreshInterval) {
                clearInterval(refreshInterval);
            }
        };
    </script>
</body>
</html>
