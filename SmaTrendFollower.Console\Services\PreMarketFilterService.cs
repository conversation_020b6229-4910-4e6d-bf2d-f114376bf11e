using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using SmaTrendFollower.Models;
using StackExchange.Redis;
using System.Collections.Concurrent;
using System.Text.Json;

namespace SmaTrendFollower.Services;

/// <summary>
/// Pre-market filter service implementation
/// Analyzes pre-market activity using Polygon API to identify high-risk trading conditions
/// </summary>
public sealed class PreMarketFilterService : IPreMarketFilterService, IDisposable
{
    private readonly IPolygonClientFactory _polygonFactory;
    private readonly IOptimizedRedisConnectionService _redisService;
    private readonly IMarketSessionGuard _sessionGuard;
    private readonly ILogger<PreMarketFilterService> _logger;
    private readonly PreMarketFilterConfig _config;
    
    private readonly ConcurrentDictionary<string, PreMarketAnalysis> _analysisCache = new();
    private readonly SemaphoreSlim _analysisLock = new(1, 1);
    private bool _disposed;

    // Redis key patterns
    private const string AnalysisCacheKeyPattern = "premarket:analysis:{0}:{1:yyyyMMdd}";
    private const string VolumeHistoryKeyPattern = "premarket:volume_history:{0}";

    public PreMarketFilterService(
        IPolygonClientFactory polygonFactory,
        IOptimizedRedisConnectionService redisService,
        IMarketSessionGuard sessionGuard,
        IConfiguration configuration,
        ILogger<PreMarketFilterService> logger)
    {
        _polygonFactory = polygonFactory ?? throw new ArgumentNullException(nameof(polygonFactory));
        _redisService = redisService ?? throw new ArgumentNullException(nameof(redisService));
        _sessionGuard = sessionGuard ?? throw new ArgumentNullException(nameof(sessionGuard));
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));

        _config = new PreMarketFilterConfig();
        configuration.GetSection("PreMarketFilter").Bind(_config);
    }

    // === Events ===
    
    public event EventHandler<PreMarketAnalysisEventArgs>? AnalysisCompleted;
    public event EventHandler<TradingBlockEventArgs>? TradingBlocked;

    // === Public Methods ===

    public async Task<PreMarketAnalysis> AnalyzePreMarketConditionsAsync(string symbol, CancellationToken cancellationToken = default)
    {
        if (_disposed)
            throw new ObjectDisposedException(nameof(PreMarketFilterService));

        if (string.IsNullOrWhiteSpace(symbol))
            throw new ArgumentException("Symbol cannot be null or empty", nameof(symbol));

        await _analysisLock.WaitAsync(cancellationToken);
        try
        {
            // Check cache first
            var cached = await GetCachedAnalysisAsync(symbol);
            if (cached.HasValue && IsAnalysisValid(cached.Value))
            {
                _logger.LogDebug("Using cached pre-market analysis for {Symbol}", symbol);
                return cached.Value;
            }

            _logger.LogInformation("Analyzing pre-market conditions for {Symbol}", symbol);

            // Get pre-market bars from Polygon
            var preMarketBars = await FetchPreMarketBarsAsync(symbol, cancellationToken);
            if (preMarketBars.Count < _config.MinimumBarsRequired)
            {
                var insufficientDataAnalysis = CreateInsufficientDataAnalysis(symbol);
                await CacheAnalysisAsync(insufficientDataAnalysis);
                return insufficientDataAnalysis;
            }

            // Get previous close for gap calculation
            var previousClose = await GetPreviousCloseAsync(symbol, cancellationToken);
            
            // Get average volume for anomaly detection
            var averageVolume = await GetAverageVolumeAsync(symbol, cancellationToken);

            // Perform analysis
            var analysis = PerformPreMarketAnalysis(symbol, preMarketBars, previousClose, averageVolume);
            
            // Cache the result
            await CacheAnalysisAsync(analysis);
            
            // Fire events
            AnalysisCompleted?.Invoke(this, new PreMarketAnalysisEventArgs { Analysis = analysis });
            
            if (analysis.ShouldBlockTrading)
            {
                TradingBlocked?.Invoke(this, new TradingBlockEventArgs
                {
                    Symbol = symbol,
                    Reason = analysis.BlockingReason,
                    RiskLevel = analysis.RiskLevel,
                    BlockTime = DateTime.UtcNow
                });
            }

            _logger.LogInformation("Pre-market analysis completed for {Symbol}: Risk={RiskLevel}, Block={ShouldBlock}, Reason={Reason}",
                symbol, analysis.RiskLevel, analysis.ShouldBlockTrading, analysis.BlockingReason);

            return analysis;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error analyzing pre-market conditions for {Symbol}", symbol);
            
            // Return safe default (block trading on error)
            var errorAnalysis = new PreMarketAnalysis(
                symbol,
                DateTime.UtcNow,
                true, // Block trading on error
                PreMarketRiskLevel.Extreme,
                0m,
                0m,
                0m,
                $"Analysis error: {ex.Message}",
                new PreMarketMetrics(0m, 0m, 0m, 0m, 0L, 0L, 0m, 0)
            );
            
            return errorAnalysis;
        }
        finally
        {
            _analysisLock.Release();
        }
    }

    public async Task<bool> ShouldBlockTradingAsync(string symbol, CancellationToken cancellationToken = default)
    {
        var analysis = await AnalyzePreMarketConditionsAsync(symbol, cancellationToken);
        return analysis.ShouldBlockTrading;
    }

    public async Task<IDictionary<string, PreMarketAnalysis>> AnalyzeBatchAsync(IEnumerable<string> symbols, CancellationToken cancellationToken = default)
    {
        var symbolList = symbols.ToList();
        _logger.LogInformation("Analyzing pre-market conditions for {Count} symbols", symbolList.Count);

        var tasks = symbolList.Select(async symbol =>
        {
            try
            {
                var analysis = await AnalyzePreMarketConditionsAsync(symbol, cancellationToken);
                return new KeyValuePair<string, PreMarketAnalysis>(symbol, analysis);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error analyzing pre-market conditions for {Symbol}", symbol);
                var errorAnalysis = new PreMarketAnalysis(
                    symbol,
                    DateTime.UtcNow,
                    true,
                    PreMarketRiskLevel.Extreme,
                    0m,
                    0m,
                    0m,
                    $"Batch analysis error: {ex.Message}",
                    new PreMarketMetrics(0m, 0m, 0m, 0m, 0L, 0L, 0m, 0)
                );
                return new KeyValuePair<string, PreMarketAnalysis>(symbol, errorAnalysis);
            }
        });

        var results = await Task.WhenAll(tasks);
        return results.ToDictionary(kvp => kvp.Key, kvp => kvp.Value);
    }

    public async Task<PreMarketAnalysis?> GetCachedAnalysisAsync(string symbol)
    {
        try
        {
            // Check in-memory cache first
            if (_analysisCache.TryGetValue(symbol, out var memoryAnalysis) && IsAnalysisValid(memoryAnalysis))
            {
                return memoryAnalysis;
            }

            // Check Redis cache
            var database = await _redisService.GetDatabaseAsync();
            var key = string.Format(AnalysisCacheKeyPattern, symbol, DateTime.Today);
            var value = await database.StringGetAsync(key);
            
            if (value.HasValue)
            {
                var analysis = JsonSerializer.Deserialize<PreMarketAnalysis>(value!);
                if (IsAnalysisValid(analysis))
                {
                    _analysisCache.TryAdd(symbol, analysis);
                    return analysis;
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Error retrieving cached analysis for {Symbol}", symbol);
        }

        return null;
    }

    public async Task ClearCacheAsync()
    {
        try
        {
            _analysisCache.Clear();
            
            var database = await _redisService.GetDatabaseAsync();
            var server = database.Multiplexer.GetServer(database.Multiplexer.GetEndPoints().First());
            
            await foreach (var key in server.KeysAsync(pattern: "premarket:analysis:*"))
            {
                await database.KeyDeleteAsync(key);
            }
            
            _logger.LogInformation("Pre-market analysis cache cleared");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error clearing pre-market analysis cache");
        }
    }

    // === Private Methods ===

    private async Task<List<PolygonBar>> FetchPreMarketBarsAsync(string symbol, CancellationToken cancellationToken)
    {
        try
        {
            var httpClient = _polygonFactory.CreateClient();
            var today = DateTime.Today;
            var preMarketStart = today.Add(_config.PreMarketStartTime.ToTimeSpan());
            var preMarketEnd = today.Add(_config.PreMarketEndTime.ToTimeSpan());

            // Convert to Unix timestamps (milliseconds)
            var startTimestamp = ((DateTimeOffset)preMarketStart).ToUnixTimeMilliseconds();
            var endTimestamp = ((DateTimeOffset)preMarketEnd).ToUnixTimeMilliseconds();

            var url = $"/v2/aggs/ticker/{symbol}/range/1/minute/{startTimestamp}/{endTimestamp}";
            url = _polygonFactory.AddApiKeyToUrl(url);

            _logger.LogDebug("Fetching pre-market bars for {Symbol}: {Url}", symbol, url);

            var response = await httpClient.GetAsync(url, cancellationToken);
            response.EnsureSuccessStatusCode();

            var content = await response.Content.ReadAsStringAsync(cancellationToken);
            var polygonResponse = JsonSerializer.Deserialize<PolygonAggregatesResponse>(content);

            if (polygonResponse?.Results == null || !polygonResponse.Results.Any())
            {
                _logger.LogWarning("No pre-market bars found for {Symbol}", symbol);
                return new List<PolygonBar>();
            }

            var bars = polygonResponse.Results.Select(r => new PolygonBar
            {
                Open = r.Open,
                High = r.High,
                Low = r.Low,
                Close = r.Close,
                Volume = r.Volume,
                Timestamp = r.Timestamp,
                VolumeWeightedAveragePrice = r.VolumeWeightedAveragePrice,
                NumberOfTransactions = r.NumberOfTransactions
            }).ToList();

            _logger.LogDebug("Retrieved {Count} pre-market bars for {Symbol}", bars.Count, symbol);
            return bars;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error fetching pre-market bars for {Symbol}", symbol);
            return new List<PolygonBar>();
        }
    }

    private async Task<decimal> GetPreviousCloseAsync(string symbol, CancellationToken cancellationToken)
    {
        try
        {
            var httpClient = _polygonFactory.CreateClient();
            var yesterday = DateTime.Today.AddDays(-1);

            // Get previous day's close
            var url = $"/v1/open-close/{symbol}/{yesterday:yyyy-MM-dd}";
            url = _polygonFactory.AddApiKeyToUrl(url);

            var response = await httpClient.GetAsync(url, cancellationToken);
            if (!response.IsSuccessStatusCode)
            {
                _logger.LogWarning("Could not fetch previous close for {Symbol}, status: {StatusCode}", symbol, response.StatusCode);
                return 0m;
            }

            var content = await response.Content.ReadAsStringAsync(cancellationToken);
            var openCloseResponse = JsonSerializer.Deserialize<PolygonOpenCloseResponse>(content);

            return openCloseResponse?.Close ?? 0m;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error fetching previous close for {Symbol}", symbol);
            return 0m;
        }
    }

    private async Task<long> GetAverageVolumeAsync(string symbol, CancellationToken cancellationToken)
    {
        try
        {
            // Try to get from Redis cache first
            var database = await _redisService.GetDatabaseAsync();
            var key = string.Format(VolumeHistoryKeyPattern, symbol);
            var cachedVolumes = await database.ListRangeAsync(key);

            if (cachedVolumes.Length >= 20) // Use cached if we have enough data
            {
                var cachedVolumeList = cachedVolumes.Select(v => long.Parse(v!)).ToList();
                return (long)cachedVolumeList.Average();
            }

            // Fetch from Polygon API
            var httpClient = _polygonFactory.CreateClient();
            var endDate = DateTime.Today.AddDays(-1);
            var startDate = endDate.AddDays(-30); // Get 30 days of data

            var url = $"/v2/aggs/ticker/{symbol}/range/1/day/{startDate:yyyy-MM-dd}/{endDate:yyyy-MM-dd}";
            url = _polygonFactory.AddApiKeyToUrl(url);

            var response = await httpClient.GetAsync(url, cancellationToken);
            response.EnsureSuccessStatusCode();

            var content = await response.Content.ReadAsStringAsync(cancellationToken);
            var polygonResponse = JsonSerializer.Deserialize<PolygonAggregatesResponse>(content);

            if (polygonResponse?.Results == null || !polygonResponse.Results.Any())
            {
                return 0L;
            }

            var volumes = polygonResponse.Results.Where(r => r.Volume.HasValue).Select(r => r.Volume!.Value).ToList();
            var averageVolume = volumes.Any() ? (long)volumes.Average() : 0L;

            // Cache the volumes for future use
            await database.KeyDeleteAsync(key);
            await database.ListRightPushAsync(key, volumes.Select(v => (RedisValue)v.ToString()).ToArray());
            await database.KeyExpireAsync(key, TimeSpan.FromDays(1));

            return averageVolume;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error fetching average volume for {Symbol}", symbol);
            return 0L;
        }
    }

    private PreMarketAnalysis PerformPreMarketAnalysis(string symbol, List<PolygonBar> preMarketBars, decimal previousClose, long averageVolume)
    {
        if (!preMarketBars.Any())
        {
            return CreateInsufficientDataAnalysis(symbol);
        }

        var preMarketHigh = preMarketBars.Where(b => b.High.HasValue).Max(b => b.High!.Value);
        var preMarketLow = preMarketBars.Where(b => b.Low.HasValue).Min(b => b.Low!.Value);
        var preMarketClose = preMarketBars.Last().Close ?? 0m;
        var preMarketVolume = preMarketBars.Sum(b => b.Volume ?? 0L);

        // Calculate volatility (high-low range as percentage of previous close)
        var volatilityPercent = previousClose > 0 ? ((preMarketHigh - preMarketLow) / previousClose) * 100m : 0m;

        // Calculate gap (difference between previous close and pre-market close)
        var gapPercent = previousClose > 0 ? Math.Abs((preMarketClose - previousClose) / previousClose) * 100m : 0m;

        // Calculate volume anomaly score
        var volumeAnomalyScore = averageVolume > 0 ? (decimal)preMarketVolume / (decimal)averageVolume : 0m;

        // Calculate ATR percentage for context
        var atrPercent = CalculateAtrPercent(preMarketBars, previousClose);

        // Determine risk level and blocking decision
        var riskLevel = DetermineRiskLevel(volatilityPercent, gapPercent, volumeAnomalyScore);
        var shouldBlock = ShouldBlockBasedOnCriteria(volatilityPercent, gapPercent, volumeAnomalyScore);
        var blockingReason = GetBlockingReason(volatilityPercent, gapPercent, volumeAnomalyScore, shouldBlock);

        var metrics = new PreMarketMetrics(
            preMarketHigh,
            preMarketLow,
            preMarketClose,
            previousClose,
            preMarketVolume,
            averageVolume,
            atrPercent,
            preMarketBars.Count
        );

        return new PreMarketAnalysis(
            symbol,
            DateTime.UtcNow,
            shouldBlock,
            riskLevel,
            volatilityPercent,
            gapPercent,
            volumeAnomalyScore,
            blockingReason,
            metrics
        );
    }

    private decimal CalculateAtrPercent(List<PolygonBar> bars, decimal previousClose)
    {
        if (bars.Count < 2)
            return 0m;

        var trueRanges = new List<decimal>();
        for (int i = 1; i < bars.Count; i++)
        {
            var high = bars[i].High ?? 0m;
            var low = bars[i].Low ?? 0m;
            var prevClose = i == 1 ? previousClose : (bars[i - 1].Close ?? 0m);

            var tr1 = high - low;
            var tr2 = Math.Abs(high - prevClose);
            var tr3 = Math.Abs(low - prevClose);

            trueRanges.Add(Math.Max(tr1, Math.Max(tr2, tr3)));
        }

        var atr = trueRanges.Average();
        return previousClose > 0 ? (atr / previousClose) * 100m : 0m;
    }

    private PreMarketRiskLevel DetermineRiskLevel(decimal volatilityPercent, decimal gapPercent, decimal volumeAnomalyScore)
    {
        var riskScore = 0;

        // Volatility scoring
        if (volatilityPercent > _config.MaxVolatilityPercent * 2) riskScore += 3;
        else if (volatilityPercent > _config.MaxVolatilityPercent) riskScore += 2;
        else if (volatilityPercent > _config.MaxVolatilityPercent * 0.5m) riskScore += 1;

        // Gap scoring
        if (gapPercent > _config.MaxGapPercent * 2) riskScore += 3;
        else if (gapPercent > _config.MaxGapPercent) riskScore += 2;
        else if (gapPercent > _config.MaxGapPercent * 0.5m) riskScore += 1;

        // Volume anomaly scoring
        if (volumeAnomalyScore > _config.VolumeAnomalyThreshold * 2) riskScore += 3;
        else if (volumeAnomalyScore > _config.VolumeAnomalyThreshold) riskScore += 2;
        else if (volumeAnomalyScore > _config.VolumeAnomalyThreshold * 0.5m) riskScore += 1;

        return riskScore switch
        {
            >= 7 => PreMarketRiskLevel.Extreme,
            >= 5 => PreMarketRiskLevel.High,
            >= 3 => PreMarketRiskLevel.Medium,
            _ => PreMarketRiskLevel.Low
        };
    }

    private bool ShouldBlockBasedOnCriteria(decimal volatilityPercent, decimal gapPercent, decimal volumeAnomalyScore)
    {
        return volatilityPercent > _config.MaxVolatilityPercent ||
               gapPercent > _config.MaxGapPercent ||
               volumeAnomalyScore > _config.VolumeAnomalyThreshold;
    }

    private string GetBlockingReason(decimal volatilityPercent, decimal gapPercent, decimal volumeAnomalyScore, bool shouldBlock)
    {
        if (!shouldBlock)
            return "No blocking conditions detected";

        var reasons = new List<string>();

        if (volatilityPercent > _config.MaxVolatilityPercent)
            reasons.Add($"High volatility: {volatilityPercent:F2}% > {_config.MaxVolatilityPercent:F2}%");

        if (gapPercent > _config.MaxGapPercent)
            reasons.Add($"Large gap: {gapPercent:F2}% > {_config.MaxGapPercent:F2}%");

        if (volumeAnomalyScore > _config.VolumeAnomalyThreshold)
            reasons.Add($"Volume anomaly: {volumeAnomalyScore:F2}x > {_config.VolumeAnomalyThreshold:F2}x");

        return string.Join("; ", reasons);
    }

    private PreMarketAnalysis CreateInsufficientDataAnalysis(string symbol)
    {
        return new PreMarketAnalysis(
            symbol,
            DateTime.UtcNow,
            true, // Block trading when insufficient data
            PreMarketRiskLevel.High,
            0m,
            0m,
            0m,
            "Insufficient pre-market data for analysis",
            new PreMarketMetrics(0m, 0m, 0m, 0m, 0L, 0L, 0m, 0)
        );
    }

    private bool IsAnalysisValid(PreMarketAnalysis analysis)
    {
        var age = DateTime.UtcNow - analysis.AnalysisTime;
        return age <= _config.CacheExpiry && analysis.AnalysisTime.Date == DateTime.Today;
    }

    private async Task CacheAnalysisAsync(PreMarketAnalysis analysis)
    {
        try
        {
            // Cache in memory
            _analysisCache.AddOrUpdate(analysis.Symbol, analysis, (_, _) => analysis);

            // Cache in Redis
            var database = await _redisService.GetDatabaseAsync();
            var key = string.Format(AnalysisCacheKeyPattern, analysis.Symbol, DateTime.Today);
            var json = JsonSerializer.Serialize(analysis);

            await database.StringSetAsync(key, json, _config.CacheExpiry);
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Error caching analysis for {Symbol}", analysis.Symbol);
        }
    }

    // === IDisposable ===

    public void Dispose()
    {
        if (_disposed)
            return;

        try
        {
            _analysisLock?.Dispose();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error disposing PreMarketFilterService");
        }
        finally
        {
            _disposed = true;
        }
    }
}
