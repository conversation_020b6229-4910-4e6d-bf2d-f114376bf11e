using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using SmaTrendFollower.Services;
using SmaTrendFollower.Configuration;

// Simple test to verify VIX resolver works in isolation
var builder = Host.CreateApplicationBuilder();

// Configure logging
builder.Logging.ClearProviders();
builder.Logging.AddConsole();
builder.Logging.SetMinimumLevel(LogLevel.Debug);

// Configure services
builder.Services.AddFullTradingSystem(builder.Configuration);

var host = builder.Build();

var logger = host.Services.GetRequiredService<ILogger<Program>>();
var vixResolver = host.Services.GetRequiredService<IVIXResolverService>();

logger.LogInformation("Testing VIX resolver with 30-second timeout...");

try
{
    using var cts = new CancellationTokenSource(TimeSpan.FromSeconds(30));
    var result = await vixResolver.GetVixWithFreshnessAsync(TimeSpan.FromMinutes(15), cts.Token);
    
    logger.LogInformation("VIX Result: Value={VixValue}, Source={Source}, Fresh={IsFresh}, Level={FallbackLevel}", 
        result.VixValue, result.Source, result.IsFresh, result.FallbackLevel);
        
    if (result.VixValue.HasValue)
    {
        logger.LogInformation("✅ VIX resolver working: {VixValue:F2}", result.VixValue.Value);
    }
    else
    {
        logger.LogWarning("❌ VIX resolver failed: {ErrorMessage}", result.ErrorMessage);
    }
}
catch (OperationCanceledException)
{
    logger.LogError("❌ VIX resolver timed out after 30 seconds");
}
catch (Exception ex)
{
    logger.LogError(ex, "❌ VIX resolver threw exception");
}

logger.LogInformation("VIX resolver test completed");
