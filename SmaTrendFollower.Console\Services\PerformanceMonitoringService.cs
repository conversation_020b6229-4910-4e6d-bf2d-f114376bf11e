using Microsoft.Extensions.Logging;
using System.Collections.Concurrent;
using System.Diagnostics;

namespace SmaTrendFollower.Services;

/// <summary>
/// Monitors and tracks performance metrics for parallel and async operations.
/// Provides real-time insights into system utilization and bottlenecks.
/// </summary>
public sealed class PerformanceMonitoringService
{
    private readonly ILogger<PerformanceMonitoringService> _logger;
    private readonly ConcurrentDictionary<string, OperationMetrics> _operationMetrics;
    private readonly ConcurrentDictionary<string, List<double>> _latencyHistory;
    private readonly PerformanceCounter? _cpuCounter;
    private readonly PerformanceCounter? _memoryCounter;
    private readonly Timer _reportingTimer;

    public PerformanceMonitoringService(ILogger<PerformanceMonitoringService> logger)
    {
        _logger = logger;
        _operationMetrics = new ConcurrentDictionary<string, OperationMetrics>();
        _latencyHistory = new ConcurrentDictionary<string, List<double>>();

        // Initialize performance counters (Windows only)
        if (OperatingSystem.IsWindows())
        {
            try
            {
                _cpuCounter = new PerformanceCounter("Processor", "% Processor Time", "_Total");
                _memoryCounter = new PerformanceCounter("Memory", "Available MBytes");
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Performance counters not available on this platform");
            }
        }
        else
        {
            _logger.LogInformation("Performance counters are only available on Windows");
        }

        // Start periodic reporting
        _reportingTimer = new Timer(ReportMetrics, null, TimeSpan.FromMinutes(1), TimeSpan.FromMinutes(1));
    }

    /// <summary>
    /// Tracks the performance of an operation
    /// </summary>
    public async Task<T> TrackOperationAsync<T>(string operationName, Func<Task<T>> operation)
    {
        var stopwatch = Stopwatch.StartNew();
        var startTime = DateTime.UtcNow;

        try
        {
            var result = await operation();
            stopwatch.Stop();

            RecordSuccess(operationName, stopwatch.Elapsed.TotalMilliseconds);
            return result;
        }
        catch (Exception ex)
        {
            stopwatch.Stop();
            RecordError(operationName, stopwatch.Elapsed.TotalMilliseconds, ex);
            throw;
        }
    }

    /// <summary>
    /// Tracks the performance of a synchronous operation
    /// </summary>
    public T TrackOperation<T>(string operationName, Func<T> operation)
    {
        var stopwatch = Stopwatch.StartNew();

        try
        {
            var result = operation();
            stopwatch.Stop();

            RecordSuccess(operationName, stopwatch.Elapsed.TotalMilliseconds);
            return result;
        }
        catch (Exception ex)
        {
            stopwatch.Stop();
            RecordError(operationName, stopwatch.Elapsed.TotalMilliseconds, ex);
            throw;
        }
    }

    /// <summary>
    /// Records a successful operation
    /// </summary>
    public void RecordSuccess(string operationName, double elapsedMs)
    {
        var metrics = _operationMetrics.AddOrUpdate(operationName,
            new OperationMetrics(operationName),
            (key, existing) => existing);

        metrics.RecordSuccess(elapsedMs);
        RecordLatency(operationName, elapsedMs);
    }

    /// <summary>
    /// Records a failed operation
    /// </summary>
    public void RecordError(string operationName, double elapsedMs, Exception exception)
    {
        var metrics = _operationMetrics.AddOrUpdate(operationName,
            new OperationMetrics(operationName),
            (key, existing) => existing);

        metrics.RecordError(elapsedMs, exception);
        RecordLatency(operationName, elapsedMs);
    }

    /// <summary>
    /// Records latency for percentile calculations
    /// </summary>
    private void RecordLatency(string operationName, double elapsedMs)
    {
        var latencies = _latencyHistory.AddOrUpdate(operationName,
            new List<double> { elapsedMs },
            (key, existing) =>
            {
                lock (existing)
                {
                    existing.Add(elapsedMs);
                    // Keep only last 1000 measurements
                    if (existing.Count > 1000)
                    {
                        existing.RemoveAt(0);
                    }
                    return existing;
                }
            });
    }

    /// <summary>
    /// Gets current system performance metrics
    /// </summary>
    public SystemPerformanceMetrics GetSystemMetrics()
    {
        var cpuUsage = 0.0;
        var availableMemoryMB = 0.0;

        if (OperatingSystem.IsWindows())
        {
            try
            {
                cpuUsage = _cpuCounter?.NextValue() ?? 0.0;
                availableMemoryMB = _memoryCounter?.NextValue() ?? 0.0;
            }
            catch (Exception ex)
            {
                _logger.LogDebug(ex, "Failed to read system performance counters");
            }
        }

        var process = Process.GetCurrentProcess();
        var workingSetMB = process.WorkingSet64 / 1024.0 / 1024.0;
        var threadCount = process.Threads.Count;

        return new SystemPerformanceMetrics(
            cpuUsage,
            workingSetMB,
            availableMemoryMB,
            threadCount,
            Environment.ProcessorCount,
            GC.GetTotalMemory(false) / 1024.0 / 1024.0
        );
    }

    /// <summary>
    /// Gets performance metrics for a specific operation
    /// </summary>
    public OperationPerformanceMetrics? GetOperationMetrics(string operationName)
    {
        if (!_operationMetrics.TryGetValue(operationName, out var metrics))
            return null;

        var latencies = _latencyHistory.GetValueOrDefault(operationName, new List<double>());
        
        double p50 = 0, p95 = 0, p99 = 0;
        lock (latencies)
        {
            if (latencies.Any())
            {
                var sorted = latencies.OrderBy(x => x).ToArray();
                p50 = GetPercentile(sorted, 0.5);
                p95 = GetPercentile(sorted, 0.95);
                p99 = GetPercentile(sorted, 0.99);
            }
        }

        return new OperationPerformanceMetrics(
            operationName,
            metrics.TotalOperations,
            metrics.SuccessCount,
            metrics.ErrorCount,
            metrics.AverageLatencyMs,
            metrics.MinLatencyMs,
            metrics.MaxLatencyMs,
            p50, p95, p99,
            metrics.ErrorRate,
            metrics.ThroughputPerSecond
        );
    }

    /// <summary>
    /// Gets all operation metrics
    /// </summary>
    public List<OperationPerformanceMetrics> GetAllOperationMetrics()
    {
        return _operationMetrics.Keys
            .Select(GetOperationMetrics)
            .Where(m => m != null)
            .Cast<OperationPerformanceMetrics>()
            .ToList();
    }

    /// <summary>
    /// Calculates percentile from sorted array
    /// </summary>
    private static double GetPercentile(double[] sortedValues, double percentile)
    {
        if (!sortedValues.Any()) return 0;
        
        var index = percentile * (sortedValues.Length - 1);
        var lower = (int)Math.Floor(index);
        var upper = (int)Math.Ceiling(index);
        
        if (lower == upper) return sortedValues[lower];
        
        var weight = index - lower;
        return sortedValues[lower] * (1 - weight) + sortedValues[upper] * weight;
    }

    /// <summary>
    /// Periodic reporting of metrics
    /// </summary>
    private void ReportMetrics(object? state)
    {
        try
        {
            var systemMetrics = GetSystemMetrics();
            var operationMetrics = GetAllOperationMetrics();

            _logger.LogInformation("=== Performance Report ===");
            _logger.LogInformation("System: CPU {CpuUsage:F1}%, Memory {MemoryMB:F0}MB, Threads {ThreadCount}",
                systemMetrics.CpuUsagePercent, systemMetrics.WorkingSetMB, systemMetrics.ThreadCount);

            foreach (var op in operationMetrics.OrderByDescending(o => o.ThroughputPerSecond))
            {
                _logger.LogInformation("{Operation}: {Total} ops, {Throughput:F1}/sec, P95 {P95:F0}ms, Errors {ErrorRate:F1}%",
                    op.OperationName, op.TotalOperations, op.ThroughputPerSecond, op.P95LatencyMs, op.ErrorRate * 100);
            }
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Error during performance reporting");
        }
    }

    public void Dispose()
    {
        _reportingTimer?.Dispose();
        _cpuCounter?.Dispose();
        _memoryCounter?.Dispose();
    }
}

/// <summary>
/// Thread-safe operation metrics tracking
/// </summary>
public class OperationMetrics
{
    private readonly object _lock = new object();
    private long _totalOperations;
    private long _successCount;
    private long _errorCount;
    private double _totalLatencyMs;
    private double _minLatencyMs = double.MaxValue;
    private double _maxLatencyMs;
    private DateTime _firstOperation = DateTime.UtcNow;

    public string OperationName { get; }
    public long TotalOperations => _totalOperations;
    public long SuccessCount => _successCount;
    public long ErrorCount => _errorCount;
    public double AverageLatencyMs => _totalOperations > 0 ? _totalLatencyMs / _totalOperations : 0;
    public double MinLatencyMs => _minLatencyMs == double.MaxValue ? 0 : _minLatencyMs;
    public double MaxLatencyMs => _maxLatencyMs;
    public double ErrorRate => _totalOperations > 0 ? (double)_errorCount / _totalOperations : 0;
    public double ThroughputPerSecond
    {
        get
        {
            var elapsed = DateTime.UtcNow - _firstOperation;
            return elapsed.TotalSeconds > 0 ? _totalOperations / elapsed.TotalSeconds : 0;
        }
    }

    public OperationMetrics(string operationName)
    {
        OperationName = operationName;
    }

    public void RecordSuccess(double elapsedMs)
    {
        lock (_lock)
        {
            _totalOperations++;
            _successCount++;
            _totalLatencyMs += elapsedMs;
            _minLatencyMs = Math.Min(_minLatencyMs, elapsedMs);
            _maxLatencyMs = Math.Max(_maxLatencyMs, elapsedMs);
        }
    }

    public void RecordError(double elapsedMs, Exception exception)
    {
        lock (_lock)
        {
            _totalOperations++;
            _errorCount++;
            _totalLatencyMs += elapsedMs;
            _minLatencyMs = Math.Min(_minLatencyMs, elapsedMs);
            _maxLatencyMs = Math.Max(_maxLatencyMs, elapsedMs);
        }
    }
}

/// <summary>
/// System-level performance metrics
/// </summary>
public record SystemPerformanceMetrics(
    double CpuUsagePercent,
    double WorkingSetMB,
    double AvailableMemoryMB,
    int ThreadCount,
    int ProcessorCount,
    double GcMemoryMB
);

/// <summary>
/// Operation-level performance metrics
/// </summary>
public record OperationPerformanceMetrics(
    string OperationName,
    long TotalOperations,
    long SuccessCount,
    long ErrorCount,
    double AverageLatencyMs,
    double MinLatencyMs,
    double MaxLatencyMs,
    double P50LatencyMs,
    double P95LatencyMs,
    double P99LatencyMs,
    double ErrorRate,
    double ThroughputPerSecond
);
