using System;
using System.Threading.Tasks;
using FluentAssertions;
using Microsoft.Extensions.Logging.Abstractions;
using SmaTrendFollower.Services;
using Xunit;

namespace SmaTrendFollower.Tests.Core.Guards;

public class MarketSessionGuardTests
{
    private readonly MarketSessionGuard _guard;

    public MarketSessionGuardTests()
    {
        var timeProvider = new TestTimeProvider();
        _guard = new MarketSessionGuard(timeProvider, new NullLogger<MarketSessionGuard>());
    }

    [Theory]
    [InlineData("2025-06-27T14:00:00Z", true)]   // Friday market open
    [InlineData("2025-06-28T14:00:00Z", false)]  // Saturday
    [InlineData("2025-06-29T14:00:00Z", false)]  // Sunday
    [InlineData("2025-06-30T14:00:00Z", true)]   // Monday market open
    public async Task CanTradeNowAsync_Works(string iso, bool expected)
    {
        // Arrange
        var testTime = DateTime.Parse(iso, null, System.Globalization.DateTimeStyles.AdjustToUniversal);
        var timeProvider = new TestTimeProvider(testTime);
        var guard = new MarketSessionGuard(timeProvider, new NullLogger<MarketSessionGuard>());

        // Act
        bool result = await guard.CanTradeNowAsync();

        // Assert
        result.Should().Be(expected);
    }

    [Fact]
    public async Task CanTradeNowAsync_Weekend_SetsReasonCorrectly()
    {
        // Arrange - Saturday
        var testTime = DateTime.Parse("2025-06-28T14:00:00Z", null, System.Globalization.DateTimeStyles.AdjustToUniversal);
        var timeProvider = new TestTimeProvider(testTime);
        var guard = new MarketSessionGuard(timeProvider, new NullLogger<MarketSessionGuard>());

        // Act
        bool result = await guard.CanTradeNowAsync();

        // Assert
        result.Should().BeFalse();
        guard.Reason.Should().Be("Weekend - markets closed");
    }

    [Fact]
    public async Task CanTradeNowAsync_Weekday_ClearsReason()
    {
        // Arrange - Friday
        var testTime = DateTime.Parse("2025-06-27T14:00:00Z", null, System.Globalization.DateTimeStyles.AdjustToUniversal);
        var timeProvider = new TestTimeProvider(testTime);
        var guard = new MarketSessionGuard(timeProvider, new NullLogger<MarketSessionGuard>());

        // Act
        bool result = await guard.CanTradeNowAsync();

        // Assert
        result.Should().BeTrue();
        guard.Reason.Should().BeEmpty();
    }
}

/// <summary>
/// Test implementation of ITimeProvider for controlled time testing
/// </summary>
public class TestTimeProvider : ITimeProvider
{
    private readonly DateTime _fixedTime;

    public TestTimeProvider(DateTime? fixedTime = null)
    {
        _fixedTime = fixedTime ?? DateTime.UtcNow;
    }

    public DateTime UtcNow => _fixedTime;
}
