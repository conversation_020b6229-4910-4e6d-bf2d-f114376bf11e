using Microsoft.Extensions.Logging;
using VaultSharp;
using VaultSharp.V1.AuthMethods.Token;
using VaultSharp.V1.Commons;

namespace SmaTrendFollower.Infrastructure.Security;

/// <summary>
/// HashiCorp Vault KV v2 secret provider for production environments.
/// Provides secure secret retrieval from <PERSON>ault with proper error handling and logging.
/// </summary>
public sealed class VaultSecretProvider : ISecretProvider
{
    private readonly IVaultClient _vault;
    private readonly string _path;
    private readonly ILogger<VaultSecretProvider>? _logger;
    private readonly Dictionary<string, string> _cache;
    private readonly object _cacheLock = new();

    /// <summary>
    /// Initializes a new instance of the VaultSecretProvider.
    /// </summary>
    /// <param name="vaultAddress">Vault server address (e.g., "http://vault:8200")</param>
    /// <param name="vaultToken">Vault authentication token</param>
    /// <param name="secretPath">KV v2 secret path (default: "secret/data/sma")</param>
    /// <param name="logger">Optional logger for diagnostics</param>
    public VaultSecretProvider(
        string vaultAddress, 
        string vaultToken, 
        string secretPath = "sma",
        ILogger<VaultSecretProvider>? logger = null)
    {
        if (string.IsNullOrWhiteSpace(vaultAddress))
            throw new ArgumentException("Vault address cannot be null or whitespace", nameof(vaultAddress));
        
        if (string.IsNullOrWhiteSpace(vaultToken))
            throw new ArgumentException("Vault token cannot be null or whitespace", nameof(vaultToken));
        
        if (string.IsNullOrWhiteSpace(secretPath))
            throw new ArgumentException("Secret path cannot be null or whitespace", nameof(secretPath));

        _logger = logger;
        _path = secretPath;
        _cache = new Dictionary<string, string>();

        try
        {
            var auth = new TokenAuthMethodInfo(vaultToken);
            var vaultClientSettings = new VaultClientSettings(vaultAddress, auth);
            _vault = new VaultClient(vaultClientSettings);
            
            _logger?.LogInformation("Initialized Vault secret provider for address {VaultAddress} with path {SecretPath}", 
                vaultAddress, secretPath);
        }
        catch (Exception ex)
        {
            _logger?.LogError(ex, "Failed to initialize Vault client for address {VaultAddress}", vaultAddress);
            throw new InvalidOperationException($"Failed to initialize Vault client: {ex.Message}", ex);
        }
    }

    /// <inheritdoc />
    public string ProviderName => "HashiCorp Vault";

    /// <inheritdoc />
    public bool IsConfigured => _vault != null;

    /// <inheritdoc />
    public string Get(string key)
    {
        if (string.IsNullOrWhiteSpace(key))
        {
            throw new ArgumentException("Secret key cannot be null or whitespace", nameof(key));
        }



        // Check cache first
        lock (_cacheLock)
        {
            if (_cache.TryGetValue(key, out var cachedValue))
            {
                _logger?.LogDebug("Retrieved secret {Key} from cache", key);
                return cachedValue;
            }
        }

        try
        {
            _logger?.LogDebug("Retrieving secret {Key} from Vault at path {Path}", key, _path);
            
            var secret = _vault.V1.Secrets.KeyValue.V2
                .ReadSecretAsync(_path)
                .GetAwaiter()
                .GetResult();

            if (secret?.Data?.Data == null)
            {
                _logger?.LogError("Vault returned null data for path {Path}", _path);
                throw new InvalidOperationException($"Vault returned null data for path '{_path}'");
            }

            if (!secret.Data.Data.TryGetValue(key, out var value) || value == null)
            {
                _logger?.LogError("Secret key {Key} not found in Vault at path {Path}", key, _path);
                throw new KeyNotFoundException($"Secret key '{key}' not found in Vault at path '{_path}'");
            }

            var stringValue = value.ToString()!;
            
            // Cache the value for future requests
            lock (_cacheLock)
            {
                _cache[key] = stringValue;
            }

            _logger?.LogDebug("Successfully retrieved secret {Key} from Vault", key);
            return stringValue;
        }
        catch (Exception ex) when (!(ex is KeyNotFoundException))
        {
            _logger?.LogError(ex, "Error retrieving secret {Key} from Vault", key);
            throw new InvalidOperationException($"Failed to retrieve secret '{key}' from Vault: {ex.Message}", ex);
        }
    }

    /// <inheritdoc />
    public bool TryGet(string key, out string? value)
    {
        value = null;

        if (string.IsNullOrWhiteSpace(key))
        {
            return false;
        }

        try
        {
            value = Get(key);
            return true;
        }
        catch (Exception ex)
        {
            _logger?.LogDebug(ex, "Failed to retrieve secret {Key} from Vault", key);
            return false;
        }
    }

    /// <inheritdoc />
    public bool Exists(string key)
    {
        if (string.IsNullOrWhiteSpace(key))
        {
            return false;
        }

        try
        {
            // Check cache first
            lock (_cacheLock)
            {
                if (_cache.ContainsKey(key))
                {
                    return true;
                }
            }

            var secret = _vault.V1.Secrets.KeyValue.V2
                .ReadSecretAsync(_path)
                .GetAwaiter()
                .GetResult();

            return secret?.Data?.Data?.ContainsKey(key) == true;
        }
        catch (Exception ex)
        {
            _logger?.LogDebug(ex, "Error checking existence of secret {Key} in Vault", key);
            return false;
        }
    }

    /// <summary>
    /// Clears the internal cache of retrieved secrets.
    /// </summary>
    public void ClearCache()
    {
        lock (_cacheLock)
        {
            _cache.Clear();
            _logger?.LogDebug("Cleared Vault secret cache");
        }
    }
}
