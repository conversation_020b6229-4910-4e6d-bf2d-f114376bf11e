using Serilog;
using Serilog.Configuration;
using Serilog.Events;

namespace SmaTrendFollower.Infrastructure;

/// <summary>
/// Extension methods for adding Discord sink to Serilog configuration
/// </summary>
public static class SerilogDiscordExtensions
{
    /// <summary>
    /// Adds Discord bot sink to Serilog configuration
    /// Sends Warning+ level logs to Discord channel using bot token authentication
    /// </summary>
    /// <param name="loggerSinkConfiguration">The logger sink configuration</param>
    /// <param name="restrictedToMinimumLevel">Minimum log level (default: Warning)</param>
    /// <param name="formatProvider">Format provider for log message formatting</param>
    /// <returns>Logger configuration for method chaining</returns>
    public static LoggerConfiguration Discord(
        this LoggerSinkConfiguration loggerSinkConfiguration,
        LogEventLevel restrictedToMinimumLevel = LogEventLevel.Warning,
        IFormatProvider? formatProvider = null)
    {
        if (loggerSinkConfiguration == null)
            throw new ArgumentNullException(nameof(loggerSinkConfiguration));

        var botToken = Environment.GetEnvironmentVariable("DISCORD_BOT_TOKEN");
        var channelId = Environment.GetEnvironmentVariable("DISCORD_CHANNEL_ID");

        if (string.IsNullOrEmpty(botToken) || string.IsNullOrEmpty(channelId))
            throw new InvalidOperationException("DISCORD_BOT_TOKEN and DISCORD_CHANNEL_ID environment variables must be set");

        return loggerSinkConfiguration.Sink(
            new DiscordBotSink(botToken, channelId, formatProvider),
            restrictedToMinimumLevel);
    }
}
