using StackExchange.Redis;
using System;
using System.Threading.Tasks;

namespace SmaTrendFollower.Console
{
    public static class TestRedis
    {
        public static async Task TestConnectionAsync()
        {
            try
            {
                var configOptions = ConfigurationOptions.Parse("*************:6379");
                configOptions.AbortOnConnectFail = false;
                configOptions.ConnectTimeout = 5000;
                configOptions.SyncTimeout = 2000;
                
                using var connection = await ConnectionMultiplexer.ConnectAsync(configOptions);
                var database = connection.GetDatabase();
                
                // Test basic connectivity
                var pong = await database.PingAsync();
                System.Console.WriteLine($"✅ Redis connection successful! Ping: {pong.TotalMilliseconds}ms");
                
                // Test basic operations
                await database.StringSetAsync("test:connection", "success");
                var result = await database.StringGetAsync("test:connection");
                System.Console.WriteLine($"✅ Redis read/write test: {result}");
                
                // Clean up test key
                await database.KeyDeleteAsync("test:connection");
                
                System.Console.WriteLine("✅ Redis is ready for SmaTrendFollower!");
            }
            catch (Exception ex)
            {
                System.Console.WriteLine($"❌ Redis connection failed: {ex.Message}");
                throw;
            }
        }
    }
}
