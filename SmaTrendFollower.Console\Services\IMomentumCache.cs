using SmaTrendFollower.Models;

namespace SmaTrendFollower.Services;

/// <summary>
/// Interface for caching momentum-related technical indicators
/// </summary>
public interface IMomentumCache
{
    /// <summary>
    /// Caches RSI value for a symbol
    /// </summary>
    /// <param name="symbol">Stock symbol</param>
    /// <param name="rsi">RSI value</param>
    /// <param name="expiry">Cache expiry time</param>
    Task SetRsiAsync(string symbol, double rsi, TimeSpan? expiry = null);

    /// <summary>
    /// Gets cached RSI value for a symbol
    /// </summary>
    /// <param name="symbol">Stock symbol</param>
    /// <returns>Cached RSI value or null if not found</returns>
    Task<double?> GetRsiAsync(string symbol);

    /// <summary>
    /// Caches MACD values for a symbol
    /// </summary>
    /// <param name="symbol">Stock symbol</param>
    /// <param name="macd">MACD result</param>
    /// <param name="expiry">Cache expiry time</param>
    Task SetMacdAsync(string symbol, Models.MacdResult macd, TimeSpan? expiry = null);

    /// <summary>
    /// Gets cached MACD values for a symbol
    /// </summary>
    /// <param name="symbol">Stock symbol</param>
    /// <returns>Cached MACD result or null if not found</returns>
    Task<Models.MacdResult?> GetMacdAsync(string symbol);

    /// <summary>
    /// Caches momentum analysis for a symbol
    /// </summary>
    /// <param name="symbol">Stock symbol</param>
    /// <param name="analysis">Momentum analysis result</param>
    /// <param name="expiry">Cache expiry time</param>
    Task SetMomentumAnalysisAsync(string symbol, MomentumAnalysis analysis, TimeSpan? expiry = null);

    /// <summary>
    /// Gets cached momentum analysis for a symbol
    /// </summary>
    /// <param name="symbol">Stock symbol</param>
    /// <returns>Cached momentum analysis or null if not found</returns>
    Task<MomentumAnalysis?> GetMomentumAnalysisAsync(string symbol);

    /// <summary>
    /// Removes cached momentum data for a symbol
    /// </summary>
    /// <param name="symbol">Stock symbol</param>
    Task RemoveAsync(string symbol);

    /// <summary>
    /// Clears all cached momentum data
    /// </summary>
    Task ClearAllAsync();

    /// <summary>
    /// Gets cache statistics
    /// </summary>
    /// <returns>Cache statistics</returns>
    Task<MomentumCacheStats> GetStatsAsync();
}

/// <summary>
/// Momentum cache statistics
/// </summary>
/// <param name="TotalKeys">Total number of cached keys</param>
/// <param name="RsiKeys">Number of RSI keys</param>
/// <param name="MacdKeys">Number of MACD keys</param>
/// <param name="AnalysisKeys">Number of momentum analysis keys</param>
/// <param name="LastUpdated">Last update timestamp</param>
public readonly record struct MomentumCacheStats(
    int TotalKeys,
    int RsiKeys,
    int MacdKeys,
    int AnalysisKeys,
    DateTime LastUpdated
);
