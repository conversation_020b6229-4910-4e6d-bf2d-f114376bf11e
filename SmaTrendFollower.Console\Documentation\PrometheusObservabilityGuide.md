# 📊 Prometheus Observability Implementation Guide

## Overview

The SmaTrendFollower system now includes comprehensive Prometheus metrics and observability features, providing real-time monitoring of trading operations, system performance, and business metrics.

## 🚀 Quick Start

### Starting the Metrics API Server

```bash
# Start the metrics API server with Prometheus endpoint
dotnet run metrics-api
```

The server will start on `http://localhost:5000` with the following endpoints:
- **Prometheus Metrics**: `http://localhost:5000/metrics`
- **Existing Dashboard**: `http://localhost:8080` (MetricsApiService)

### Testing Metrics Collection

```bash
# Run the metrics demo to generate sample data
dotnet run test-metrics
```

This will exercise the instrumented services and populate metrics with sample data.

## 📈 Available Metrics

### Trading Metrics

| Metric Name | Type | Description | Labels |
|-------------|------|-------------|---------|
| `trades_total` | Counter | Total trades executed | `side`, `symbol` |
| `signals_total` | Counter | Trading signals generated | `signal_type`, `executed` |
| `signal_latency_ms` | Histogram | Signal generation latency | - |
| `portfolio_value_usd` | Gauge | Current portfolio value | - |
| `cash_balance_usd` | Gauge | Current cash balance | - |
| `daily_pnl_usd` | Gauge | Daily profit and loss | - |

### Market Data Metrics

| Metric Name | Type | Description | Labels |
|-------------|------|-------------|---------|
| `websocket_reconnect_total` | Counter | WebSocket reconnections | `channel`, `reason` |
| `universe_size` | Gauge | Trading universe size | - |
| `market_data_requests_total` | Counter | Market data requests | `source`, `status` |
| `market_data_latency_ms` | Histogram | Market data fetch latency | - |
| `data_staleness_minutes` | Gauge | Data age in minutes | `data_type`, `symbol` |

### System Performance Metrics

| Metric Name | Type | Description | Labels |
|-------------|------|-------------|---------|
| `rate_limit_hits_total` | Counter | API rate limit hits | `service`, `endpoint` |
| `circuit_breaker_trips_total` | Counter | Circuit breaker trips | `service`, `reason` |
| `redis_latency_ms` | Histogram | Redis operation latency | - |
| `redis_cache_operations_total` | Counter | Redis cache operations | `operation`, `result` |
| `redis_connections` | Gauge | Redis connection count | - |

### Risk Management Metrics

| Metric Name | Type | Description | Labels |
|-------------|------|-------------|---------|
| `risk_checks_total` | Counter | Risk checks performed | `check_type`, `result` |
| `trades_blocked_total` | Counter | Trades blocked by risk | `reason` |
| `current_positions` | Gauge | Open position count | - |
| `current_exposure_percent` | Gauge | Market exposure percentage | - |

### Error Tracking Metrics

| Metric Name | Type | Description | Labels |
|-------------|------|-------------|---------|
| `application_errors_total` | Counter | Application errors | `component`, `error_type` |
| `order_failures_total` | Counter | Order execution failures | `reason`, `symbol` |

### Market Regime Metrics

| Metric Name | Type | Description | Labels |
|-------------|------|-------------|---------|
| `current_vix` | Gauge | Current VIX value | - |
| `vix_fallbacks_total` | Counter | VIX data source fallbacks | `from_source`, `to_source` |
| `market_regime` | Gauge | Market regime indicator | - |

## 🔧 Implementation Details

### MetricsRegistry

The `MetricsRegistry` class provides centralized access to all Prometheus metrics:

```csharp
using SmaTrendFollower.Monitoring;

// Record a trade execution
MetricsRegistry.TradesTotal.WithLabels("Buy", "AAPL").Inc();

// Record signal generation latency
var sw = Stopwatch.StartNew();
// ... signal generation logic ...
MetricsRegistry.SignalLatencyMs.Observe(sw.Elapsed.TotalMilliseconds);

// Update portfolio value
MetricsRegistry.PortfolioValueUsd.Set(125000.50m);
```

### Service Instrumentation

Key services have been instrumented with metrics:

#### SimpleSignalGenerator
- Records signal generation latency
- Counts signals generated
- Updates universe size
- Tracks application errors

#### TradeExecutor
- Counts successful trades by side and symbol
- Records order execution failures

#### PolygonWebSocketManager
- Tracks WebSocket reconnections by channel and reason

#### DynamicUniverseProvider
- Updates universe size metric when universe is loaded

### ASP.NET Core Integration

The metrics API uses ASP.NET Core with Prometheus middleware:

```csharp
// Configure middleware pipeline
app.UseHttpMetrics();          // Prometheus middleware for HTTP metrics
app.MapMetrics();              // GET /metrics endpoint (Prometheus format)
```

## 📊 Prometheus Configuration

### Scrape Configuration

Add this to your `prometheus.yml`:

```yaml
scrape_configs:
  - job_name: 'sma-trend-follower'
    static_configs:
      - targets: ['localhost:5000']
    scrape_interval: 15s
    metrics_path: /metrics
```

### Sample Queries

```promql
# Trading volume by symbol
sum(rate(trades_total[5m])) by (symbol)

# Signal generation rate
rate(signals_total[5m])

# Average signal generation latency
histogram_quantile(0.95, rate(signal_latency_ms_bucket[5m]))

# WebSocket reconnection rate
rate(websocket_reconnect_total[5m])

# Current portfolio metrics
portfolio_value_usd
cash_balance_usd
daily_pnl_usd

# Error rates
rate(application_errors_total[5m])
rate(order_failures_total[5m])
```

## 🎯 Grafana Dashboard

### Key Panels

1. **Trading Overview**
   - Portfolio value over time
   - Daily P&L
   - Trade count and success rate

2. **Signal Generation**
   - Signal generation rate
   - Signal latency percentiles
   - Universe size

3. **System Health**
   - WebSocket reconnections
   - API error rates
   - Redis performance

4. **Risk Management**
   - Current positions
   - Market exposure
   - Risk check results

## 🔍 Monitoring Best Practices

### Alerting Rules

```yaml
groups:
  - name: trading_alerts
    rules:
      - alert: HighSignalLatency
        expr: histogram_quantile(0.95, rate(signal_latency_ms_bucket[5m])) > 5000
        for: 2m
        labels:
          severity: warning
        annotations:
          summary: "High signal generation latency"

      - alert: FrequentWebSocketReconnections
        expr: rate(websocket_reconnect_total[5m]) > 0.1
        for: 1m
        labels:
          severity: critical
        annotations:
          summary: "Frequent WebSocket reconnections detected"

      - alert: HighErrorRate
        expr: rate(application_errors_total[5m]) > 0.05
        for: 2m
        labels:
          severity: warning
        annotations:
          summary: "High application error rate"
```

### Health Checks

The system includes health checks that can be monitored:
- Application health
- Redis connectivity (if configured)

## 🧪 Testing

### Manual Testing

1. Start the metrics API: `dotnet run metrics-api`
2. Run the metrics demo: `dotnet run test-metrics`
3. Visit `http://localhost:5000/metrics` to see metrics
4. Look for the metrics listed in the demo output

### Automated Testing

The metrics are automatically updated during normal system operation:
- Signal generation updates latency and count metrics
- Trade execution updates trade counters
- WebSocket reconnections update reconnection counters
- Universe loading updates universe size

## 🔧 Configuration

### Environment Variables

```bash
# Redis connection for health checks (optional)
REDIS_CONNECTION_STRING=*************:6379

# Metrics API port (default: 5000)
METRICS_API_PORT=5000
```

### Service Configuration

The observability services are automatically registered when using `AddFullTradingSystem()`:

```csharp
services.AddFullTradingSystem(configuration)
    .AddObservabilityServices(configuration); // Included automatically
```

## 📝 Next Steps

1. **Set up Prometheus** to scrape the metrics endpoint
2. **Configure Grafana** with dashboards for visualization
3. **Set up alerting** based on the provided alert rules
4. **Monitor in production** and adjust thresholds as needed
5. **Add custom metrics** for business-specific requirements

## 🎉 Conclusion

The SmaTrendFollower system now provides comprehensive observability through Prometheus metrics, enabling:
- Real-time monitoring of trading operations
- Performance tracking and optimization
- Proactive alerting on system issues
- Business intelligence through trading metrics
- Operational visibility into system health

The implementation follows Prometheus best practices and integrates seamlessly with the existing trading system architecture.
