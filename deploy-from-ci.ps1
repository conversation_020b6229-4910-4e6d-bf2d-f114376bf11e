# Deploy SmaTrendFollower from CI artifacts
# This script downloads and deploys the latest build from GitHub Actions

param(
    [string]$DeployPath = "C:\SmaTrendFollower",
    [string]$GitHubToken = $env:GITHUB_TOKEN,
    [string]$Repository = "patco1/SmaTrendFollower",
    [switch]$Backup = $true
)

Write-Host "SmaTrendFollower CI Deployment Script" -ForegroundColor Green
Write-Host "=====================================" -ForegroundColor Green

# Validate parameters
if (-not $GitHubToken) {
    Write-Error "GitHub token is required. Set GITHUB_TOKEN environment variable or pass -GitHubToken parameter."
    exit 1
}

# Create deployment directory if it doesn't exist
if (-not (Test-Path $DeployPath)) {
    Write-Host "Creating deployment directory: $DeployPath" -ForegroundColor Yellow
    New-Item -ItemType Directory -Path $DeployPath -Force | Out-Null
}

# Backup existing deployment if requested
if ($Backup -and (Test-Path "$DeployPath\SmaTrendFollower.Console.exe")) {
    $BackupPath = "$DeployPath\backup-$(Get-Date -Format 'yyyyMMdd-HHmmss')"
    Write-Host "Creating backup at: $BackupPath" -ForegroundColor Yellow
    New-Item -ItemType Directory -Path $BackupPath -Force | Out-Null
    Copy-Item "$DeployPath\*" -Destination $BackupPath -Recurse -Exclude "backup-*"
}

try {
    # Get latest successful workflow run
    Write-Host "Fetching latest successful build..." -ForegroundColor Cyan
    $headers = @{
        "Authorization" = "token $GitHubToken"
        "Accept" = "application/vnd.github.v3+json"
    }
    
    $runsUrl = "https://api.github.com/repos/$Repository/actions/runs?status=success&per_page=1"
    $latestRun = Invoke-RestMethod -Uri $runsUrl -Headers $headers
    
    if ($latestRun.workflow_runs.Count -eq 0) {
        Write-Error "No successful workflow runs found."
        exit 1
    }
    
    $runId = $latestRun.workflow_runs[0].id
    Write-Host "Latest successful run ID: $runId" -ForegroundColor Green
    
    # Get artifacts for the run
    $artifactsUrl = "https://api.github.com/repos/$Repository/actions/runs/$runId/artifacts"
    $artifacts = Invoke-RestMethod -Uri $artifactsUrl -Headers $headers
    
    $buildArtifact = $artifacts.artifacts | Where-Object { $_.name -eq "bot-build" } | Select-Object -First 1
    
    if (-not $buildArtifact) {
        Write-Error "Build artifact 'bot-build' not found in latest run."
        exit 1
    }
    
    # Download artifact
    Write-Host "Downloading build artifact..." -ForegroundColor Cyan
    $downloadUrl = $buildArtifact.archive_download_url
    $artifactPath = "$env:TEMP\sma-build-$runId.zip"
    
    Invoke-RestMethod -Uri $downloadUrl -Headers $headers -OutFile $artifactPath
    Write-Host "Downloaded to: $artifactPath" -ForegroundColor Green
    
    # Extract artifact
    Write-Host "Extracting build to deployment directory..." -ForegroundColor Cyan
    Expand-Archive -Path $artifactPath -DestinationPath $DeployPath -Force
    
    # Clean up
    Remove-Item $artifactPath -Force
    
    Write-Host "Deployment completed successfully!" -ForegroundColor Green
    Write-Host "Application deployed to: $DeployPath" -ForegroundColor Cyan
    Write-Host "Run with: $DeployPath\SmaTrendFollower.Console.exe" -ForegroundColor Cyan
    
} catch {
    Write-Error "Deployment failed: $($_.Exception.Message)"
    exit 1
}
