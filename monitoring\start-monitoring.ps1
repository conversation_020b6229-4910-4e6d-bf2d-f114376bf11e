# SmaTrendFollower Monitoring Stack Startup Script
# This script starts the complete monitoring infrastructure

param(
    [switch]$Stop = $false,
    [switch]$Restart = $false,
    [switch]$Status = $false,
    [switch]$Logs = $false
)

$ErrorActionPreference = "Stop"

# Color output functions
function Write-Success { param($Message) Write-Host $Message -ForegroundColor Green }
function Write-Warning { param($Message) Write-Host $Message -ForegroundColor Yellow }
function Write-Error { param($Message) Write-Host $Message -ForegroundColor Red }
function Write-Info { param($Message) Write-Host $Message -ForegroundColor Cyan }

# Check if Docker is running
function Test-DockerRunning {
    try {
        docker version | Out-Null
        return $true
    }
    catch {
        return $false
    }
}

# Main script logic
Write-Info "🚀 SmaTrendFollower Monitoring Stack Manager"
Write-Info "================================================"

if (-not (Test-DockerRunning)) {
    Write-Error "❌ Docker is not running. Please start Docker Desktop and try again."
    exit 1
}

# Change to monitoring directory
$monitoringPath = Split-Path -Parent $MyInvocation.MyCommand.Path
Set-Location $monitoringPath

if ($Stop) {
    Write-Info "🛑 Stopping monitoring stack..."
    docker-compose down
    Write-Success "✅ Monitoring stack stopped"
    exit 0
}

if ($Restart) {
    Write-Info "🔄 Restarting monitoring stack..."
    docker-compose down
    docker-compose up -d
    Write-Success "✅ Monitoring stack restarted"
}
elseif ($Status) {
    Write-Info "📊 Monitoring stack status:"
    docker-compose ps
    Write-Info ""
    Write-Info "🌐 Access URLs:"
    Write-Info "  Grafana:      http://localhost:3000 (admin/admin)"
    Write-Info "  Prometheus:   http://localhost:9090"
    Write-Info "  AlertManager: http://localhost:9093"
    Write-Info "  Node Exporter: http://localhost:9100"
    Write-Info "  Redis Exporter: http://localhost:9121"
    exit 0
}
elseif ($Logs) {
    Write-Info "📋 Showing monitoring stack logs..."
    docker-compose logs -f
    exit 0
}
else {
    Write-Info "🚀 Starting monitoring stack..."
    
    # Check if services are already running
    $runningServices = docker-compose ps --services --filter "status=running"
    if ($runningServices) {
        Write-Warning "⚠️  Some services are already running:"
        $runningServices | ForEach-Object { Write-Warning "   - $_" }
        Write-Info ""
    }
    
    # Start the stack
    docker-compose up -d
    
    # Wait a moment for services to start
    Start-Sleep -Seconds 5
    
    # Check service status
    Write-Info ""
    Write-Info "📊 Service Status:"
    docker-compose ps
    
    Write-Info ""
    Write-Success "✅ Monitoring stack started successfully!"
    Write-Info ""
    Write-Info "🌐 Access URLs:"
    Write-Info "  Grafana:      http://localhost:3000 (admin/admin)"
    Write-Info "  Prometheus:   http://localhost:9090"
    Write-Info "  AlertManager: http://localhost:9093"
    Write-Info ""
    Write-Info "📈 Next Steps:"
    Write-Info "  1. Start SmaTrendFollower with metrics: dotnet run --project ../SmaTrendFollower.Console -- metrics-api"
    Write-Info "  2. Open Grafana and explore the pre-configured dashboards"
    Write-Info "  3. Check Prometheus targets at http://localhost:9090/targets"
    Write-Info ""
    Write-Info "🔧 Management Commands:"
    Write-Info "  Stop:    .\start-monitoring.ps1 -Stop"
    Write-Info "  Restart: .\start-monitoring.ps1 -Restart"
    Write-Info "  Status:  .\start-monitoring.ps1 -Status"
    Write-Info "  Logs:    .\start-monitoring.ps1 -Logs"
}
