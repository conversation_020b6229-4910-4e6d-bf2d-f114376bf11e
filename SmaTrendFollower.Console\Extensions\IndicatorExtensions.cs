using Alpaca.Markets;
using Skender.Stock.Indicators;
using SmaTrendFollower.Models;

namespace SmaTrendFollower.Console.Extensions;

/// <summary>
/// Static extension methods for converting Alpaca bar data to Skender Stock Indicators and calculating technical indicators
/// </summary>
public static class IndicatorExtensions
{
    /// <summary>
    /// Converts Alpaca bar series to Skender Stock.Indicators Quote list
    /// </summary>
    /// <param name="bars">Collection of Alpaca bars</param>
    /// <returns>List of Quote objects ordered by date</returns>
    /// <exception cref="ArgumentNullException">Thrown when bars is null</exception>
    /// <exception cref="ArgumentException">Thrown when bars collection is empty</exception>
    public static List<Quote> ToQuotes(this IEnumerable<IBar> bars)
    {
        if (bars == null)
            throw new ArgumentNullException(nameof(bars));

        var barList = bars.ToList();
        if (barList.Count == 0)
            throw new ArgumentException("Bars collection cannot be empty", nameof(bars));

        return barList.Select(bar => new Quote
        {
            Date = bar.TimeUtc,
            Open = bar.Open,
            High = bar.High,
            Low = bar.Low,
            Close = bar.Close,
            Volume = bar.Volume
        }).OrderBy(q => q.Date).ToList();
    }

    /// <summary>
    /// Calculates 14-period Average True Range and returns the latest value
    /// </summary>
    /// <param name="bars">Collection of Alpaca bars</param>
    /// <returns>Latest ATR value</returns>
    /// <exception cref="ArgumentException">Thrown when insufficient bars for ATR calculation (need at least 14)</exception>
    public static double GetAtr14(this IEnumerable<IBar> bars)
    {
        var quotes = bars.ToQuotes();
        
        if (quotes.Count < 14)
            throw new ArgumentException($"Insufficient bars for ATR14 calculation. Got {quotes.Count} bars, need at least 14", nameof(bars));

        var atrResults = quotes.GetAtr(14).ToList();
        var latestAtr = atrResults.LastOrDefault()?.Atr;

        if (!latestAtr.HasValue)
            throw new ArgumentException("Could not calculate ATR14 - no valid result returned", nameof(bars));

        return latestAtr.Value;
    }

    /// <summary>
    /// Calculates 50-period Simple Moving Average and returns the latest value
    /// </summary>
    /// <param name="bars">Collection of Alpaca bars</param>
    /// <returns>Latest SMA50 value</returns>
    /// <exception cref="ArgumentException">Thrown when insufficient bars for SMA50 calculation (need at least 50)</exception>
    public static double GetSma50(this IEnumerable<IBar> bars)
    {
        var quotes = bars.ToQuotes();
        
        if (quotes.Count < 50)
            throw new ArgumentException($"Insufficient bars for SMA50 calculation. Got {quotes.Count} bars, need at least 50", nameof(bars));

        var smaResults = quotes.GetSma(50).ToList();
        var latestSma = smaResults.LastOrDefault()?.Sma;

        if (!latestSma.HasValue)
            throw new ArgumentException("Could not calculate SMA50 - no valid result returned", nameof(bars));

        return latestSma.Value;
    }

    /// <summary>
    /// Calculates 200-period Simple Moving Average and returns the latest value
    /// </summary>
    /// <param name="bars">Collection of Alpaca bars</param>
    /// <returns>Latest SMA200 value</returns>
    /// <exception cref="ArgumentException">Thrown when insufficient bars for SMA200 calculation (need at least 200)</exception>
    public static double GetSma200(this IEnumerable<IBar> bars)
    {
        var quotes = bars.ToQuotes();
        
        if (quotes.Count < 200)
            throw new ArgumentException($"Insufficient bars for SMA200 calculation. Got {quotes.Count} bars, need at least 200", nameof(bars));

        var smaResults = quotes.GetSma(200).ToList();
        var latestSma = smaResults.LastOrDefault()?.Sma;

        if (!latestSma.HasValue)
            throw new ArgumentException("Could not calculate SMA200 - no valid result returned", nameof(bars));

        return latestSma.Value;
    }

    /// <summary>
    /// Calculates total return over specified number of days
    /// </summary>
    /// <param name="bars">Collection of Alpaca bars</param>
    /// <param name="days">Number of days to calculate return over</param>
    /// <returns>Total return as a percentage (e.g., 0.05 for 5% return)</returns>
    /// <exception cref="ArgumentException">Thrown when insufficient bars for return calculation or invalid days parameter</exception>
    public static double GetTotalReturn(this IEnumerable<IBar> bars, int days)
    {
        if (days <= 0)
            throw new ArgumentException("Days must be greater than 0", nameof(days));

        var quotes = bars.ToQuotes();
        
        if (quotes.Count < days + 1)
            throw new ArgumentException($"Insufficient bars for {days}-day return calculation. Got {quotes.Count} bars, need at least {days + 1}", nameof(bars));

        var latestPrice = quotes.Last().Close;
        var startPrice = quotes[quotes.Count - days - 1].Close;

        if (startPrice == 0)
            throw new ArgumentException("Start price cannot be zero for return calculation", nameof(bars));

        return (double)((latestPrice - startPrice) / startPrice);
    }

    /// <summary>
    /// Calculates 14-period Relative Strength Index and returns the latest value
    /// </summary>
    /// <param name="bars">Collection of Alpaca bars</param>
    /// <returns>Latest RSI14 value</returns>
    /// <exception cref="ArgumentException">Thrown when insufficient bars for RSI14 calculation (need at least 14)</exception>
    public static double GetRsi14(this IEnumerable<IBar> bars)
    {
        var quotes = bars.ToQuotes();

        if (quotes.Count < 14)
            throw new ArgumentException($"Insufficient bars for RSI14 calculation. Got {quotes.Count} bars, need at least 14", nameof(bars));

        var rsiResults = quotes.GetRsi(14).ToList();
        var latestRsi = rsiResults.LastOrDefault()?.Rsi;

        if (!latestRsi.HasValue)
            throw new ArgumentException("Could not calculate RSI14 - no valid result returned", nameof(bars));

        return latestRsi.Value;
    }

    /// <summary>
    /// Calculates MACD (12,26,9) and returns the latest values
    /// </summary>
    /// <param name="bars">Collection of Alpaca bars</param>
    /// <returns>Latest MACD result with MACD line, Signal line, and Histogram</returns>
    /// <exception cref="ArgumentException">Thrown when insufficient bars for MACD calculation (need at least 34)</exception>
    public static Models.MacdResult GetMacd(this IEnumerable<IBar> bars)
    {
        var quotes = bars.ToQuotes();

        if (quotes.Count < 34) // Need at least 34 bars for MACD(12,26,9)
            throw new ArgumentException($"Insufficient bars for MACD calculation. Got {quotes.Count} bars, need at least 34", nameof(bars));

        var macdResults = quotes.GetMacd(12, 26, 9).ToList();
        var latestMacd = macdResults.LastOrDefault();

        if (latestMacd?.Macd == null || latestMacd?.Signal == null || latestMacd?.Histogram == null)
            throw new ArgumentException("Could not calculate MACD - no valid result returned", nameof(bars));

        return new Models.MacdResult(
            latestMacd.Macd.Value,
            latestMacd.Signal.Value,
            latestMacd.Histogram.Value
        );
    }
}
