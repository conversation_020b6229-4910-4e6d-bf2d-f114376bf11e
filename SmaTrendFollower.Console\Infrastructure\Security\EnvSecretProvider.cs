using Microsoft.Extensions.Logging;

namespace SmaTrendFollower.Infrastructure.Security;

/// <summary>
/// Environment variable-based secret provider for development and fallback scenarios.
/// Retrieves secrets from environment variables when HashiCorp Vault is not available.
/// </summary>
public sealed class EnvSecretProvider : ISecretProvider
{
    private readonly ILogger<EnvSecretProvider>? _logger;

    /// <summary>
    /// Initializes a new instance of the EnvSecretProvider.
    /// </summary>
    /// <param name="logger">Optional logger for diagnostics</param>
    public EnvSecretProvider(ILogger<EnvSecretProvider>? logger = null)
    {
        _logger = logger;
        _logger?.LogInformation("Initialized environment variable secret provider");
    }

    /// <inheritdoc />
    public string ProviderName => "Environment Variables";

    /// <inheritdoc />
    public bool IsConfigured => true; // Environment variables are always available

    /// <inheritdoc />
    public string Get(string key)
    {
        if (string.IsNullOrWhiteSpace(key))
        {
            throw new ArgumentException("Secret key cannot be null or whitespace", nameof(key));
        }

        var value = Environment.GetEnvironmentVariable(key);
        
        if (string.IsNullOrEmpty(value))
        {
            _logger?.LogError("Environment variable {Key} not found or empty", key);
            throw new KeyNotFoundException($"Environment variable '{key}' not found or empty");
        }

        _logger?.LogDebug("Successfully retrieved environment variable {Key}", key);
        return value;
    }

    /// <inheritdoc />
    public bool TryGet(string key, out string? value)
    {
        value = null;

        if (string.IsNullOrWhiteSpace(key))
        {
            _logger?.LogWarning("Attempted to retrieve secret with null or whitespace key");
            return false;
        }

        try
        {
            value = Environment.GetEnvironmentVariable(key);
            var found = !string.IsNullOrEmpty(value);
            
            if (found)
            {
                _logger?.LogDebug("Successfully retrieved environment variable {Key}", key);
            }
            else
            {
                _logger?.LogDebug("Environment variable {Key} not found or empty", key);
            }

            return found;
        }
        catch (Exception ex)
        {
            _logger?.LogError(ex, "Error retrieving environment variable {Key}", key);
            return false;
        }
    }

    /// <inheritdoc />
    public bool Exists(string key)
    {
        if (string.IsNullOrWhiteSpace(key))
        {
            return false;
        }

        try
        {
            var value = Environment.GetEnvironmentVariable(key);
            return !string.IsNullOrEmpty(value);
        }
        catch (Exception ex)
        {
            _logger?.LogError(ex, "Error checking existence of environment variable {Key}", key);
            return false;
        }
    }
}
