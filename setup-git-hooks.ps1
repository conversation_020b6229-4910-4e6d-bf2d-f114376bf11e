# Setup Git hooks for SmaTrendFollower
# This script configures Git to use the custom hooks in .githooks/

Write-Host "Setting up Git hooks for SmaTrendFollower..." -ForegroundColor Green

# Configure Git to use .githooks directory
git config core.hooksPath .githooks

# Make hooks executable (for Unix-like systems)
if ($IsLinux -or $IsMacOS) {
    chmod +x .githooks/pre-push
    Write-Host "Made hooks executable" -ForegroundColor Yellow
}

Write-Host "Git hooks configured successfully!" -ForegroundColor Green
Write-Host "Pre-push hook will now run tests before each push." -ForegroundColor Cyan
