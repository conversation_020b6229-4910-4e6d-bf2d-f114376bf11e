using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using StackExchange.Redis;
using SmaTrendFollower.Services;
using SmaTrendFollower.Configuration;

namespace SmaTrendFollower.Examples;

/// <summary>
/// Example demonstrating the SyntheticVixService and SyntheticVixTrainer
/// Shows how to use the enhanced synthetic VIX calculation with ML-trained weights
/// </summary>
public static class SyntheticVixServiceExample
{
    public static async Task RunAsync()
    {
        System.Console.WriteLine("🚀 SyntheticVixService + Weekly Trainer Example");
        System.Console.WriteLine("================================================");

        // Create host with full service configuration
        var host = CreateHost();
        
        try
        {
            // Get services
            var syntheticVixService = host.Services.GetRequiredService<ISyntheticVixService>();
            var vixTrainer = host.Services.GetRequiredService<SyntheticVixTrainer>();
            // Note: Logger not used in this demo, but available for debugging

            System.Console.WriteLine("✅ Services registered successfully");

            // Check if weights are fresh
            var areWeightsFresh = await syntheticVixService.AreWeightsFreshAsync();
            System.Console.WriteLine($"📊 Regression weights fresh: {areWeightsFresh}");

            // Get current weights
            var currentWeights = await syntheticVixService.GetCurrentWeightsAsync();
            if (currentWeights != null)
            {
                System.Console.WriteLine("📈 Current Regression Weights:");
                System.Console.WriteLine($"   VXX Coefficient: {currentWeights.VxxCoefficient:F4}");
                System.Console.WriteLine($"   UVXY Coefficient: {currentWeights.UvxyCoefficient:F4}");
                System.Console.WriteLine($"   SVXY Coefficient: {currentWeights.SvxyCoefficient:F4}");
                System.Console.WriteLine($"   SPY Coefficient: {currentWeights.SpyCoefficient:F4}");
                System.Console.WriteLine($"   Intercept: {currentWeights.Intercept:F4}");
                System.Console.WriteLine($"   R-Squared: {currentWeights.RSquared:F3}");
                System.Console.WriteLine($"   Sample Size: {currentWeights.SampleSize}");
                System.Console.WriteLine($"   Trained At: {currentWeights.TrainedAt:yyyy-MM-dd HH:mm:ss} UTC");
            }
            else
            {
                System.Console.WriteLine("⚠️  No trained weights found - will use static fallback");
            }

            // Demonstrate training (would normally run weekly via Quartz)
            System.Console.WriteLine("\n🎯 Demonstrating Weekly Training Process...");
            try
            {
                // Note: This will fail without real API keys and Redis, but shows the workflow
                await vixTrainer.TrainAsync();
                System.Console.WriteLine("✅ Training completed successfully");
            }
            catch (Exception ex)
            {
                System.Console.WriteLine($"⚠️  Training failed (expected in demo): {ex.Message}");
                System.Console.WriteLine("   (This is normal without real API keys and Redis connection)");
            }

            // Demonstrate synthetic VIX estimation
            System.Console.WriteLine("\n💡 Demonstrating Synthetic VIX Estimation...");
            try
            {
                var syntheticVix = await syntheticVixService.EstimateAsync();
                if (syntheticVix.HasValue)
                {
                    System.Console.WriteLine($"📊 Synthetic VIX Estimate: {syntheticVix.Value:F2}");
                }
                else
                {
                    System.Console.WriteLine("⚠️  Synthetic VIX estimation failed (expected in demo)");
                    System.Console.WriteLine("   (This is normal without real API keys and fresh ETF data)");
                }
            }
            catch (Exception ex)
            {
                System.Console.WriteLine($"⚠️  Estimation failed (expected in demo): {ex.Message}");
                System.Console.WriteLine("   (This is normal without real API keys and Redis connection)");
            }

            // Show integration with VIXResolverService
            System.Console.WriteLine("\n🔗 Integration with VIXResolverService:");
            System.Console.WriteLine("   • VIXResolverService now uses SyntheticVixService for Level 3 fallback");
            System.Console.WriteLine("   • Enhanced synthetic calculation with ML-trained regression weights");
            System.Console.WriteLine("   • Falls back to legacy calculation if enhanced service fails");
            System.Console.WriteLine("   • Enforces 15-minute data freshness requirement");

            // Show Quartz scheduling
            System.Console.WriteLine("\n⏰ Quartz.NET Scheduling:");
            System.Console.WriteLine("   • SyntheticVixTrainer scheduled weekly on Sunday at 6:00 PM ET");
            System.Console.WriteLine("   • Automatically trains new regression weights using 1-year historical data");
            System.Console.WriteLine("   • Validates model quality (R² > 0.5) before storing new weights");
            System.Console.WriteLine("   • Stores weights in Redis with 30-day TTL");

            // Show production readiness features
            System.Console.WriteLine("\n🏭 Production Readiness Features:");
            System.Console.WriteLine("   • Comprehensive error handling and logging");
            System.Console.WriteLine("   • Data freshness validation (18-minute threshold)");
            System.Console.WriteLine("   • Fallback to static coefficients when training unavailable");
            System.Console.WriteLine("   • Redis caching with appropriate TTLs");
            System.Console.WriteLine("   • Dependency injection and service registration");
            System.Console.WriteLine("   • Thread-safe implementation with semaphore locks");

            System.Console.WriteLine("\n✅ SyntheticVixService demonstration completed!");
        }
        catch (Exception ex)
        {
            System.Console.WriteLine($"❌ Error during demonstration: {ex.Message}");
            System.Console.WriteLine("   This is expected without proper Redis and API configuration");
        }
        finally
        {
            await host.StopAsync();
            host.Dispose();
        }
    }

    private static IHost CreateHost()
    {
        var configuration = new ConfigurationBuilder()
            .AddInMemoryCollection(new Dictionary<string, string?>
            {
                ["POLYGON_API_KEY"] = "demo-polygon-key",
                ["ALPACA_API_KEY"] = "demo-alpaca-key", 
                ["ALPACA_SECRET_KEY"] = "demo-alpaca-secret",
                ["REDIS_CONNECTION"] = "localhost:6379"
            })
            .Build();

        return Host.CreateDefaultBuilder()
            .ConfigureServices((context, services) =>
            {
                // Add configuration
                services.AddSingleton<IConfiguration>(configuration);

                // Add logging
                services.AddLogging(builder => 
                {
                    builder.AddConsole();
                    builder.SetMinimumLevel(LogLevel.Information);
                });

                // Add HTTP client factory
                services.AddHttpClient();

                // Add Redis (will fail in demo, but shows registration)
                try
                {
                    var redis = ConnectionMultiplexer.Connect("*************:6379");
                    services.AddSingleton(redis);
                }
                catch
                {
                    // Create a mock Redis connection for demo
                    services.AddSingleton<ConnectionMultiplexer>(provider =>
                    {
                        throw new InvalidOperationException("Redis not available in demo environment");
                    });
                }

                // Add our services using the service configuration
                services.AddMarketDataServices();
                services.AddSchedulingServices();
            })
            .Build();
    }
}

/// <summary>
/// Console command to run the SyntheticVixService example
/// Usage: dotnet run synthetic-vix-example
/// </summary>
public static class SyntheticVixExampleCommand
{
    public static async Task<int> ExecuteAsync(string[] args)
    {
        if (args.Length > 0 && args[0].Equals("synthetic-vix-example", StringComparison.OrdinalIgnoreCase))
        {
            await SyntheticVixServiceExample.RunAsync();
            return 0;
        }
        
        return -1; // Not handled
    }
}
