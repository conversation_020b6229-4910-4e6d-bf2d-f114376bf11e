using Microsoft.Extensions.Logging;
using Quartz;
using StackExchange.Redis;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace SmaTrendFollower.Services;

/// <summary>
/// Automated Redis key hygiene service that ensures all keys have appropriate TTL values
/// Runs as a scheduled Quartz job to prevent memory leaks from keys without expiration
/// </summary>
[DisallowConcurrentExecution]
public sealed class RedisCleanupService : IJob, IDisposable
{
    private readonly IOptimizedRedisConnectionService _redisService;
    private readonly ITradingMetricsService? _metricsService;
    private readonly ILogger<RedisCleanupService> _logger;
    private readonly SemaphoreSlim _cleanupLock = new(1, 1);
    private bool _disposed;

    public RedisCleanupService(
        IOptimizedRedisConnectionService redisService,
        ILogger<RedisCleanupService> logger,
        ITradingMetricsService? metricsService = null)
    {
        _redisService = redisService ?? throw new ArgumentNullException(nameof(redisService));
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        _metricsService = metricsService; // Optional dependency for metrics
    }

    /// <summary>
    /// Quartz.NET job execution entry point
    /// Performs Redis key hygiene by applying TTLs to keys without expiration
    /// </summary>
    public async Task Execute(IJobExecutionContext context)
    {
        if (_disposed)
        {
            _logger.LogWarning("RedisCleanupService is disposed, skipping execution");
            return;
        }

        await _cleanupLock.WaitAsync(context.CancellationToken);
        try
        {
            _logger.LogInformation("Starting Redis key hygiene cleanup");
            var startTime = DateTime.UtcNow;
            
            var stats = await PerformCleanupAsync(context.CancellationToken);
            
            var duration = DateTime.UtcNow - startTime;
            _logger.LogInformation(
                "Redis cleanup completed in {Duration:F2}s. Keys processed: {Processed}, TTLs applied: {Fixed}, Errors: {Errors}",
                duration.TotalSeconds, stats.KeysProcessed, stats.TTLsApplied, stats.Errors);

            // Record metrics if available
            await RecordCleanupMetricsAsync(stats, duration);
        }
        catch (OperationCanceledException)
        {
            _logger.LogWarning("Redis cleanup was cancelled");
            throw;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during Redis cleanup execution");
            throw;
        }
        finally
        {
            _cleanupLock.Release();
        }
    }

    /// <summary>
    /// Performs the actual Redis cleanup operation
    /// </summary>
    private async Task<CleanupStats> PerformCleanupAsync(CancellationToken cancellationToken)
    {
        var stats = new CleanupStats();

        try
        {
            var database = await _redisService.GetDatabaseAsync();
            var server = database.Multiplexer.GetServer(database.Multiplexer.GetEndPoints().First());

            // Process each key pattern defined in RedisKeyConstants
            foreach (var (pattern, expectedTtl) in RedisKeyConstants.PatternToTTL)
            {
                if (cancellationToken.IsCancellationRequested)
                    break;

                await ProcessKeyPattern(database, server, pattern, expectedTtl, stats, cancellationToken);
            }

            // Also scan for any orphaned keys that don't match known patterns
            await ScanForOrphanedKeys(database, server, stats, cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during Redis cleanup operation");
            stats.Errors++;
            throw;
        }

        return stats;
    }

    /// <summary>
    /// Processes keys matching a specific pattern and applies TTL if missing
    /// </summary>
    private async Task ProcessKeyPattern(
        IDatabase database,
        IServer server,
        string pattern,
        TimeSpan expectedTtl,
        CleanupStats stats,
        CancellationToken cancellationToken)
    {
        try
        {
            var keysProcessed = 0;
            var ttlsApplied = 0;

            await foreach (var key in server.KeysAsync(pattern: pattern))
            {
                if (cancellationToken.IsCancellationRequested)
                    break;

                keysProcessed++;
                stats.KeysProcessed++;

                try
                {
                    var currentTtl = await database.KeyTimeToLiveAsync(key);
                    
                    // Apply TTL if key has no expiration (null) or if it's a health check key that should be cleaned up
                    if (currentTtl == null || (pattern.Contains("health_check") && currentTtl > TimeSpan.FromHours(1)))
                    {
                        await database.KeyExpireAsync(key, expectedTtl);
                        ttlsApplied++;
                        stats.TTLsApplied++;
                        
                        _logger.LogDebug("Applied TTL {TTL} to key {Key}", expectedTtl, key);
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogWarning(ex, "Failed to process key {Key}", key);
                    stats.Errors++;
                }
            }

            if (keysProcessed > 0)
            {
                _logger.LogDebug("Pattern {Pattern}: processed {Processed} keys, applied {Applied} TTLs", 
                    pattern, keysProcessed, ttlsApplied);
            }
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Error processing pattern {Pattern}", pattern);
            stats.Errors++;
        }
    }

    /// <summary>
    /// Scans for orphaned keys that don't match any known patterns
    /// These keys get a default TTL to prevent indefinite accumulation
    /// </summary>
    private async Task ScanForOrphanedKeys(
        IDatabase database,
        IServer server,
        CleanupStats stats,
        CancellationToken cancellationToken)
    {
        try
        {
            var orphanedKeys = new List<RedisKey>();
            var defaultTtl = TimeSpan.FromDays(1); // Default TTL for unknown keys

            await foreach (var key in server.KeysAsync())
            {
                if (cancellationToken.IsCancellationRequested)
                    break;

                var keyString = key.ToString();
                
                // Skip keys that match known patterns
                if (RedisKeyConstants.GetTTLForKey(keyString) != null)
                    continue;

                // Check if this orphaned key has no TTL
                var currentTtl = await database.KeyTimeToLiveAsync(key);
                if (currentTtl == null)
                {
                    orphanedKeys.Add(key);
                    
                    // Process in batches to avoid overwhelming Redis
                    if (orphanedKeys.Count >= 100)
                    {
                        await ProcessOrphanedKeyBatch(database, orphanedKeys, defaultTtl, stats);
                        orphanedKeys.Clear();
                    }
                }
            }

            // Process remaining orphaned keys
            if (orphanedKeys.Count > 0)
            {
                await ProcessOrphanedKeyBatch(database, orphanedKeys, defaultTtl, stats);
            }
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Error scanning for orphaned keys");
            stats.Errors++;
        }
    }

    /// <summary>
    /// Processes a batch of orphaned keys by applying default TTL
    /// </summary>
    private async Task ProcessOrphanedKeyBatch(
        IDatabase database,
        List<RedisKey> keys,
        TimeSpan defaultTtl,
        CleanupStats stats)
    {
        foreach (var key in keys)
        {
            try
            {
                await database.KeyExpireAsync(key, defaultTtl);
                stats.TTLsApplied++;
                stats.KeysProcessed++;
                
                _logger.LogDebug("Applied default TTL {TTL} to orphaned key {Key}", defaultTtl, key);
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Failed to apply TTL to orphaned key {Key}", key);
                stats.Errors++;
            }
        }

        if (keys.Count > 0)
        {
            _logger.LogInformation("Applied default TTL to {Count} orphaned keys", keys.Count);
        }
    }

    /// <summary>
    /// Records cleanup metrics to the trading metrics service
    /// </summary>
    private async Task RecordCleanupMetricsAsync(CleanupStats stats, TimeSpan duration)
    {
        if (_metricsService == null)
            return;

        try
        {
            // Record cleanup performance
            await _metricsService.RecordPerformanceAsync(
                "redis_cleanup",
                duration,
                stats.Errors == 0,
                $"Keys: {stats.KeysProcessed}, TTLs: {stats.TTLsApplied}, Errors: {stats.Errors}");

            // Record specific metrics
            await _metricsService.RecordSystemMetricAsync("redis_keys_processed", stats.KeysProcessed, "count");
            await _metricsService.RecordSystemMetricAsync("redis_ttls_applied", stats.TTLsApplied, "count");
            await _metricsService.RecordSystemMetricAsync("redis_cleanup_errors", stats.Errors, "count");
            await _metricsService.RecordSystemMetricAsync("redis_cleanup_duration_seconds", (decimal)duration.TotalSeconds, "seconds");

            _logger.LogDebug("Redis cleanup metrics recorded successfully");
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Failed to record Redis cleanup metrics");
        }
    }

    public void Dispose()
    {
        if (_disposed)
            return;

        _cleanupLock?.Dispose();
        _disposed = true;
    }

    /// <summary>
    /// Statistics for cleanup operation
    /// </summary>
    private class CleanupStats
    {
        public int KeysProcessed { get; set; }
        public int TTLsApplied { get; set; }
        public int Errors { get; set; }
    }
}
