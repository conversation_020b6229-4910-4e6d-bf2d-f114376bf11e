namespace SmaTrendFollower.Services;

/// <summary>
/// Polygon WebSocket channel types for different data streams
/// Each channel corresponds to specific Polygon WebSocket subscription patterns
/// </summary>
public enum PolygonWsChannel
{
    /// <summary>
    /// Equity trade updates (T.* subscriptions)
    /// Real-time trade execution data for individual stocks
    /// </summary>
    EquityTrades,

    /// <summary>
    /// Equity quote updates (Q.* subscriptions)
    /// Real-time bid/ask quote data for individual stocks
    /// </summary>
    EquityQuotes,

    /// <summary>
    /// Equity minute aggregates (AM.* subscriptions)
    /// 1-minute OHLCV bars for individual stocks
    /// </summary>
    EquityMinute,

    /// <summary>
    /// Index minute aggregates (AM.I:* subscriptions)
    /// 1-minute OHLCV bars for market indices (VIX, SPX, NDX, etc.)
    /// </summary>
    IndexMinute
}

/// <summary>
/// Extension methods for PolygonWsChannel enum
/// </summary>
public static class PolygonWsChannelExtensions
{
    /// <summary>
    /// Gets the Polygon WebSocket URL for the specified channel
    /// </summary>
    /// <param name="channel">The WebSocket channel</param>
    /// <returns>The appropriate WebSocket URL</returns>
    public static string GetWebSocketUrl(this PolygonWsChannel channel)
    {
        return channel switch
        {
            PolygonWsChannel.EquityTrades => "wss://socket.polygon.io/stocks",
            PolygonWsChannel.EquityQuotes => "wss://socket.polygon.io/stocks",
            PolygonWsChannel.EquityMinute => "wss://socket.polygon.io/stocks",
            PolygonWsChannel.IndexMinute => "wss://socket.polygon.io/indices",
            _ => throw new ArgumentOutOfRangeException(nameof(channel), channel, "Unknown WebSocket channel")
        };
    }

    /// <summary>
    /// Gets the subscription prefix for the specified channel
    /// </summary>
    /// <param name="channel">The WebSocket channel</param>
    /// <returns>The subscription prefix (e.g., "T.", "Q.", "AM.")</returns>
    public static string GetSubscriptionPrefix(this PolygonWsChannel channel)
    {
        return channel switch
        {
            PolygonWsChannel.EquityTrades => "T.",
            PolygonWsChannel.EquityQuotes => "Q.",
            PolygonWsChannel.EquityMinute => "AM.",
            PolygonWsChannel.IndexMinute => "AM.I:",
            _ => throw new ArgumentOutOfRangeException(nameof(channel), channel, "Unknown WebSocket channel")
        };
    }

    /// <summary>
    /// Gets the Redis key pattern for storing subscriptions for this channel
    /// </summary>
    /// <param name="channel">The WebSocket channel</param>
    /// <returns>The Redis key for storing subscription lists</returns>
    public static string GetRedisSubscriptionKey(this PolygonWsChannel channel)
    {
        return $"ws:subs:{channel}";
    }

    /// <summary>
    /// Gets a human-readable description of the channel
    /// </summary>
    /// <param name="channel">The WebSocket channel</param>
    /// <returns>A descriptive string for logging and monitoring</returns>
    public static string GetDescription(this PolygonWsChannel channel)
    {
        return channel switch
        {
            PolygonWsChannel.EquityTrades => "Equity Trade Updates",
            PolygonWsChannel.EquityQuotes => "Equity Quote Updates",
            PolygonWsChannel.EquityMinute => "Equity Minute Aggregates",
            PolygonWsChannel.IndexMinute => "Index Minute Aggregates",
            _ => throw new ArgumentOutOfRangeException(nameof(channel), channel, "Unknown WebSocket channel")
        };
    }

    /// <summary>
    /// Formats a list of symbols for subscription to this channel
    /// </summary>
    /// <param name="channel">The WebSocket channel</param>
    /// <param name="symbols">The symbols to subscribe to</param>
    /// <returns>Formatted subscription parameters string</returns>
    public static string FormatSubscriptionParams(this PolygonWsChannel channel, IEnumerable<string> symbols)
    {
        var prefix = channel.GetSubscriptionPrefix();
        return string.Join(",", symbols.Select(symbol => $"{prefix}{symbol}"));
    }
}
