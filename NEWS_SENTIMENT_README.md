# News Sentiment Analysis System

This document describes the real-time news sentiment analysis system that integrates Alpaca news feeds with Gemini AI for automated sentiment scoring and ML feature enhancement.

## Overview

The system ingests real-time news headlines from Alpaca, analyzes sentiment using Google's Gemini AI, stores the results in Redis, and feeds sentiment scores into the ML-enhanced signal generation pipeline.

## Architecture

### Core Components

1. **GeminiClient** - Interface to Google's Gemini AI for sentiment analysis
2. **NewsSentimentService** - Background service that processes Alpaca news feeds
3. **MomentumModelTrainer** - Enhanced to include sentiment features in ML models
4. **MLEnhancedSignalGenerator** - Updated to use sentiment in feature extraction

### Data Flow

```
Alpaca News Stream → NewsSentimentService → Gemini AI → Redis → ML Features → Trading Signals
```

## Configuration

### 1. Environment Variables

Add the following environment variables:

```bash
# Gemini AI API Key
GEMINI_API_KEY=your-gemini-api-key

# Alpaca credentials (already configured)
APCA_API_KEY_ID=your-alpaca-key
APCA_API_SECRET=your-alpaca-secret
```

### 2. appsettings.json

The system automatically configures the following sections:

```json
{
  "AlpacaNews": {
    "Endpoint": "wss://stream.data.alpaca.markets/v1beta1/news",
    "KeyIdEnv": "APCA_API_KEY_ID",
    "SecretEnv": "APCA_API_SECRET"
  },
  "Gemini": {
    "Endpoint": "https://generativelanguage.googleapis.com/v1beta/models/gemini-pro:generateContent",
    "ApiKeyEnv": "GEMINI_API_KEY",
    "TimeoutSec": 45
  }
}
```

## Usage

### Local Development

1. **Set up environment variables:**
   ```bash
   export GEMINI_API_KEY="your-gemini-api-key"
   export APCA_API_KEY_ID="your-alpaca-key"
   export APCA_API_SECRET="your-alpaca-secret"
   ```

2. **Run the application:**
   ```bash
   ASPNETCORE_ENVIRONMENT=LocalProd dotnet run -c Release --project SmaTrendFollower.Console
   ```

### Docker Deployment

1. **Create .env file:**
   ```bash
   GEMINI_API_KEY=your-gemini-api-key
   ```

2. **Build and run:**
   ```bash
   docker compose build bot
   docker compose up -d bot
   ```

### Verify Operation

Check logs for sentiment analysis activity:

```bash
# Docker logs
docker compose logs bot | grep Sentiment

# Local logs
grep Sentiment logs/sma-trend-follower-$(date +%Y%m%d).log
```

Expected log entries:
```
[INFO] Stored sentiment for AAPL: 0.750 (News: 12345)
[INFO] Retrieved sentiment for TSLA on 20250101: 0.250
```

## Data Storage

### Redis Keys

Sentiment data is stored in Redis with the following structure:

```
Sentiment:{SYMBOL}:{YYYYMMDD} -> Hash
  - latest: {sentiment_score}
  - {news_id_1}: {sentiment_score_1}
  - {news_id_2}: {sentiment_score_2}
  - ...
```

**Example:**
```
Sentiment:AAPL:20250101 -> Hash
  - latest: 0.75
  - 12345: 0.80
  - 12346: 0.70
```

### TTL (Time To Live)

- Sentiment data expires after 24 hours
- Automatic cleanup prevents Redis bloat

## ML Integration

### Feature Vector Enhancement

The sentiment score is now included as the 9th feature in the ML model:

```csharp
SignalFeatures(
    SmaGap,         // 1. Technical: Price vs SMA50
    Volatility,     // 2. Technical: ATR/Price ratio
    Rsi,            // 3. Technical: 14-period RSI
    BreadthScore,   // 4. Market: Breadth indicator
    VixLevel,       // 5. Market: Volatility index
    SixMonthReturn, // 6. Momentum: 6-month return
    RelativeVolume, // 7. Volume: Current vs average
    MarketRegime,   // 8. Market: Regime classification
    Sentiment       // 9. News: Sentiment score (-1 to +1)
)
```

### Model Training

The enhanced features are automatically included in:
- Signal ranking models
- Position sizing models
- Slippage forecasting models

## Cost Management

### Universe Filtering

The system only processes news for symbols in the current trading universe, reducing API costs:

```csharp
// Only analyze sentiment for universe symbols
if (!_universeProvider.TodaySymbols.Contains(newsSymbol)) {
    continue; // Skip processing
}
```

### Rate Limiting

- Maximum 5 concurrent Gemini API calls
- Automatic backoff on API errors
- Graceful degradation when sentiment unavailable

## Monitoring

### Prometheus Metrics

The system exposes metrics for monitoring:

- `sentiment_requests_total` - Total sentiment analysis requests
- `sentiment_errors_total` - Failed sentiment analysis requests
- `sentiment_cache_hits_total` - Redis cache hits
- `sentiment_processing_duration_seconds` - Processing time

### Health Checks

Monitor system health through:

1. **Redis connectivity** - Sentiment storage availability
2. **Alpaca news stream** - Real-time news feed status
3. **Gemini API** - Sentiment analysis service status

## Troubleshooting

### Common Issues

1. **No sentiment data:**
   - Check Gemini API key configuration
   - Verify Alpaca news stream connection
   - Ensure symbols are in trading universe

2. **High API costs:**
   - Review universe size (should be ~200 symbols)
   - Check rate limiting configuration
   - Monitor Gemini API usage

3. **Stale sentiment scores:**
   - Verify Redis TTL settings (24 hours)
   - Check news stream connectivity
   - Review error logs for processing failures

### Debug Commands

```bash
# Check Redis sentiment data
redis-cli HGETALL "Sentiment:AAPL:$(date +%Y%m%d)"

# Monitor news processing
docker compose logs -f bot | grep "NewsSentimentService"

# Check ML feature integration
grep "sentiment" logs/ml-training-*.log
```

## Performance

### Expected Throughput

- **News processing:** ~100-500 headlines/hour during market hours
- **Sentiment analysis:** ~5-10 requests/second (rate limited)
- **Redis storage:** <1ms per sentiment score
- **ML integration:** No additional latency (cached lookups)

### Resource Usage

- **Memory:** +50MB for news processing buffers
- **CPU:** +5-10% during active news periods
- **Network:** ~1-5 KB/s for Gemini API calls
- **Redis:** ~1-10 MB/day for sentiment data

## Future Enhancements

1. **Multi-source sentiment** - Integrate additional news sources
2. **Sentiment aggregation** - Weighted averaging across sources
3. **Historical sentiment** - Long-term sentiment trend analysis
4. **Sentiment momentum** - Rate of sentiment change features
5. **Entity-specific sentiment** - Company vs sector sentiment separation
