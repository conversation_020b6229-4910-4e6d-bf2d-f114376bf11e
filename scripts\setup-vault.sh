#!/usr/bin/env bash
# Vault Setup Script for SmaTrendFollower
# This script initializes Vault with the required secrets

set -e

# Colors for output
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

# Vault configuration
VAULT_ADDR="http://localhost:8200"
VAULT_TOKEN="root"

print_status "Setting up Vault secrets for SmaTrendFollower..."

# Check if Vault is running
if ! curl -s "$VAULT_ADDR/v1/sys/health" > /dev/null; then
    print_warning "Vault is not running. Please start the Docker stack first:"
    echo "  docker-compose up -d vault"
    exit 1
fi

# Export Vault environment variables
export VAULT_ADDR
export VAULT_TOKEN

print_status "Vault is running. Setting up secrets..."

# Create the secrets
print_status "Creating secret store..."

# Prompt for API keys if not provided as environment variables
if [ -z "$POLYGON_API_KEY" ]; then
    echo -n "Enter Polygon API Key: "
    read -r POLYGON_API_KEY
fi

if [ -z "$ALPACA_KEY_ID" ]; then
    echo -n "Enter Alpaca Key ID: "
    read -r ALPACA_KEY_ID
fi

if [ -z "$ALPACA_SECRET" ]; then
    echo -n "Enter Alpaca Secret: "
    read -s ALPACA_SECRET
    echo
fi

if [ -z "$DISCORD_BOT_TOKEN" ]; then
    echo -n "Enter Discord Bot Token (optional): "
    read -r DISCORD_BOT_TOKEN
fi

if [ -z "$DISCORD_CHANNEL_ID" ]; then
    echo -n "Enter Discord Channel ID (optional): "
    read -r DISCORD_CHANNEL_ID
fi

if [ -z "$OPENAI_API_KEY" ]; then
    echo -n "Enter OpenAI API Key (optional): "
    read -s OPENAI_API_KEY
    echo
fi

# Store secrets in Vault
print_status "Storing secrets in Vault..."

vault kv put secret/data/sma \
    POLYGON_API_KEY="$POLYGON_API_KEY" \
    ALPACA_KEY_ID="$ALPACA_KEY_ID" \
    ALPACA_SECRET="$ALPACA_SECRET" \
    DISCORD_BOT_TOKEN="$DISCORD_BOT_TOKEN" \
    DISCORD_CHANNEL_ID="$DISCORD_CHANNEL_ID" \
    OPENAI_API_KEY="$OPENAI_API_KEY"

print_success "Secrets stored successfully in Vault!"

# Verify secrets
print_status "Verifying secrets..."
vault kv get secret/data/sma

print_success "Vault setup completed successfully!"
print_status "You can now start the full stack with: docker-compose up -d"
