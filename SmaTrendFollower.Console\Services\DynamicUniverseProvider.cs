using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using StackExchange.Redis;
using SmaTrendFollower.Models;
using SmaTrendFollower.Monitoring;
using System.Diagnostics;

namespace SmaTrendFollower.Services;

/// <summary>
/// Service for dynamically building and caching tradeable symbol universes based on liquidity, volatility, and price criteria
/// Enhanced with Polygon-based symbol universe integration
/// </summary>
public sealed class DynamicUniverseProvider : IDynamicUniverseProvider, IDisposable
{
    private readonly IMarketDataService _marketDataService;
    private readonly IPolygonSymbolUniverseService? _polygonSymbolService;
    private readonly IDailyUniverseRefreshService? _dailyUniverseService;
    private readonly IUniverseFetcherService? _universeFetcherService;
    private readonly IDatabase? _redis;
    private readonly ConnectionMultiplexer? _connectionMultiplexer;
    private readonly ILogger<DynamicUniverseProvider> _logger;
    private readonly UniverseFilterCriteria _defaultCriteria;
    private readonly bool _usePolygonUniverse;
    
    // Default candidate symbols - can be expanded to include more comprehensive lists
    private static readonly string[] DefaultCandidates = new[]
    {
        // Major indices and ETFs
        "SPY", "QQQ", "IWM", "VTI", "VEA", "VWO", "AGG", "TLT", "GLD", "VIX",
        
        // Large cap tech
        "AAPL", "MSFT", "GOOGL", "GOOG", "AMZN", "TSLA", "META", "NVDA", "NFLX", "ADBE",
        "CRM", "ORCL", "INTC", "AMD", "QCOM", "AVGO", "TXN", "CSCO", "IBM", "UBER",
        
        // Large cap non-tech
        "BRK.B", "UNH", "JNJ", "XOM", "JPM", "V", "PG", "HD", "CVX", "MA",
        "ABBV", "PFE", "KO", "PEP", "TMO", "COST", "WMT", "MRK", "DIS", "ABT",
        
        // Mid cap growth
        "SHOP", "SQ", "ROKU", "ZOOM", "DOCU", "OKTA", "SNOW", "PLTR", "RBLX", "COIN",
        "HOOD", "SOFI", "AFRM", "UPST", "OPEN", "LCID", "RIVN", "F", "GM", "NIO",
        
        // Financial services
        "BAC", "WFC", "GS", "MS", "C", "USB", "PNC", "TFC", "COF", "AXP",
        
        // Healthcare & biotech
        "MRNA", "BNTX", "GILD", "AMGN", "BIIB", "REGN", "VRTX", "ILMN", "ISRG", "DHR",
        
        // Energy & commodities
        "COP", "EOG", "SLB", "HAL", "OXY", "DVN", "FANG", "MPC", "VLO", "PSX",
        
        // Consumer & retail
        "AMZN", "TSLA", "NKE", "SBUX", "MCD", "TGT", "LOW", "TJX", "BKNG", "ABNB",
        
        // Industrial & aerospace
        "BA", "CAT", "DE", "MMM", "HON", "UPS", "FDX", "LMT", "RTX", "GE"
    };

    public DynamicUniverseProvider(
        IMarketDataService marketDataService,
        IConfiguration configuration,
        ILogger<DynamicUniverseProvider> logger,
        IPolygonSymbolUniverseService? polygonSymbolService = null,
        IDailyUniverseRefreshService? dailyUniverseService = null,
        IUniverseFetcherService? universeFetcherService = null)
    {
        _marketDataService = marketDataService;
        _logger = logger;
        _polygonSymbolService = polygonSymbolService;
        _dailyUniverseService = dailyUniverseService;
        _universeFetcherService = universeFetcherService;

        // Check if we should use Polygon-based universe
        _usePolygonUniverse = configuration.GetValue<bool>("UsePolygonUniverse", false) &&
                              _polygonSymbolService != null &&
                              _dailyUniverseService != null;
        
        // Initialize Redis connection if configured (optional)
        try
        {
            var redisUrl =
                configuration.GetSection("Redis")["ConnectionString"]
                ?? configuration["REDIS_URL"]
                ?? Environment.GetEnvironmentVariable("REDIS_CONNECTION_STRING")
                ?? Environment.GetEnvironmentVariable("Redis__ConnectionString")
                ?? "192.168.1.168:6379"; // env-hierarchy form

            if (!string.IsNullOrEmpty(redisUrl))
            {
                var redisDatabase = int.Parse(configuration["REDIS_DATABASE"] ?? "0");
                var redisPassword = configuration["REDIS_PASSWORD"];

                var configOptions = ConfigurationOptions.Parse(redisUrl);
                configOptions.AbortOnConnectFail = false; // Don't fail if Redis is unavailable
                if (!string.IsNullOrEmpty(redisPassword))
                {
                    configOptions.Password = redisPassword;
                }

                _connectionMultiplexer = ConnectionMultiplexer.Connect(configOptions);
                _redis = _connectionMultiplexer.GetDatabase(redisDatabase);
                _logger.LogInformation("Redis connection established for universe caching");
            }
            else
            {
                _logger.LogInformation("Redis not configured - universe caching disabled");
                _connectionMultiplexer = null;
                _redis = null;
            }
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Failed to connect to Redis - universe caching disabled");
            _connectionMultiplexer = null;
            _redis = null;
        }
        
        // Initialize default filter criteria
        _defaultCriteria = new UniverseFilterCriteria
        {
            MinPrice = 10.0m,
            MinAverageVolume = 1_000_000,
            MinVolatilityPercent = 2.0m,
            AnalysisPeriodDays = 20,
            MaxSymbols = 200 // Reasonable limit for processing
        };
        
        _logger.LogInformation("DynamicUniverseProvider initialized with {CandidateCount} default candidates, UsePolygonUniverse={UsePolygonUniverse}",
            DefaultCandidates.Length, _usePolygonUniverse);
    }

    public async Task<IEnumerable<string>> BuildUniverseAsync(IEnumerable<string>? candidateSymbols = null, CancellationToken cancellationToken = default)
    {
        var stopwatch = Stopwatch.StartNew();

        // Use Polygon-based universe if available and enabled
        if (_usePolygonUniverse && candidateSymbols == null)
        {
            _logger.LogInformation("Using Polygon-based universe for candidate selection");
            return await BuildPolygonBasedUniverseAsync(cancellationToken);
        }

        var candidates = await GetCandidateSymbolsAsync(candidateSymbols, cancellationToken);
        var metrics = new UniverseGenerationMetrics();
        var filterBreakdown = new Dictionary<string, int>
        {
            ["TotalCandidates"] = candidates.Count,
            ["PassedPriceFilter"] = 0,
            ["PassedVolumeFilter"] = 0,
            ["PassedVolatilityFilter"] = 0,
            ["FinalQualified"] = 0,
            ["Errors"] = 0
        };

        _logger.LogInformation("Building universe from {CandidateCount} candidate symbols", candidates.Count);

        var qualified = new List<string>();
        var apiCallCount = 0;

        // Process symbols in parallel batches for better performance
        const int batchSize = 20; // Increased batch size
        const int maxConcurrentBatches = 3; // Limit concurrent API calls

        var semaphore = new SemaphoreSlim(maxConcurrentBatches, maxConcurrentBatches);
        var batchTasks = new List<Task<(List<string> QualifiedSymbols, int ApiCalls)>>();

        for (int i = 0; i < candidates.Count; i += batchSize)
        {
            var batch = candidates.Skip(i).Take(batchSize);
            var batchTask = ProcessBatchWithSemaphore(batch, filterBreakdown, semaphore, cancellationToken);
            batchTasks.Add(batchTask);
        }

        // Wait for all batches to complete
        var batchResults = await Task.WhenAll(batchTasks);

        // Aggregate results
        foreach (var result in batchResults)
        {
            qualified.AddRange(result.QualifiedSymbols);
            apiCallCount += result.ApiCalls;
        }

        stopwatch.Stop();

        // Update metrics
        metrics.GenerationTime = stopwatch.Elapsed;
        metrics.ApiCallCount = apiCallCount;
        metrics.ErrorCount = filterBreakdown["Errors"];
        metrics.FilterBreakdown = filterBreakdown;
        metrics.AverageProcessingTime = TimeSpan.FromMilliseconds(stopwatch.Elapsed.TotalMilliseconds / candidates.Count);

        // Apply max symbols limit if specified
        if (_defaultCriteria.MaxSymbols.HasValue && qualified.Count > _defaultCriteria.MaxSymbols.Value)
        {
            qualified = qualified.Take(_defaultCriteria.MaxSymbols.Value).ToList();
            _logger.LogInformation("Limited universe to {MaxSymbols} symbols", _defaultCriteria.MaxSymbols.Value);
        }

        filterBreakdown["FinalQualified"] = qualified.Count;

        // Cache the result in Redis if available
        if (_redis != null)
        {
            try
            {
                var universeData = new RedisUniverse
                {
                    Symbols = qualified,
                    GeneratedAt = DateTime.UtcNow,
                    CandidateCount = candidates.Count,
                    QualifiedCount = qualified.Count,
                    FilterCriteria = _defaultCriteria,
                    Metrics = metrics,
                    Metadata = $"Generated from {candidates.Count} candidates in {stopwatch.Elapsed.TotalSeconds:F1}s"
                };

                await _redis.StringSetAsync(RedisUniverse.GetRedisKey(), universeData.ToJson(), TimeSpan.FromHours(24));
                _logger.LogDebug("Universe cached in Redis");
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Failed to cache universe in Redis");
            }
        }

        _logger.LogInformation("Universe built: {QualifiedCount}/{CandidateCount} symbols qualified in {ElapsedMs}ms",
            qualified.Count, candidates.Count, stopwatch.ElapsedMilliseconds);

        return qualified;
    }

    public async Task<RedisUniverse?> GetUniverseDetailsAsync(CancellationToken cancellationToken = default)
    {
        if (_redis == null)
        {
            return null;
        }

        try
        {
            cancellationToken.ThrowIfCancellationRequested();
            var universeJson = await _redis.StringGetAsync(RedisUniverse.GetRedisKey());
            if (!universeJson.HasValue)
            {
                return null;
            }

            return RedisUniverse.FromJson(universeJson!);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving universe details");
            return null;
        }
    }

    public async Task<IEnumerable<string>> RefreshUniverseAsync(IEnumerable<string>? candidateSymbols = null, CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("Forcing universe refresh");
        return await BuildUniverseAsync(candidateSymbols, cancellationToken);
    }

    public async Task<bool> IsCacheValidAsync(CancellationToken cancellationToken = default)
    {
        if (_redis == null)
        {
            return false;
        }

        try
        {
            cancellationToken.ThrowIfCancellationRequested();
            var universeJson = await _redis.StringGetAsync(RedisUniverse.GetRedisKey());
            if (!universeJson.HasValue)
            {
                return false;
            }

            var universeData = RedisUniverse.FromJson(universeJson!);
            if (universeData == null)
            {
                return false;
            }

            // Consider cache valid if generated within the last 24 hours
            var cacheAge = DateTime.UtcNow - universeData.GeneratedAt;
            return cacheAge < TimeSpan.FromHours(24);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error checking cache validity");
            return false;
        }
    }

    public IEnumerable<string> GetDefaultCandidates()
    {
        return DefaultCandidates;
    }

    /// <summary>
    /// Get candidate symbols from cache or fallback to default candidates
    /// </summary>
    private async Task<List<string>> GetCandidateSymbolsAsync(IEnumerable<string>? candidateSymbols, CancellationToken cancellationToken)
    {
        // If specific candidates provided, use them
        if (candidateSymbols != null)
        {
            return candidateSymbols.ToList();
        }

        // Try to get cached candidates from UniverseFetcherService
        if (_universeFetcherService != null)
        {
            try
            {
                var cachedSymbols = await _universeFetcherService.GetCachedSymbolsAsync(cancellationToken);
                if (cachedSymbols != null && cachedSymbols.Count > 0)
                {
                    _logger.LogInformation("Using {Count} cached symbols from UniverseFetcherService", cachedSymbols.Count);
                    return cachedSymbols;
                }
                else
                {
                    _logger.LogInformation("No cached symbols found, falling back to default candidates");
                }
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Error retrieving cached symbols, falling back to default candidates");
            }
        }

        // Fallback to default candidates
        _logger.LogInformation("Using {Count} default candidates", DefaultCandidates.Length);
        return DefaultCandidates.ToList();
    }

    private async Task<(List<string> QualifiedSymbols, int ApiCalls)> ProcessBatchWithSemaphore(
        IEnumerable<string> symbols,
        Dictionary<string, int> filterBreakdown,
        SemaphoreSlim semaphore,
        CancellationToken cancellationToken)
    {
        await semaphore.WaitAsync(cancellationToken);
        try
        {
            return await ProcessBatch(symbols, filterBreakdown, cancellationToken);
        }
        finally
        {
            semaphore.Release();
        }
    }

    private async Task<(List<string> QualifiedSymbols, int ApiCalls)> ProcessBatch(
        IEnumerable<string> symbols,
        Dictionary<string, int> filterBreakdown,
        CancellationToken cancellationToken)
    {
        var qualified = new List<string>();
        var apiCalls = 0;

        foreach (var symbol in symbols)
        {
            cancellationToken.ThrowIfCancellationRequested();

            try
            {
                // Get historical data for analysis
                var startDate = DateTime.UtcNow.AddDays(-_defaultCriteria.AnalysisPeriodDays);
                var endDate = DateTime.UtcNow;

                var response = await _marketDataService.GetStockBarsAsync(symbol, startDate, endDate);
                var bars = response.Items.ToList();
                apiCalls++;

                if (bars.Count < 10) // Need minimum data for analysis
                {
                    continue;
                }

                var currentPrice = bars.Last().Close;
                var volumes = bars.Select(b => (long)b.Volume).ToList();
                var closes = bars.Select(b => b.Close).ToList();

                // Apply filters
                if (!PassesPriceFilter(currentPrice))
                    continue;
                filterBreakdown["PassedPriceFilter"]++;

                if (!PassesVolumeFilter(volumes))
                    continue;
                filterBreakdown["PassedVolumeFilter"]++;

                if (!PassesVolatilityFilter(closes))
                    continue;
                filterBreakdown["PassedVolatilityFilter"]++;

                qualified.Add(symbol);
            }
            catch (Exception ex)
            {
                _logger.LogDebug("Error processing symbol {Symbol}: {Error}", symbol, ex.Message);
                filterBreakdown["Errors"]++;
            }
        }

        return (qualified, apiCalls);
    }

    private bool PassesPriceFilter(decimal price)
    {
        return price >= _defaultCriteria.MinPrice;
    }

    private bool PassesVolumeFilter(List<long> volumes)
    {
        if (volumes.Count == 0) return false;
        var averageVolume = volumes.Average();
        return averageVolume >= _defaultCriteria.MinAverageVolume;
    }

    private bool PassesVolatilityFilter(List<decimal> closes)
    {
        if (closes.Count < 2) return false;

        // Calculate daily returns
        var returns = new List<decimal>();
        for (int i = 1; i < closes.Count; i++)
        {
            var dailyReturn = (closes[i] - closes[i - 1]) / closes[i - 1];
            returns.Add(Math.Abs(dailyReturn));
        }

        if (returns.Count == 0) return false;

        // Calculate average absolute daily return as volatility proxy
        var averageVolatility = returns.Average();
        var volatilityPercent = averageVolatility * 100;

        return volatilityPercent >= _defaultCriteria.MinVolatilityPercent;
    }

    /// <summary>
    /// Build universe using Polygon-based symbol universe and daily candidates
    /// </summary>
    private async Task<IEnumerable<string>> BuildPolygonBasedUniverseAsync(CancellationToken cancellationToken)
    {
        try
        {
            // First, try to get pre-filtered candidates from daily universe service
            var candidates = await _dailyUniverseService!.GetCurrentCandidatesAsync(cancellationToken);
            if (candidates != null && candidates.Candidates.Any())
            {
                var candidateSymbols = candidates.Candidates.Select(c => c.Symbol).ToList();
                _logger.LogInformation("Using {CandidateCount} pre-filtered candidates from daily universe service", candidateSymbols.Count);
                return candidateSymbols;
            }

            _logger.LogInformation("No pre-filtered candidates available, falling back to Polygon symbol universe");

            // Fallback: Get symbols from Polygon universe and apply our own filtering
            var polygonSymbols = await _polygonSymbolService!.GetSymbolListAsync(cancellationToken);
            var symbolTickers = polygonSymbols.Select(s => s.Ticker).ToList();

            _logger.LogInformation("Retrieved {SymbolCount} symbols from Polygon universe, applying filters", symbolTickers.Count);

            // Apply our traditional filtering to the Polygon symbols
            return await BuildUniverseAsync(symbolTickers, cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error building Polygon-based universe, falling back to cached/default candidates");
            var fallbackCandidates = await GetCandidateSymbolsAsync(null, cancellationToken);
            return await BuildUniverseAsync(fallbackCandidates, cancellationToken);
        }
    }

    /// <summary>
    /// Get cached universe with Polygon integration
    /// </summary>
    public async Task<IEnumerable<string>> GetCachedUniverseAsync(CancellationToken cancellationToken = default)
    {
        // If using Polygon universe, try to get from daily universe service first
        if (_usePolygonUniverse)
        {
            try
            {
                var candidates = await _dailyUniverseService!.GetCurrentCandidatesAsync(cancellationToken);
                if (candidates != null && candidates.Candidates.Any())
                {
                    var candidateSymbols = candidates.Candidates.Select(c => c.Symbol).ToList();
                    _logger.LogDebug("Retrieved {CandidateCount} symbols from daily universe cache", candidateSymbols.Count);
                    return candidateSymbols;
                }
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Error retrieving from daily universe service, falling back to traditional cache");
            }
        }

        // Fallback to traditional Redis cache
        if (_redis == null)
        {
            _logger.LogDebug("Redis not available, building new universe");
            return await BuildUniverseAsync(null, cancellationToken);
        }

        try
        {
            cancellationToken.ThrowIfCancellationRequested();
            var universeJson = await _redis.StringGetAsync(RedisUniverse.GetRedisKey());
            if (!universeJson.HasValue)
            {
                _logger.LogInformation("No cached universe found, building new universe");
                return await BuildUniverseAsync(null, cancellationToken);
            }

            var universeData = RedisUniverse.FromJson(universeJson!);
            if (universeData == null)
            {
                _logger.LogWarning("Failed to deserialize cached universe, building new universe");
                return await BuildUniverseAsync(null, cancellationToken);
            }

            _logger.LogDebug("Retrieved cached universe: {SymbolCount} symbols (generated at {GeneratedAt})",
                universeData.Symbols.Count, universeData.GeneratedAt);

            // Update universe size metric
            MetricsRegistry.CurrentUniverseSize.Set(universeData.Symbols.Count);

            return universeData.Symbols;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving cached universe, building new universe");
            return await BuildUniverseAsync(null, cancellationToken);
        }
    }

    public void Dispose()
    {
        _connectionMultiplexer?.Dispose();
    }
}
