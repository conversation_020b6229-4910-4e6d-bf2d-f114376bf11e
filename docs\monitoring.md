# 📊 SmaTrendFollower Monitoring Setup

## Overview

This document provides comprehensive setup instructions for the Grafana & Prometheus monitoring pack for the SmaTrendFollower trading system. The monitoring stack includes real-time dashboards, alerting, and observability for all trading operations.

## 🚀 Quick Start

### 1. Start the Monitoring Stack

```bash
# Navigate to monitoring directory
cd monitoring

# Start all monitoring services
docker-compose up -d

# Verify services are running
docker-compose ps
```

### 2. Start the SmaTrendFollower Metrics API

```bash
# Start the trading bot with metrics endpoint
dotnet run --project SmaTrendFollower.Console -- metrics-api
```

The metrics endpoint will be available at `http://localhost:5000/metrics`

### 3. Access Dashboards

- **Grafana**: http://localhost:3000 (admin/admin)
- **Prometheus**: http://localhost:9090
- **AlertManager**: http://localhost:9093

## 📈 Available Dashboards

### Trading Overview Dashboard
- **Cumulative P&L**: Real-time portfolio value and daily P&L tracking
- **Universe Size**: Current number of symbols being monitored
- **Current Regime**: Market regime indicator (Sideways/TrendUp/TrendDown/Panic)
- **Trading Activity**: Trade and signal generation rates
- **Current Positions**: Number of open positions
- **Current VIX**: Real-time VIX level monitoring

### Execution Quality Dashboard
- **Average Slippage**: 15-minute rolling average slippage in basis points
- **Symbols Halted**: Count of symbols currently halted due to anomalies
- **Order Success Rate**: Percentage of successful order executions
- **Signal Generation Latency**: Performance metrics for signal generation
- **Error Rates**: Application errors, order failures, and WebSocket reconnections

## 🔧 Configuration

### Container Mount Paths

The monitoring stack uses the following volume mounts:

```yaml
services:
  prometheus:
    volumes:
      - ./prometheus/prometheus.yml:/etc/prometheus/prometheus.yml
      - ./prometheus/alerts.yml:/etc/prometheus/alerts.yml
      - prometheus_data:/prometheus

  grafana:
    volumes:
      - ./grafana/dashboards:/var/lib/grafana/dashboards
      - ./grafana/provisioning/dashboards:/etc/grafana/provisioning/dashboards
      - grafana_data:/var/lib/grafana

  alertmanager:
    volumes:
      - ./alertmanager/alertmanager.yml:/etc/alertmanager/alertmanager.yml
      - alertmanager_data:/alertmanager
```

### Prometheus Scrape Configuration

The system scrapes metrics from:
- **SmaTrendFollower**: `host.docker.internal:5000/metrics` (15s interval)
- **Node Exporter**: System metrics (30s interval)
- **Redis Exporter**: Redis performance metrics (30s interval)

### Dashboard Auto-Provisioning

Dashboards are automatically loaded from `/var/lib/grafana/dashboards` using the provisioning configuration in `monitoring/grafana/provisioning/dashboards/sma-dashboards.yml`.

## 🚨 Alerting Rules

### Critical Alerts
- **BotDown**: Trading bot is unreachable for >2 minutes
- **HighErrorRate**: Application error rate >0.1 errors/minute

### Warning Alerts
- **HighDrawdown**: Daily P&L below -$5,000
- **HighSlippage**: Average slippage >10 basis points
- **SignalGenerationLatency**: 95th percentile >30 seconds
- **OrderFailureRate**: Order failure rate >5%
- **WebSocketReconnects**: Reconnection rate >0.5/minute
- **UniverseSizeDropped**: Universe size <50 symbols
- **HighVIX**: VIX level >35
- **TooManyPositions**: Position count >10

## 📊 Key Metrics

### Trading Metrics
| Metric | Type | Description |
|--------|------|-------------|
| `trades_total` | Counter | Total trades executed (labeled by side, symbol) |
| `signals_total` | Counter | Trading signals generated |
| `signal_latency_ms` | Histogram | Signal generation latency |
| `portfolio_value_usd` | Gauge | Current portfolio value |
| `daily_pnl_usd` | Gauge | Daily profit and loss |
| `current_positions` | Gauge | Number of open positions |

### System Metrics
| Metric | Type | Description |
|--------|------|-------------|
| `websocket_reconnect_total` | Counter | WebSocket reconnections |
| `universe_size` | Gauge | Trading universe size |
| `application_errors_total` | Counter | Application errors |
| `order_failures_total` | Counter | Order execution failures |

### Market Metrics
| Metric | Type | Description |
|--------|------|-------------|
| `current_vix` | Gauge | Current VIX value |
| `market_regime` | Gauge | Market regime indicator |
| `slippage_actual_bps` | Histogram | Execution slippage in basis points |

## 🔍 Useful Prometheus Queries

```promql
# Trading volume by symbol (last 5 minutes)
sum(rate(trades_total[5m])) by (symbol)

# Signal generation rate
rate(signals_total[5m])

# 95th percentile signal generation latency
histogram_quantile(0.95, rate(signal_latency_ms_bucket[5m]))

# WebSocket reconnection rate
rate(websocket_reconnect_total[5m])

# Current portfolio metrics
portfolio_value_usd
daily_pnl_usd

# Error rates
rate(application_errors_total[5m])
rate(order_failures_total[5m])

# Average slippage in basis points
rate(slippage_actual_bps_sum[15m]) / rate(slippage_actual_bps_count[15m]) * 10000
```

## 🛠️ Troubleshooting

### Common Issues

1. **Metrics not appearing in Prometheus**
   - Verify SmaTrendFollower is running with `dotnet run metrics-api`
   - Check Prometheus targets at http://localhost:9090/targets
   - Ensure firewall allows port 5000

2. **Dashboards not loading**
   - Check Grafana logs: `docker-compose logs grafana`
   - Verify dashboard files are mounted correctly
   - Restart Grafana: `docker-compose restart grafana`

3. **Alerts not firing**
   - Check AlertManager configuration: http://localhost:9093
   - Verify alert rules in Prometheus: http://localhost:9090/alerts
   - Check AlertManager logs: `docker-compose logs alertmanager`

### Log Locations

```bash
# View all monitoring service logs
docker-compose logs

# View specific service logs
docker-compose logs prometheus
docker-compose logs grafana
docker-compose logs alertmanager
```

## 🔄 Maintenance

### Updating Dashboards
1. Modify JSON files in `monitoring/grafana/dashboards/`
2. Restart Grafana: `docker-compose restart grafana`
3. Dashboards will auto-reload within 30 seconds

### Updating Alert Rules
1. Modify `monitoring/prometheus/alerts.yml`
2. Reload Prometheus configuration:
   ```bash
   curl -X POST http://localhost:9090/-/reload
   ```

### Data Retention
- **Prometheus**: 30 days (configurable in docker-compose.yml)
- **Grafana**: Persistent storage in Docker volumes
- **AlertManager**: Persistent storage in Docker volumes

## 🔐 Security Considerations

- Default Grafana credentials: admin/admin (change immediately)
- Monitoring stack runs on localhost only
- No external authentication configured by default
- Consider adding reverse proxy with SSL for production

## 📚 Additional Resources

- [Prometheus Documentation](https://prometheus.io/docs/)
- [Grafana Documentation](https://grafana.com/docs/)
- [AlertManager Documentation](https://prometheus.io/docs/alerting/latest/alertmanager/)
- [SmaTrendFollower Prometheus Guide](../SmaTrendFollower.Console/Documentation/PrometheusObservabilityGuide.md)
