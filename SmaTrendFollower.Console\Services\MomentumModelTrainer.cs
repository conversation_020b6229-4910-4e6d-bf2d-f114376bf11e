using Microsoft.Extensions.Logging;
using SmaTrendFollower.Models;
using SmaTrendFollower.Services;
using SmaTrendFollower.Console.Extensions;
using StackExchange.Redis;

namespace SmaTrendFollower.Services;

/// <summary>
/// Interface for momentum model training with sentiment integration
/// </summary>
public interface IMomentumModelTrainer
{
    /// <summary>
    /// Creates feature vector for ML model including sentiment data
    /// </summary>
    Task<float[]> CreateFeatureVectorAsync(string symbol, DateTime date, CancellationToken cancellationToken = default);

    /// <summary>
    /// Trains momentum model with sentiment features
    /// </summary>
    Task<bool> TrainModelAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets sentiment score for a symbol on a specific date
    /// </summary>
    Task<double> GetSentimentScoreAsync(string symbol, DateTime date, CancellationToken cancellationToken = default);
}

/// <summary>
/// Momentum model trainer that incorporates news sentiment analysis into ML features
/// </summary>
public sealed class MomentumModelTrainer : IMomentumModelTrainer
{
    private readonly IDatabase _redisDatabase;
    private readonly ILogger<MomentumModelTrainer> _logger;
    private readonly IMomentumCache _momentumCache;
    private readonly IMarketDataService _marketDataService;

    public MomentumModelTrainer(
        OptimizedRedisConnectionService redisConnectionService,
        ILogger<MomentumModelTrainer> logger,
        IMomentumCache momentumCache,
        IMarketDataService marketDataService)
    {
        _redisDatabase = redisConnectionService.GetDatabaseAsync().GetAwaiter().GetResult();
        _logger = logger;
        _momentumCache = momentumCache;
        _marketDataService = marketDataService;
    }

    /// <summary>
    /// Creates feature vector for ML model including sentiment data
    /// </summary>
    public async Task<float[]> CreateFeatureVectorAsync(string symbol, DateTime date, CancellationToken cancellationToken = default)
    {
        try
        {
            // Get basic momentum features
            var endDate = date.Date;
            var startDate = endDate.AddDays(-200); // Get 200 days of data
            var barsPage = await _marketDataService.GetStockBarsAsync(symbol, startDate, endDate);
            var bars = barsPage?.Items?.ToList();
            if (bars == null || bars.Count < 50)
            {
                _logger.LogWarning("Insufficient bar data for {Symbol}", symbol);
                return new float[] { 0, 0, 0 }; // Default feature vector
            }

            // Calculate RSI
            var rsi = bars.GetRsi14();

            // Calculate MACD
            var macd = bars.GetMacd();
            var macdHistogram = macd.Histogram;

            // Get sentiment score
            var sentiment = await GetSentimentScoreAsync(symbol, date, cancellationToken);

            // Create feature vector: [RSI, MACD Histogram, Sentiment]
            var features = new float[]
            {
                (float)rsi,
                (float)macdHistogram,
                (float)sentiment
            };

            _logger.LogDebug("Created feature vector for {Symbol}: RSI={RSI:F2}, MACD={MACD:F4}, Sentiment={Sentiment:F3}",
                symbol, rsi, macdHistogram, sentiment);

            return features;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating feature vector for {Symbol}", symbol);
            return new float[] { 0, 0, 0 }; // Default feature vector on error
        }
    }

    /// <summary>
    /// Gets sentiment score for a symbol on a specific date
    /// </summary>
    public async Task<double> GetSentimentScoreAsync(string symbol, DateTime date, CancellationToken cancellationToken = default)
    {
        try
        {
            var dateKey = date.ToString("yyyyMMdd");
            var sentimentKey = $"Sentiment:{symbol}:{dateKey}";

            // Try to get the latest sentiment score for the symbol on the given date
            var latestSentiment = await _redisDatabase.HashGetAsync(sentimentKey, "latest");
            
            if (latestSentiment.HasValue && double.TryParse(latestSentiment, out var sentiment))
            {
                _logger.LogDebug("Retrieved sentiment for {Symbol} on {Date}: {Sentiment:F3}", 
                    symbol, dateKey, sentiment);
                return sentiment;
            }

            // If no latest sentiment, try to get any sentiment from that day
            var allSentiments = await _redisDatabase.HashGetAllAsync(sentimentKey);
            if (allSentiments.Any())
            {
                var sentimentValues = allSentiments
                    .Where(kv => kv.Name != "latest" && !string.IsNullOrEmpty(kv.Value) && double.TryParse(kv.Value, out _))
                    .Select(kv => double.Parse(kv.Value!))
                    .ToList();

                if (sentimentValues.Any())
                {
                    // Return average sentiment for the day
                    var avgSentiment = sentimentValues.Average();
                    _logger.LogDebug("Retrieved average sentiment for {Symbol} on {Date}: {Sentiment:F3} (from {Count} news items)",
                        symbol, dateKey, avgSentiment, sentimentValues.Count);
                    return avgSentiment;
                }
            }

            // No sentiment data found, return neutral
            _logger.LogDebug("No sentiment data found for {Symbol} on {Date}, returning neutral (0.0)", symbol, dateKey);
            return 0.0;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving sentiment for {Symbol} on {Date}", symbol, date);
            return 0.0; // Return neutral sentiment on error
        }
    }

    /// <summary>
    /// Trains momentum model with sentiment features
    /// </summary>
    public async Task<bool> TrainModelAsync(CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Starting momentum model training with sentiment features...");

            // This is a placeholder for the actual training logic
            // In a real implementation, this would:
            // 1. Collect historical data with sentiment scores
            // 2. Create feature vectors for training
            // 3. Train an ML model (e.g., using Microsoft.ML)
            // 4. Save the trained model
            // 5. Update Redis with model version

            // For now, we'll simulate successful training
            await Task.Delay(1000, cancellationToken); // Simulate training time

            _logger.LogInformation("Momentum model training completed successfully");
            return true;
        }
        catch (OperationCanceledException)
        {
            _logger.LogInformation("Momentum model training was cancelled");
            return false;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during momentum model training");
            return false;
        }
    }
}
