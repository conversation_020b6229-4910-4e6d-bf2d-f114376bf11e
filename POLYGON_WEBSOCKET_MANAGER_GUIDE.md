# Polygon WebSocket Manager Guide

## Overview

The `PolygonWebSocketManager` is a production-ready WebSocket management system designed to handle large-scale real-time market data subscriptions from Polygon.io. It solves the challenge of subscribing to 200+ symbols without hitting rate limits while providing robust error handling and automatic recovery.

## Key Features

### 🚀 Batch Subscriptions
- Automatically chunks symbol lists into ≤500-symbol batches
- Respects Polygon rate limits and subscription quotas
- Handles 1000+ symbols efficiently across multiple chunks

### 🔄 Auto-Reconnection
- Exponential backoff retry logic (1s → 32s max delay)
- Automatic re-subscription to persisted symbol lists
- Circuit breaker protection for rate limit errors

### 📊 Redis Persistence
- Subscription lists stored in Redis with 24h TTL
- Auto re-subscription on reconnect using cached data
- Standardized key patterns: `ws:subs:{channel}`

### 📈 Monitoring & Metrics
- Prometheus metrics for connection health
- Subscription count tracking per channel
- Reconnection event monitoring

## Architecture

```
┌─────────────────────────────────────────────────────────────┐
│                PolygonWebSocketManager                      │
├─────────────────────────────────────────────────────────────┤
│  ┌─────────────────┐  ┌─────────────────┐                  │
│  │ Equity Socket   │  │ Index Socket    │                  │
│  │ (stocks)        │  │ (indices)       │                  │
│  └─────────────────┘  └─────────────────┘                  │
├─────────────────────────────────────────────────────────────┤
│              Redis Subscription Persistence                │
├─────────────────────────────────────────────────────────────┤
│  ws:subs:EquityTrades   │  ws:subs:IndexMinute             │
│  ws:subs:EquityQuotes   │  ws:subs:EquityMinute            │
└─────────────────────────────────────────────────────────────┘
```

## Channel Types

| Channel | Prefix | Description | WebSocket URL |
|---------|--------|-------------|---------------|
| `EquityTrades` | `T.` | Real-time trade execution data | `wss://socket.polygon.io/stocks` |
| `EquityQuotes` | `Q.` | Real-time bid/ask quote data | `wss://socket.polygon.io/stocks` |
| `EquityMinute` | `AM.` | 1-minute OHLCV bars for stocks | `wss://socket.polygon.io/stocks` |
| `IndexMinute` | `AM.I:` | 1-minute OHLCV bars for indices | `wss://socket.polygon.io/indices` |

## Usage Examples

### Basic Setup

```csharp
// Service registration in DI container
services.AddSingleton<PolygonWebSocketManager>(provider =>
{
    var redisService = provider.GetRequiredService<IOptimizedRedisConnectionService>();
    var logger = provider.GetRequiredService<ILogger<PolygonWebSocketManager>>();
    var configuration = provider.GetRequiredService<IConfiguration>();
    var metricsService = provider.GetService<ITradingMetricsService>();
    var apiKey = configuration["POLY_API_KEY"];
    
    return new PolygonWebSocketManager(redisService, logger, apiKey, metricsService);
});
```

### Universe Subscription

```csharp
public class TradingService
{
    private readonly PolygonWebSocketManager _wsManager;
    
    public async Task StartTradingAsync()
    {
        // Get universe from Redis
        var universe = await GetUniverseSymbolsAsync(); // Returns 200+ symbols
        
        // Subscribe to minute bars for all universe symbols
        await _wsManager.BatchSubscribeAsync(PolygonWsChannel.EquityMinute, universe);
        
        // Subscribe to trades for execution monitoring
        await _wsManager.BatchSubscribeAsync(PolygonWsChannel.EquityTrades, universe);
        
        // Subscribe to index data for market regime analysis
        var indices = new[] { "VIX", "SPX", "NDX", "RUT" };
        await _wsManager.BatchSubscribeAsync(PolygonWsChannel.IndexMinute, indices);
    }
}
```

### Connection Monitoring

```csharp
public async Task MonitorConnectionsAsync()
{
    foreach (var channel in Enum.GetValues<PolygonWsChannel>())
    {
        var state = _wsManager.GetConnectionState(channel);
        var count = _wsManager.GetSubscribedSymbolCount(channel);
        
        _logger.LogInformation("Channel {Channel}: {State}, {Count} symbols", 
            channel, state, count);
    }
}
```

## Configuration

### Environment Variables

```bash
# Required
POLY_API_KEY=your_polygon_api_key

# Redis Configuration
REDIS_URL=*************:6379
REDIS_DATABASE=0

# Optional WebSocket Settings
WEBSOCKET_MAX_RECONNECT_ATTEMPTS=10
WEBSOCKET_BASE_RECONNECT_DELAY_MS=1000
WEBSOCKET_MAX_RECONNECT_DELAY_MS=32000
WEBSOCKET_CIRCUIT_BREAKER_THRESHOLD=3
WEBSOCKET_RATE_LIMIT_RETRY_DELAY_MS=2000
```

### Redis Key Patterns

| Key Pattern | TTL | Description |
|-------------|-----|-------------|
| `ws:subs:EquityTrades` | 24h | Equity trade subscriptions |
| `ws:subs:EquityQuotes` | 24h | Equity quote subscriptions |
| `ws:subs:EquityMinute` | 24h | Equity minute bar subscriptions |
| `ws:subs:IndexMinute` | 24h | Index minute bar subscriptions |

## Error Handling

### Rate Limiting (429 Errors)

```
1. Log rate limit error with symbol details
2. Wait 2 seconds before retry
3. Increment consecutive error counter
4. Open circuit breaker after 3 consecutive errors
5. Stop accepting new subscriptions until reset
```

### Connection Failures

```
1. Log connection failure
2. Calculate exponential backoff delay: min(1000 * 2^attempt, 32000)ms
3. Wait for calculated delay
4. Attempt reconnection
5. On success: reload subscriptions from Redis
6. On failure: retry up to 10 attempts
```

### Circuit Breaker States

- **Closed**: Normal operation, accepting subscriptions
- **Open**: Rate limit protection active, rejecting subscriptions
- **Half-Open**: Testing if rate limits have cleared

## Monitoring

### Prometheus Metrics

```prometheus
# WebSocket reconnection events
websocket_reconnect_total{channel="EquityMinute"} 5

# Current subscription counts
websocket_subscribed_symbols{channel="EquityMinute"} 250
websocket_subscribed_symbols{channel="IndexMinute"} 4
```

### Log Messages

```
[Info] Batch subscribing 250 symbols in 1 chunk(s) for channel EquityMinute
[Info] Successfully subscribed to universe symbols via WebSocket manager
[Warn] Rate limit error #1 for channel EquityMinute: Too Many Subscriptions
[Error] Circuit breaker opened for channel EquityMinute after 3 consecutive rate limit errors
[Info] Reconnection successful for channel EquityMinute
[Info] Auto re-subscribing to 250 persisted symbols for channel EquityMinute
```

## Troubleshooting

### Common Issues

#### 1. Subscription Failures
**Symptoms**: Symbols not receiving data, 429 errors
**Solutions**:
- Check Polygon subscription limits
- Verify API key permissions
- Monitor circuit breaker status
- Reduce symbol count if necessary

#### 2. Connection Drops
**Symptoms**: Frequent reconnections, data gaps
**Solutions**:
- Check network stability
- Verify Redis connectivity
- Monitor system resources
- Review Polygon service status

#### 3. Redis Persistence Issues
**Symptoms**: Subscriptions not restored on reconnect
**Solutions**:
- Verify Redis connection
- Check key TTL settings
- Ensure Redis hygiene service is running
- Monitor Redis memory usage

### Performance Optimization

1. **Symbol Batching**: Keep batches ≤500 symbols
2. **Connection Pooling**: Use separate sockets for equity vs index data
3. **Redis Optimization**: Use connection pooling and compression
4. **Memory Management**: Monitor WebSocket buffer sizes
5. **Metrics Collection**: Use sampling for high-frequency metrics

## Testing

### Unit Tests
```bash
dotnet test SmaTrendFollower.Tests/Services/PolygonWebSocketManagerTests.cs
```

### Integration Tests
```bash
# Requires Redis connection
dotnet test SmaTrendFollower.Tests/Integration/PolygonWebSocketManagerIntegrationTests.cs
```

### Load Testing
```bash
# Test with 1000+ symbols
dotnet run --project LoadTest.PolygonWebSocket
```

## Best Practices

1. **Gradual Rollout**: Start with small symbol sets, gradually increase
2. **Monitor Metrics**: Watch reconnection rates and subscription counts
3. **Redis Hygiene**: Ensure TTL cleanup is working properly
4. **Error Alerting**: Set up alerts for circuit breaker events
5. **Capacity Planning**: Monitor Polygon subscription quotas
6. **Graceful Shutdown**: Always call `DisconnectAllAsync()` on shutdown
