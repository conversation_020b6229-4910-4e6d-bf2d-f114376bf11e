{"id": null, "title": "Execution Quality", "tags": ["sma-trend-follower", "execution", "quality"], "timezone": "browser", "refresh": "30s", "time": {"from": "now-3h", "to": "now"}, "panels": [{"id": 1, "type": "timeseries", "title": "Average Slippage (bps, 15m)", "targets": [{"expr": "rate(slippage_actual_bps_sum[15m]) / rate(slippage_actual_bps_count[15m]) * 10000", "legendFormat": "Avg <PERSON> (bps)", "refId": "A"}, {"expr": "histogram_quantile(0.95, rate(slippage_actual_bps_bucket[15m])) * 10000", "legendFormat": "95th Percentile (bps)", "refId": "B"}], "datasource": {"type": "prometheus", "uid": "prometheus"}, "gridPos": {"x": 0, "y": 0, "w": 12, "h": 8}, "fieldConfig": {"defaults": {"unit": "short", "decimals": 2, "custom": {"drawStyle": "line", "lineInterpolation": "linear", "lineWidth": 2, "fillOpacity": 10, "gradientMode": "none", "spanNulls": false, "insertNulls": false, "showPoints": "never", "pointSize": 5, "stacking": {"mode": "none", "group": "A"}, "axisPlacement": "auto", "axisLabel": "Basis Points", "axisColorMode": "text", "scaleDistribution": {"type": "linear"}, "axisCenteredZero": false, "hideFrom": {"legend": false, "tooltip": false, "vis": false}, "thresholdsStyle": {"mode": "line"}}, "color": {"mode": "palette-classic"}, "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "yellow", "value": 5}, {"color": "red", "value": 10}]}}}, "options": {"tooltip": {"mode": "multi", "sort": "none"}, "legend": {"displayMode": "visible", "placement": "bottom", "calcs": ["lastNotNull", "mean"]}}}, {"id": 2, "type": "stat", "title": "Symbols Halted Now", "targets": [{"expr": "count(anomaly_halted == 1)", "legendFormat": "Halted Symbols", "refId": "A"}], "datasource": {"type": "prometheus", "uid": "prometheus"}, "gridPos": {"x": 12, "y": 0, "w": 6, "h": 4}, "fieldConfig": {"defaults": {"unit": "short", "color": {"mode": "thresholds"}, "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "yellow", "value": 1}, {"color": "red", "value": 5}]}}}, "options": {"reduceOptions": {"values": false, "calcs": ["lastNotNull"], "fields": ""}, "orientation": "auto", "textMode": "auto", "colorMode": "background", "graphMode": "area", "justifyMode": "auto"}}, {"id": 3, "type": "stat", "title": "Order Success Rate", "targets": [{"expr": "rate(trades_total[5m]) / (rate(trades_total[5m]) + rate(order_failures_total[5m])) * 100", "legendFormat": "Success %", "refId": "A"}], "datasource": {"type": "prometheus", "uid": "prometheus"}, "gridPos": {"x": 12, "y": 4, "w": 6, "h": 4}, "fieldConfig": {"defaults": {"unit": "percent", "decimals": 1, "color": {"mode": "thresholds"}, "thresholds": {"mode": "absolute", "steps": [{"color": "red", "value": null}, {"color": "yellow", "value": 95}, {"color": "green", "value": 99}]}}}, "options": {"reduceOptions": {"values": false, "calcs": ["lastNotNull"], "fields": ""}, "orientation": "auto", "textMode": "auto", "colorMode": "background", "graphMode": "area", "justifyMode": "auto"}}, {"id": 4, "type": "timeseries", "title": "Signal Generation Latency", "targets": [{"expr": "histogram_quantile(0.50, rate(signal_latency_ms_bucket[5m]))", "legendFormat": "50th Percentile", "refId": "A"}, {"expr": "histogram_quantile(0.95, rate(signal_latency_ms_bucket[5m]))", "legendFormat": "95th Percentile", "refId": "B"}, {"expr": "histogram_quantile(0.99, rate(signal_latency_ms_bucket[5m]))", "legendFormat": "99th Percentile", "refId": "C"}], "datasource": {"type": "prometheus", "uid": "prometheus"}, "gridPos": {"x": 0, "y": 8, "w": 12, "h": 6}, "fieldConfig": {"defaults": {"unit": "ms", "custom": {"drawStyle": "line", "lineInterpolation": "linear", "lineWidth": 1, "fillOpacity": 10, "gradientMode": "none", "spanNulls": false, "insertNulls": false, "showPoints": "never", "pointSize": 5, "stacking": {"mode": "none", "group": "A"}, "axisPlacement": "auto", "axisLabel": "Milliseconds", "axisColorMode": "text", "scaleDistribution": {"type": "linear"}, "axisCenteredZero": false, "hideFrom": {"legend": false, "tooltip": false, "vis": false}, "thresholdsStyle": {"mode": "line"}}, "color": {"mode": "palette-classic"}, "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "yellow", "value": 5000}, {"color": "red", "value": 15000}]}}}, "options": {"tooltip": {"mode": "multi", "sort": "none"}, "legend": {"displayMode": "visible", "placement": "bottom"}}}, {"id": 5, "type": "timeseries", "title": "Error Rates", "targets": [{"expr": "rate(application_errors_total[5m])", "legendFormat": "Application Errors/min", "refId": "A"}, {"expr": "rate(order_failures_total[5m])", "legendFormat": "Order Failures/min", "refId": "B"}, {"expr": "rate(websocket_reconnect_total[5m])", "legendFormat": "WebSocket Reconnects/min", "refId": "C"}], "datasource": {"type": "prometheus", "uid": "prometheus"}, "gridPos": {"x": 12, "y": 8, "w": 6, "h": 6}, "fieldConfig": {"defaults": {"unit": "reqps", "custom": {"drawStyle": "line", "lineInterpolation": "linear", "lineWidth": 1, "fillOpacity": 10, "gradientMode": "none", "spanNulls": false, "insertNulls": false, "showPoints": "never", "pointSize": 5, "stacking": {"mode": "none", "group": "A"}, "axisPlacement": "auto", "axisLabel": "", "axisColorMode": "text", "scaleDistribution": {"type": "linear"}, "axisCenteredZero": false, "hideFrom": {"legend": false, "tooltip": false, "vis": false}, "thresholdsStyle": {"mode": "off"}}, "color": {"mode": "palette-classic"}}}, "options": {"tooltip": {"mode": "multi", "sort": "none"}, "legend": {"displayMode": "visible", "placement": "bottom"}}}], "schemaVersion": 37, "version": 1, "uid": "sma-execution-quality", "editable": true, "fiscalYearStartMonth": 0, "graphTooltip": 0, "links": [], "liveNow": false, "style": "dark", "templating": {"list": []}, "weekStart": ""}