using Microsoft.Extensions.Logging;
using Quartz;
using StackExchange.Redis;

namespace SmaTrendFollower.Services;

/// <summary>
/// Monitors intraday P&L drawdown and sends Discord alerts when threshold is exceeded
/// Runs every 5 minutes during trading hours to track portfolio performance
/// </summary>
[DisallowConcurrentExecution]
public sealed class DrawdownMonitor : IJob
{
    private readonly IDatabase _redis;
    private readonly ILogger<DrawdownMonitor> _logger;
    private readonly decimal _drawdownThreshold;
    private readonly string _peakPnlKey = "pnl:peak:today";
    private readonly string _currentPnlKey = "pnl:today";
    private readonly string _lastAlertKey = "drawdown:last_alert";

    public DrawdownMonitor(
        ConnectionMultiplexer redisConnection,
        ILogger<DrawdownMonitor> logger)
    {
        _redis = redisConnection.GetDatabase();
        _logger = logger;
        
        // 5% drawdown threshold (configurable via environment)
        var thresholdStr = Environment.GetEnvironmentVariable("DRAWDOWN_ALERT_THRESHOLD");
        _drawdownThreshold = decimal.TryParse(thresholdStr, out var threshold) ? threshold : 0.05m;
    }

    public async Task Execute(IJobExecutionContext context)
    {
        try
        {
            // Get current P&L from Redis
            var currentPnlStr = await _redis.StringGetAsync(_currentPnlKey);
            if (!currentPnlStr.HasValue)
            {
                _logger.LogDebug("No current P&L data available for drawdown monitoring");
                return;
            }

            if (!decimal.TryParse(currentPnlStr, out var currentPnl))
            {
                _logger.LogWarning("Invalid P&L format in Redis: {PnL}", currentPnlStr);
                return;
            }

            // Get or initialize peak P&L
            var peakPnlStr = await _redis.StringGetAsync(_peakPnlKey);
            var peakPnl = decimal.TryParse(peakPnlStr, out var peak) ? peak : 0m;

            // Update peak if current P&L is higher
            if (currentPnl > peakPnl)
            {
                peakPnl = currentPnl;
                await _redis.StringSetAsync(_peakPnlKey, peakPnl.ToString("F2"), TimeSpan.FromHours(24));
                _logger.LogDebug("Updated peak P&L to {PeakPnL:C}", peakPnl);
            }

            // Calculate drawdown percentage
            var drawdown = peakPnl == 0 ? 0 : (currentPnl - peakPnl) / Math.Abs(peakPnl);
            
            // Check if drawdown exceeds threshold
            if (drawdown < -_drawdownThreshold)
            {
                await HandleDrawdownAlert(currentPnl, peakPnl, drawdown);
            }
            else
            {
                _logger.LogDebug("Drawdown monitoring: Current P&L {CurrentPnL:C}, Peak {PeakPnL:C}, Drawdown {Drawdown:P1}", 
                    currentPnl, peakPnl, drawdown);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during drawdown monitoring");
        }
    }

    private async Task HandleDrawdownAlert(decimal currentPnl, decimal peakPnl, decimal drawdown)
    {
        try
        {
            // Check if we've already alerted recently (prevent spam)
            var lastAlertStr = await _redis.StringGetAsync(_lastAlertKey);
            if (DateTime.TryParse(lastAlertStr, out var lastAlert))
            {
                var timeSinceLastAlert = DateTime.UtcNow - lastAlert;
                if (timeSinceLastAlert < TimeSpan.FromMinutes(15)) // Minimum 15 minutes between alerts
                {
                    _logger.LogDebug("Drawdown alert suppressed - last alert was {Minutes} minutes ago", 
                        timeSinceLastAlert.TotalMinutes);
                    return;
                }
            }

            // Log the warning (this will trigger Discord alert via DiscordSink)
            _logger.LogWarning(
                "DRAWDOWN ALERT: {Threshold:P0} threshold exceeded. Current P&L: {CurrentPnL:C}, Peak P&L: {PeakPnL:C}, Drawdown: {Drawdown:P1}",
                _drawdownThreshold, currentPnl, peakPnl, drawdown);

            // Record the alert timestamp
            await _redis.StringSetAsync(_lastAlertKey, DateTime.UtcNow.ToString("O"), TimeSpan.FromHours(24));

            _logger.LogInformation("Drawdown alert sent for {Drawdown:P1} drawdown", drawdown);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error sending drawdown alert");
        }
    }

    /// <summary>
    /// Resets the peak P&L tracking (typically called at market open)
    /// </summary>
    public async Task ResetDailyPeakAsync()
    {
        try
        {
            await _redis.KeyDeleteAsync(_peakPnlKey);
            await _redis.KeyDeleteAsync(_lastAlertKey);
            _logger.LogInformation("Reset daily peak P&L tracking for new trading session");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error resetting daily peak P&L");
        }
    }

    /// <summary>
    /// Updates the current P&L value in Redis (called by trading services)
    /// </summary>
    public async Task UpdateCurrentPnLAsync(decimal pnl)
    {
        try
        {
            await _redis.StringSetAsync(_currentPnlKey, pnl.ToString("F2"), TimeSpan.FromHours(24));
            _logger.LogDebug("Updated current P&L to {PnL:C}", pnl);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating current P&L");
        }
    }

    /// <summary>
    /// Gets current drawdown statistics for monitoring dashboard
    /// </summary>
    public async Task<DrawdownStats?> GetDrawdownStatsAsync()
    {
        try
        {
            var currentPnlStr = await _redis.StringGetAsync(_currentPnlKey);
            var peakPnlStr = await _redis.StringGetAsync(_peakPnlKey);

            if (!currentPnlStr.HasValue || !decimal.TryParse(currentPnlStr, out var currentPnl))
                return null;

            var peakPnl = decimal.TryParse(peakPnlStr, out var peak) ? peak : 0m;
            var drawdown = peakPnl == 0 ? 0 : (currentPnl - peakPnl) / Math.Abs(peakPnl);

            return new DrawdownStats
            {
                CurrentPnL = currentPnl,
                PeakPnL = peakPnl,
                DrawdownPercent = drawdown,
                ThresholdPercent = _drawdownThreshold,
                IsAlertTriggered = drawdown < -_drawdownThreshold
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting drawdown stats");
            return null;
        }
    }
}

/// <summary>
/// Drawdown monitoring statistics
/// </summary>
public record DrawdownStats
{
    public decimal CurrentPnL { get; init; }
    public decimal PeakPnL { get; init; }
    public decimal DrawdownPercent { get; init; }
    public decimal ThresholdPercent { get; init; }
    public bool IsAlertTriggered { get; init; }
}
