using Microsoft.Extensions.Configuration;
using SmaTrendFollower.Services;

namespace SmaTrendFollower.Configuration;

/// <summary>
/// Configuration extensions for wheel strategy
/// </summary>
public static class WheelStrategyConfiguration
{
    /// <summary>
    /// Creates WheelStrategyConfig from IConfiguration
    /// </summary>
    public static WheelStrategyConfig CreateWheelStrategyConfig(IConfiguration configuration)
    {
        var section = configuration.GetSection("WheelStrategy");

        return new WheelStrategyConfig
        {
            Enabled = section.GetValue<bool>("Enabled", true),
            MaxAllocationPercent = section.GetValue<decimal>("MaxAllocationPercent", 0.20m),
            MinPremiumPercent = section.GetValue<decimal>("MinPremiumPercent", 0.01m),
            MinDaysToExpiration = section.GetValue<int>("MinDaysToExpiration", 7),
            MaxDaysToExpiration = section.GetValue<int>("MaxDaysToExpiration", 45),
            MaxDeltaForPuts = section.GetValue<decimal>("MaxDeltaForPuts", 0.30m),
            MaxDeltaForCalls = section.GetValue<decimal>("MaxDeltaForCalls", 0.30m),
            MinLiquidity = section.GetValue<decimal>("MinLiquidity", 100m),
            MaxBidAskSpreadPercent = section.GetValue<decimal>("MaxBidAskSpreadPercent", 0.05m),
            EnableRolling = section.GetValue<bool>("EnableRolling", true),
            RollThreshold = section.GetValue<decimal>("RollThreshold", 0.50m),
            MaxRollAttempts = section.GetValue<int>("MaxRollAttempts", 2),
            RequireHighIV = section.GetValue<bool>("RequireHighIV", true),
            MinIVPercentile = section.GetValue<decimal>("MinIVPercentile", 30m),
            AllowedSymbols = GetStringList(section, "AllowedSymbols"),
            ExcludedSymbols = GetStringList(section, "ExcludedSymbols")
        };
    }

    private static IReadOnlyList<string>? GetStringList(IConfigurationSection section, string key)
    {
        var values = section.GetSection(key).Get<string[]>();
        return values?.Length > 0 ? values : null;
    }
}

/// <summary>
/// Wheel strategy timing configuration
/// </summary>
public record WheelStrategyTimingConfig(
    TimeOnly EntryWindowStart = default,
    TimeOnly EntryWindowEnd = default,
    TimeSpan CycleInterval = default,
    bool EnableExtendedHours = false,
    bool EnablePreMarketEntry = false
)
{
    public WheelStrategyTimingConfig() : this(
        new TimeOnly(9, 35), // 9:35 AM ET
        new TimeOnly(15, 30), // 3:30 PM ET  
        TimeSpan.FromMinutes(15),
        false,
        false
    ) { }

    public static WheelStrategyTimingConfig FromConfiguration(IConfiguration configuration)
    {
        var section = configuration.GetSection("WheelStrategy:Timing");
        
        return new WheelStrategyTimingConfig(
            TimeOnly.TryParse(section["EntryWindowStart"], out var start) ? start : new TimeOnly(9, 35),
            TimeOnly.TryParse(section["EntryWindowEnd"], out var end) ? end : new TimeOnly(15, 30),
            TimeSpan.TryParse(section["CycleInterval"], out var interval) ? interval : TimeSpan.FromMinutes(15),
            section.GetValue<bool>("EnableExtendedHours", false),
            section.GetValue<bool>("EnablePreMarketEntry", false)
        );
    }

    /// <summary>
    /// Checks if current time is within entry window
    /// </summary>
    public bool IsEntryWindow
    {
        get
        {
            var now = TimeOnly.FromDateTime(DateTime.Now);
            return now >= EntryWindowStart && now <= EntryWindowEnd;
        }
    }
}

/// <summary>
/// Wheel strategy risk management configuration
/// </summary>
public record WheelRiskConfig(
    decimal MaxDrawdownPercent = 0.10m,
    decimal MaxDailyLossPercent = 0.05m,
    int MaxActivePositions = 10,
    decimal MaxSinglePositionPercent = 0.05m,
    bool EnableEmergencyStop = true,
    decimal EmergencyStopThreshold = 0.15m,
    bool EnablePositionSizing = true,
    decimal VolatilityAdjustmentFactor = 1.0m
)
{
    public static WheelRiskConfig FromConfiguration(IConfiguration configuration)
    {
        var section = configuration.GetSection("WheelStrategy:Risk");
        
        return new WheelRiskConfig(
            section.GetValue<decimal>("MaxDrawdownPercent", 0.10m),
            section.GetValue<decimal>("MaxDailyLossPercent", 0.05m),
            section.GetValue<int>("MaxActivePositions", 10),
            section.GetValue<decimal>("MaxSinglePositionPercent", 0.05m),
            section.GetValue<bool>("EnableEmergencyStop", true),
            section.GetValue<decimal>("EmergencyStopThreshold", 0.15m),
            section.GetValue<bool>("EnablePositionSizing", true),
            section.GetValue<decimal>("VolatilityAdjustmentFactor", 1.0m)
        );
    }
}
