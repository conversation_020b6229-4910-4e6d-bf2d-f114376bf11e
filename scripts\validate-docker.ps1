# Docker Deployment Validation Script (PowerShell)
# This script validates the Docker deployment without actually running it

function Write-Status {
    param([string]$Message)
    Write-Host "[INFO] $Message" -ForegroundColor Blue
}

function Write-Success {
    param([string]$Message)
    Write-Host "[SUCCESS] $Message" -ForegroundColor Green
}

function Write-Warning {
    param([string]$Message)
    Write-Host "[WARNING] $Message" -ForegroundColor Yellow
}

function Write-Error {
    param([string]$Message)
    Write-Host "[ERROR] $Message" -ForegroundColor Red
}

Write-Status "Validating Docker deployment configuration..."

# Check if required files exist
$requiredFiles = @(
    "Dockerfile",
    "docker-compose.yml",
    ".dockerignore",
    "scripts/deploy.sh",
    "scripts/deploy.ps1",
    "scripts/setup-vault.sh",
    "scripts/setup-vault.ps1",
    "monitoring/prometheus/prometheus.yml",
    "monitoring/grafana/provisioning/datasources/prometheus.yml",
    "docs/docker-deployment.md"
)

$missingFiles = @()
foreach ($file in $requiredFiles) {
    if (Test-Path $file) {
        Write-Success "✓ $file exists"
    } else {
        Write-Error "✗ $file missing"
        $missingFiles += $file
    }
}

if ($missingFiles.Count -gt 0) {
    Write-Error "Missing required files. Docker deployment may not work correctly."
    exit 1
}

# Validate Dockerfile
Write-Status "Validating Dockerfile..."
$dockerfileContent = Get-Content "Dockerfile" -Raw

if ($dockerfileContent -match "FROM mcr\.microsoft\.com/dotnet/sdk:8\.0") {
    Write-Success "✓ Dockerfile uses correct .NET 8 SDK base image"
} else {
    Write-Error "✗ Dockerfile missing or incorrect .NET 8 SDK base image"
}

if ($dockerfileContent -match "FROM mcr\.microsoft\.com/dotnet/aspnet:8\.0") {
    Write-Success "✓ Dockerfile uses correct .NET 8 runtime base image"
} else {
    Write-Error "✗ Dockerfile missing or incorrect .NET 8 runtime base image"
}

if ($dockerfileContent -match "HEALTHCHECK") {
    Write-Success "✓ Dockerfile includes health check"
} else {
    Write-Warning "⚠ Dockerfile missing health check"
}

# Validate docker-compose.yml
Write-Status "Validating docker-compose.yml..."
$composeContent = Get-Content "docker-compose.yml" -Raw

if ($composeContent -match "version:") {
    Write-Success "✓ docker-compose.yml has version specified"
} else {
    Write-Error "✗ docker-compose.yml missing version"
}

$services = @("vault", "redis", "prometheus", "grafana", "bot")
foreach ($service in $services) {
    if ($composeContent -match "${service}:") {
        Write-Success "✓ Service '$service' defined in docker-compose.yml"
    } else {
        Write-Error "✗ Service '$service' missing from docker-compose.yml"
    }
}

# Check for health checks in docker-compose
if ($composeContent -match "healthcheck:") {
    Write-Success "✓ docker-compose.yml includes health checks"
} else {
    Write-Warning "⚠ docker-compose.yml missing health checks"
}

# Check for networks
if ($composeContent -match "networks:") {
    Write-Success "✓ docker-compose.yml defines networks"
} else {
    Write-Warning "⚠ docker-compose.yml missing network configuration"
}

# Check for volumes
if ($composeContent -match "volumes:") {
    Write-Success "✓ docker-compose.yml defines volumes"
} else {
    Write-Warning "⚠ docker-compose.yml missing volume configuration"
}

# Validate monitoring configuration
Write-Status "Validating monitoring configuration..."
$prometheusConfig = Get-Content "monitoring/prometheus/prometheus.yml" -Raw

if ($prometheusConfig -match "bot:5000") {
    Write-Success "✓ Prometheus configured to scrape bot metrics"
} else {
    Write-Error "✗ Prometheus not configured for bot metrics"
}

if (Test-Path "monitoring/grafana/provisioning/datasources/prometheus.yml") {
    $grafanaConfig = Get-Content "monitoring/grafana/provisioning/datasources/prometheus.yml" -Raw
    if ($grafanaConfig -match "prometheus:9090") {
        Write-Success "✓ Grafana configured with Prometheus datasource"
    } else {
        Write-Error "✗ Grafana datasource configuration incorrect"
    }
} else {
    Write-Error "✗ Grafana datasource configuration missing"
}

# Check project structure
Write-Status "Validating project structure..."
if (Test-Path "SmaTrendFollower.Console/SmaTrendFollower.Console.csproj") {
    Write-Success "✓ Main project file exists"
} else {
    Write-Error "✗ Main project file missing"
}

if (Test-Path "SmaTrendFollower.sln") {
    Write-Success "✓ Solution file exists"
} else {
    Write-Error "✗ Solution file missing"
}

# Check for .dockerignore patterns
Write-Status "Validating .dockerignore..."
$dockerignoreContent = Get-Content ".dockerignore" -Raw
$dockerignorePatterns = @("bin/", "obj/", "*.log", ".git", "TestResults/")

foreach ($pattern in $dockerignorePatterns) {
    if ($dockerignoreContent -match [regex]::Escape($pattern)) {
        Write-Success "✓ .dockerignore excludes $pattern"
    } else {
        Write-Warning "⚠ .dockerignore missing pattern: $pattern"
    }
}

Write-Status "Validation complete!"
Write-Success "Docker deployment configuration appears to be valid."
Write-Status "To deploy, run: .\scripts\deploy.ps1 (Windows) or ./scripts/deploy.sh (Linux/macOS)"
