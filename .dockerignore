# Git
.git
.gitignore
.gitattributes

# Documentation
*.md
docs/
Examples/

# Build artifacts
**/bin/
**/obj/
**/out/
**/publish/

# Test results
TestResults/
**/TestResults/

# Cache and temporary files
cache/
logs/
data/
backups/
*.db
*.log

# IDE and editor files
.vs/
.vscode/
*.user
*.suo
*.userosscache
*.sln.docstates

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Environment files
.env
.env.local
.env.production
.env.development

# Package managers
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Monitoring (will be mounted as volumes)
monitoring/

# Scripts (not needed in container)
scripts/
*.ps1
*.sh

# Backup files
backup-*.ps1
restore-*.ps1
*.backup

# Test projects (not needed for runtime)
SmaTrendFollower.Tests/
SmaTrendFollower.Tests.Core/
UniverseTest/
VixResolverTest/

# Temporary files
*.tmp
*.temp
*.swp
*.swo

# Docker files (avoid recursion)
Dockerfile
docker-compose*.yml
.dockerignore
