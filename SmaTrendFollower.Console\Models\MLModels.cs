using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace SmaTrendFollower.Models;

/// <summary>
/// Market regime classification for ML-based regime detection
/// </summary>
public enum MarketRegime
{
    /// <summary>
    /// Sideways/Neutral market conditions
    /// </summary>
    Sideways = 0,

    /// <summary>
    /// Trending up market conditions
    /// </summary>
    TrendingUp = 1,

    /// <summary>
    /// Trending down market conditions
    /// </summary>
    TrendingDown = 2,

    /// <summary>
    /// Panic/High volatility market conditions
    /// </summary>
    Panic = 3
}

/// <summary>
/// Entity representing signal features and forward returns for ML training.
/// Stores historical signal data with calculated features and actual outcomes.
/// </summary>
[Table("Features")]
[Index(nameof(Symbol), nameof(Date), IsUnique = true)]
public class SignalFeature
{
    [Key]
    [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
    public int Id { get; set; }

    /// <summary>
    /// Stock symbol (e.g., "AAPL", "TSLA")
    /// </summary>
    [Required]
    [MaxLength(20)]
    public string Symbol { get; set; } = string.Empty;

    /// <summary>
    /// Date when the signal was generated
    /// </summary>
    [Required]
    public DateTime Date { get; set; }

    /// <summary>
    /// Close price / 50-day SMA ratio (SMA gap feature)
    /// </summary>
    [Required]
    public float SmaGap { get; set; }

    /// <summary>
    /// ATR / Close price ratio (volatility feature)
    /// </summary>
    [Required]
    public float Volatility { get; set; }

    /// <summary>
    /// 14-period RSI value
    /// </summary>
    [Required]
    public float Rsi { get; set; }

    /// <summary>
    /// Market breadth score (0-1 scale)
    /// </summary>
    [Required]
    public float BreadthScore { get; set; }

    /// <summary>
    /// VIX level at signal generation time
    /// </summary>
    [Required]
    public float VixLevel { get; set; }

    /// <summary>
    /// 6-month momentum return
    /// </summary>
    [Required]
    public float SixMonthReturn { get; set; }

    /// <summary>
    /// Volume relative to 20-day average
    /// </summary>
    [Required]
    public float RelativeVolume { get; set; }

    /// <summary>
    /// Market regime classification (0=Bear, 1=Bull, 0.5=Neutral)
    /// </summary>
    [Required]
    public float MarketRegime { get; set; }

    /// <summary>
    /// Forward 3-day return (target variable for ML)
    /// </summary>
    [Required]
    public float Forward3DReturn { get; set; }

    /// <summary>
    /// Forward 5-day return (alternative target)
    /// </summary>
    [Required]
    public float Forward5DReturn { get; set; }

    /// <summary>
    /// Forward 10-day return (longer-term target)
    /// </summary>
    [Required]
    public float Forward10DReturn { get; set; }

    /// <summary>
    /// Maximum favorable excursion within 10 days
    /// </summary>
    [Required]
    public float MaxFavorableExcursion { get; set; }

    /// <summary>
    /// Maximum adverse excursion within 10 days
    /// </summary>
    [Required]
    public float MaxAdverseExcursion { get; set; }

    /// <summary>
    /// Signal ranking probability from ML model (0.0 to 1.0)
    /// </summary>
    [Required]
    public float RankProb { get; set; }

    /// <summary>
    /// ATR as percentage of close price
    /// </summary>
    [Required]
    public float ATR_Pct { get; set; }

    /// <summary>
    /// 10-day average spread as percentage of price
    /// </summary>
    [Required]
    public float AvgSpreadPct { get; set; }

    /// <summary>
    /// Target equity risk percentage (label for position sizing model)
    /// </summary>
    [Required]
    public float EquityPctRisk { get; set; }

    /// <summary>
    /// When this feature record was created
    /// </summary>
    [Required]
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
}

/// <summary>
/// Input features for ML model prediction
/// </summary>
public record SignalFeatures(
    float SmaGap,
    float Volatility,
    float Rsi,
    float BreadthScore,
    float VixLevel,
    float SixMonthReturn,
    float RelativeVolume,
    float MarketRegime
);

/// <summary>
/// ML model input record for training and prediction
/// </summary>
public record MLSignalInput
{
    public string Symbol { get; set; } = string.Empty;
    public float SmaGap { get; set; }
    public float Volatility { get; set; }
    public float Rsi { get; set; }
    public float BreadthScore { get; set; }
    public float VixLevel { get; set; }
    public float SixMonthReturn { get; set; }
    public float RelativeVolume { get; set; }
    public float MarketRegime { get; set; }
    public bool Win { get; set; } // Target: Forward3DReturn >= 0.01 (1%)
}

/// <summary>
/// ML model output for binary classification
/// </summary>
public record MLSignalOutput
{
    public bool PredictedLabel { get; set; }
    public float Probability { get; set; }
    public float Score { get; set; }
}

/// <summary>
/// Enhanced trading signal with ML probability score
/// </summary>
public record MLEnhancedTradingSignal(
    string Symbol,
    decimal Price,
    decimal Atr,
    decimal SixMonthReturn,
    float MLProbability,
    float MLScore,
    SignalFeatures Features
)
{
    /// <summary>
    /// Converts to a standard TradingSignal
    /// </summary>
    public TradingSignal ToTradingSignal() => new(Symbol, Price, Atr, SixMonthReturn);
};

/// <summary>
/// ML model metadata and performance metrics
/// </summary>
public record MLModelInfo(
    string ModelPath,
    string ModelVersion,
    DateTime TrainedAt,
    float Accuracy,
    float Precision,
    float Recall,
    float F1Score,
    int TrainingSamples,
    int ValidationSamples
);

/// <summary>
/// Feature importance scores from trained model
/// </summary>
public record FeatureImportance(
    string FeatureName,
    float Importance,
    int Rank
);

/// <summary>
/// Entity representing order fill data for slippage analysis and ML training.
/// Stores execution details with market context for slippage prediction models.
/// </summary>
[Table("FillsLog")]
[Index(nameof(Symbol), nameof(TimeUtc), IsUnique = false)]
public class FillLog
{
    [Key]
    [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
    public int Id { get; set; }

    /// <summary>
    /// UTC timestamp when the fill occurred
    /// </summary>
    [Required]
    public DateTime TimeUtc { get; set; }

    /// <summary>
    /// Stock symbol (e.g., "AAPL", "TSLA")
    /// </summary>
    [Required]
    [MaxLength(20)]
    public string Symbol { get; set; } = string.Empty;

    /// <summary>
    /// Order side: 0 = Buy, 1 = Sell
    /// </summary>
    [Required]
    public int Side { get; set; }

    /// <summary>
    /// Quantity filled
    /// </summary>
    [Required]
    public decimal Qty { get; set; }

    /// <summary>
    /// Actual fill price received
    /// </summary>
    [Required]
    public decimal FillPrice { get; set; }

    /// <summary>
    /// Mid price at time of order (Bid + Ask) / 2
    /// </summary>
    [Required]
    public decimal MidPrice { get; set; }

    /// <summary>
    /// Bid-ask spread as percentage of mid price
    /// </summary>
    [Required]
    public float SpreadPct { get; set; }

    /// <summary>
    /// ML signal ranking probability (0.0 - 1.0)
    /// </summary>
    [Required]
    public float RankProb { get; set; }

    /// <summary>
    /// Market regime indicator (0.0 - 1.0)
    /// </summary>
    [Required]
    public float Regime { get; set; }

    /// <summary>
    /// ATR as percentage of price
    /// </summary>
    [Required]
    public float ATR_Pct { get; set; }

    /// <summary>
    /// Volume as percentage of 10-day average
    /// </summary>
    [Required]
    public float VolumePct10d { get; set; }

    /// <summary>
    /// When this fill record was created
    /// </summary>
    [Required]
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
}

/// <summary>
/// Input features for slippage prediction model
/// </summary>
public record SlippageInput(
    float SpreadPct,
    float RankProb,
    float ATR_Pct,
    float VolumePct10d,
    float Regime,
    uint Side,
    int Hour
);

/// <summary>
/// Output from slippage prediction model
/// </summary>
public record SlippageOutput(
    float Score // Predicted slippage in basis points
);

/// <summary>
/// Quote context for slippage prediction
/// </summary>
public record QuoteContext(
    decimal MidPrice,
    float SpreadPct,
    DateTime TimestampUtc
);

/// <summary>
/// ML training configuration
/// </summary>
public record MLTrainingConfig(
    int MaxExperimentTimeSeconds = 180,
    float TrainTestSplit = 0.8f,
    float WinThreshold = 0.01f, // 1% return threshold for "win"
    string ModelOutputPath = "Model/signal_model.zip",
    bool UseAutoML = true,
    int MaxModelsToExplore = 50
);

/// <summary>
/// Slippage training configuration
/// </summary>
public record SlippageTrainingConfig(
    int MaxExperimentTimeSeconds = 300,
    float TrainTestSplit = 0.8f,
    string ModelOutputPath = "Model/slippage_model.zip",
    int NumberOfTrees = 200,
    int NumberOfLeaves = 32,
    int TrainingDataDays = 60
);

// ===== REGIME CLASSIFICATION MODELS =====

/// <summary>
/// Input features for regime classification model training and prediction
/// </summary>
public record RegimeInput(
    float SPX_Ret,
    float VIX_Level,
    float VIX_Change,
    float Breadth_Score
);

/// <summary>
/// Output from regime classification model
/// </summary>
public class RegimeOutput
{
    public uint PredictedLabel { get; set; }
    public float[] Score { get; set; } = Array.Empty<float>();
}

/// <summary>
/// Training row for regime classification CSV export
/// </summary>
public record RegimeRow(
    DateTime Date,
    float SPX_Ret,
    float VIX_Level,
    float VIX_Change,
    float Breadth_Score,
    uint RegimeLabel
);
