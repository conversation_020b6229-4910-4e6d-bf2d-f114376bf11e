using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using StackExchange.Redis;
using SmaTrendFollower.Services;

namespace SmaTrendFollower.Examples;

/// <summary>
/// Demonstration of QuoteVolatilityGuard functionality
/// Shows how the guard detects spread volatility and sets halt keys
/// </summary>
public static class QuoteVolatilityGuardDemo
{
    // Commented out to avoid multiple entry point warnings
    /*
    public static async Task Main(string[] args)
    {
        System.Console.WriteLine("🔍 QuoteVolatilityGuard Demonstration");
        System.Console.WriteLine("=====================================");

        var host = CreateHost();
        using var scope = host.Services.CreateScope();
        
        var guard = scope.ServiceProvider.GetRequiredService<QuoteVolatilityGuard>();
        var redis = scope.ServiceProvider.GetRequiredService<IConnectionMultiplexer>();
        var database = redis.GetDatabase();
        var logger = scope.ServiceProvider.GetRequiredService<ILogger>();

        const string testSymbol = "DEMO";
        const decimal basePrice = 100.00m;

        try
        {
            // Clean up any existing halt keys
            await database.KeyDeleteAsync($"halt:{testSymbol}");
            
            System.Console.WriteLine($"\n📊 Step 1: Feeding normal quotes for {testSymbol}");
            System.Console.WriteLine("   Building baseline spread statistics...");

            // Feed normal quotes to establish baseline (tight spreads)
            for (int i = 0; i < 60; i++)
            {
                var normalBid = basePrice - 0.05m; // 5 cent spread
                var normalAsk = basePrice + 0.05m;
                guard.OnQuote(testSymbol, normalBid, normalAsk);
                
                if (i % 20 == 0)
                {
                    var stats = guard.GetSpreadStats(testSymbol);
                    if (stats.HasValue)
                    {
                        System.Console.WriteLine($"   Quote {i + 1}: Mean spread = {stats.Value.Mean:F6}, StdDev = {stats.Value.StandardDeviation:F6}");
                    }
                }
            }

            // Check initial halt status
            var initialHalt = await guard.IsTradingHaltedAsync(testSymbol);
            System.Console.WriteLine($"   Initial halt status: {(initialHalt ? "HALTED" : "ACTIVE")}");

            System.Console.WriteLine($"\n⚡ Step 2: Simulating volatile spread for {testSymbol}");
            System.Console.WriteLine("   Feeding extremely wide spread...");

            // Feed extremely volatile spread (should trigger halt)
            var volatileBid = basePrice - 2.00m; // $4.00 spread (vs normal $0.10)
            var volatileAsk = basePrice + 2.00m;

            System.Console.WriteLine($"   Normal spread: $0.10 ({((0.10m / basePrice) * 100):F3}%)");
            System.Console.WriteLine($"   Volatile spread: $4.00 ({((4.00m / basePrice) * 100):F3}%)");
            
            guard.OnQuote(testSymbol, volatileBid, volatileAsk);

            // Wait for async halt operation
            await Task.Delay(500);

            System.Console.WriteLine($"\n🚨 Step 3: Checking halt status after volatile quote");

            // Check halt status
            var haltedAfterVolatility = await guard.IsTradingHaltedAsync(testSymbol);
            System.Console.WriteLine($"   Halt status: {(haltedAfterVolatility ? "HALTED ✅" : "ACTIVE ❌")}");

            // Check Redis key directly
            var haltValue = await database.StringGetAsync($"halt:{testSymbol}");
            var ttl = await database.KeyTimeToLiveAsync($"halt:{testSymbol}");

            if (haltValue.HasValue)
            {
                System.Console.WriteLine($"   Redis halt key value: '{haltValue}'");
                System.Console.WriteLine($"   TTL remaining: {ttl?.TotalMinutes:F1} minutes");
            }
            else
            {
                System.Console.WriteLine("   ❌ No halt key found in Redis");
            }

            System.Console.WriteLine($"\n📈 Step 4: Spread statistics summary");
            var finalStats = guard.GetSpreadStats(testSymbol);
            if (finalStats.HasValue)
            {
                System.Console.WriteLine($"   Total quotes processed: {finalStats.Value.Count}");
                System.Console.WriteLine($"   Window size: {finalStats.Value.WindowSize}");
                System.Console.WriteLine($"   Mean spread: {finalStats.Value.Mean:F6}");
                System.Console.WriteLine($"   Standard deviation: {finalStats.Value.StandardDeviation:F6}");
                System.Console.WriteLine($"   Has sufficient data: {finalStats.Value.HasSufficientData}");
            }

            System.Console.WriteLine($"\n🔄 Step 5: Testing halt expiration");
            System.Console.WriteLine("   Waiting for halt to expire...");
            
            // Wait for halt to expire (2 minutes in real scenario, but we'll check periodically)
            var startTime = DateTime.UtcNow;
            while (DateTime.UtcNow - startTime < TimeSpan.FromSeconds(10)) // Check for 10 seconds
            {
                var currentHalt = await guard.IsTradingHaltedAsync(testSymbol);
                var currentTtl = await database.KeyTimeToLiveAsync($"halt:{testSymbol}");
                
                System.Console.WriteLine($"   Time: {(DateTime.UtcNow - startTime).TotalSeconds:F1}s, " +
                                $"Halted: {(currentHalt ? "YES" : "NO")}, " +
                                $"TTL: {currentTtl?.TotalSeconds:F0}s");
                
                if (!currentHalt)
                {
                    System.Console.WriteLine("   ✅ Halt has expired!");
                    break;
                }

                await Task.Delay(2000);
            }

            System.Console.WriteLine($"\n✅ Demo completed successfully!");
            System.Console.WriteLine("\nKey Features Demonstrated:");
            System.Console.WriteLine("• Real-time spread volatility detection");
            System.Console.WriteLine("• Automatic trading halt with Redis key");
            System.Console.WriteLine("• 2-sigma threshold for sensitive detection");
            System.Console.WriteLine("• TTL-based automatic halt expiration");
            System.Console.WriteLine("• Thread-safe statistics tracking");
        }
        catch (Exception ex)
        {
            System.Console.WriteLine($"\n❌ Demo failed: {ex.Message}");
            logger.LogError(ex, "QuoteVolatilityGuard demo failed");
        }
        finally
        {
            // Cleanup
            await database.KeyDeleteAsync($"halt:{testSymbol}");
            guard.ClearSpreadStats(testSymbol);
            System.Console.WriteLine("\n🧹 Cleanup completed");
        }
    }
    */

    private static IHost CreateHost()
    {
        var configuration = new ConfigurationBuilder()
            .AddInMemoryCollection(new Dictionary<string, string?>
            {
                ["ConnectionStrings:Redis"] = "*************:6379"
            })
            .Build();

        return Host.CreateDefaultBuilder()
            .ConfigureServices(services =>
            {
                services.AddSingleton<IConfiguration>(configuration);
                services.AddLogging(builder => 
                {
                    builder.AddConsole();
                    builder.SetMinimumLevel(LogLevel.Information);
                });

                // Register Redis connection
                services.AddSingleton<IConnectionMultiplexer>(provider =>
                {
                    var connectionString = configuration.GetConnectionString("Redis") ?? "*************:6379";
                    return ConnectionMultiplexer.Connect(connectionString);
                });

                // Register QuoteVolatilityGuard
                services.AddSingleton<QuoteVolatilityGuard>();
            })
            .Build();
    }
}
