#!/usr/bin/env bash
# Docker Deployment Validation Script
# This script validates the Docker deployment without actually running it

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_status "Validating Docker deployment configuration..."

# Check if required files exist
required_files=(
    "Dockerfile"
    "docker-compose.yml"
    ".dockerignore"
    "scripts/deploy.sh"
    "scripts/deploy.ps1"
    "scripts/setup-vault.sh"
    "scripts/setup-vault.ps1"
    "monitoring/prometheus/prometheus.yml"
    "monitoring/grafana/provisioning/datasources/prometheus.yml"
    "docs/docker-deployment.md"
)

missing_files=()
for file in "${required_files[@]}"; do
    if [ -f "$file" ]; then
        print_success "✓ $file exists"
    else
        print_error "✗ $file missing"
        missing_files+=("$file")
    fi
done

if [ ${#missing_files[@]} -gt 0 ]; then
    print_error "Missing required files. Docker deployment may not work correctly."
    exit 1
fi

# Validate Dockerfile
print_status "Validating Dockerfile..."
if grep -q "FROM mcr.microsoft.com/dotnet/sdk:8.0" Dockerfile; then
    print_success "✓ Dockerfile uses correct .NET 8 SDK base image"
else
    print_error "✗ Dockerfile missing or incorrect .NET 8 SDK base image"
fi

if grep -q "FROM mcr.microsoft.com/dotnet/aspnet:8.0" Dockerfile; then
    print_success "✓ Dockerfile uses correct .NET 8 runtime base image"
else
    print_error "✗ Dockerfile missing or incorrect .NET 8 runtime base image"
fi

if grep -q "HEALTHCHECK" Dockerfile; then
    print_success "✓ Dockerfile includes health check"
else
    print_warning "⚠ Dockerfile missing health check"
fi

# Validate docker-compose.yml
print_status "Validating docker-compose.yml..."
if grep -q "version:" docker-compose.yml; then
    print_success "✓ docker-compose.yml has version specified"
else
    print_error "✗ docker-compose.yml missing version"
fi

services=("vault" "redis" "prometheus" "grafana" "bot")
for service in "${services[@]}"; do
    if grep -q "$service:" docker-compose.yml; then
        print_success "✓ Service '$service' defined in docker-compose.yml"
    else
        print_error "✗ Service '$service' missing from docker-compose.yml"
    fi
done

# Check for health checks in docker-compose
if grep -q "healthcheck:" docker-compose.yml; then
    print_success "✓ docker-compose.yml includes health checks"
else
    print_warning "⚠ docker-compose.yml missing health checks"
fi

# Check for networks
if grep -q "networks:" docker-compose.yml; then
    print_success "✓ docker-compose.yml defines networks"
else
    print_warning "⚠ docker-compose.yml missing network configuration"
fi

# Check for volumes
if grep -q "volumes:" docker-compose.yml; then
    print_success "✓ docker-compose.yml defines volumes"
else
    print_warning "⚠ docker-compose.yml missing volume configuration"
fi

# Validate monitoring configuration
print_status "Validating monitoring configuration..."
if grep -q "bot:5000" monitoring/prometheus/prometheus.yml; then
    print_success "✓ Prometheus configured to scrape bot metrics"
else
    print_error "✗ Prometheus not configured for bot metrics"
fi

if [ -f "monitoring/grafana/provisioning/datasources/prometheus.yml" ]; then
    if grep -q "prometheus:9090" monitoring/grafana/provisioning/datasources/prometheus.yml; then
        print_success "✓ Grafana configured with Prometheus datasource"
    else
        print_error "✗ Grafana datasource configuration incorrect"
    fi
else
    print_error "✗ Grafana datasource configuration missing"
fi

# Check project structure
print_status "Validating project structure..."
if [ -f "SmaTrendFollower.Console/SmaTrendFollower.Console.csproj" ]; then
    print_success "✓ Main project file exists"
else
    print_error "✗ Main project file missing"
fi

if [ -f "SmaTrendFollower.sln" ]; then
    print_success "✓ Solution file exists"
else
    print_error "✗ Solution file missing"
fi

# Check for .dockerignore patterns
print_status "Validating .dockerignore..."
dockerignore_patterns=("bin/" "obj/" "*.log" ".git" "TestResults/")
for pattern in "${dockerignore_patterns[@]}"; do
    if grep -q "$pattern" .dockerignore; then
        print_success "✓ .dockerignore excludes $pattern"
    else
        print_warning "⚠ .dockerignore missing pattern: $pattern"
    fi
done

print_status "Validation complete!"
print_success "Docker deployment configuration appears to be valid."
print_status "To deploy, run: ./scripts/deploy.sh (Linux/macOS) or .\scripts\deploy.ps1 (Windows)"
