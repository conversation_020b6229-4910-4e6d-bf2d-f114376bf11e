# Real-time Streaming Implementation

This document describes the real-time streaming implementation for SmaTrendFollower, providing live market data from both Alpaca and Polygon APIs.

## Overview

The streaming architecture follows the user's preferred pattern:
- **Alpaca WebSocket**: For equities trading/exit signals and trade updates
- **Polygon WebSocket**: For index and volatility triggers (VIX spikes, SPX movements)

## Architecture

### Core Components

1. **IStreamingDataService**: Main interface for all streaming operations
2. **StreamingDataService**: Unified service managing both Alpaca and Polygon streams
3. **IPolygonWebSocketClient**: Dedicated Polygon WebSocket client
4. **PolygonWebSocketClient**: Implementation with auto-reconnection and error handling
5. **PolygonWebSocketManager**: Advanced WebSocket manager for batch subscriptions and rate limiting
6. **PolygonWsChannel**: Channel enumeration for different data stream types

### Key Features

- **Automatic Reconnection**: Both services implement exponential backoff retry logic
- **Connection Management**: Proper connection lifecycle with graceful degradation
- **Event-driven Architecture**: Clean separation of concerns with typed event args
- **Error Handling**: Comprehensive error handling with logging and recovery
- **Rate Limiting**: Respects API limits (Alpaca: 200 req/min, Polygon: 5 req/sec)
- **Batch Subscriptions**: PolygonWebSocketManager handles 200+ symbols with automatic chunking
- **Redis Persistence**: Subscription lists persisted for auto re-subscription on reconnect
- **Circuit Breaker**: Rate limit protection with automatic circuit breaking
- **Prometheus Metrics**: WebSocket connection and subscription monitoring

## PolygonWebSocketManager

### Overview

The `PolygonWebSocketManager` is an advanced WebSocket management system designed to handle large-scale symbol subscriptions (200+ symbols) without hitting Polygon rate limits. It provides:

- **Batch Subscriptions**: Automatically chunks symbols into ≤500-symbol groups
- **Multiple Channels**: Separate sockets for equity vs index streams
- **Redis Persistence**: Auto re-subscription on reconnect using Redis storage
- **Rate Limit Protection**: Circuit breaker for 429 errors with exponential backoff
- **Prometheus Metrics**: Connection and subscription monitoring

### Channel Types

```csharp
public enum PolygonWsChannel
{
    EquityTrades,   // T.* - Real-time trade execution data
    EquityQuotes,   // Q.* - Real-time bid/ask quote data
    EquityMinute,   // AM.* - 1-minute OHLCV bars for stocks
    IndexMinute     // AM.I:* - 1-minute OHLCV bars for indices (VIX, SPX, etc.)
}
```

### Usage Examples

#### Basic Subscription
```csharp
// Inject the manager
private readonly PolygonWebSocketManager _wsManager;

// Subscribe to equity minute bars for universe
var universe = new[] { "AAPL", "MSFT", "GOOGL", /* ... 200+ symbols */ };
await _wsManager.BatchSubscribeAsync(PolygonWsChannel.EquityMinute, universe);

// Subscribe to index data
var indices = new[] { "VIX", "SPX", "NDX" };
await _wsManager.BatchSubscribeAsync(PolygonWsChannel.IndexMinute, indices);
```

#### Connection Management
```csharp
// Get connection status
var state = _wsManager.GetConnectionState(PolygonWsChannel.EquityTrades);
var subscribedCount = _wsManager.GetSubscribedSymbolCount(PolygonWsChannel.EquityTrades);

// Disconnect all connections
await _wsManager.DisconnectAllAsync();
```

### Redis Integration

The manager automatically persists subscription lists to Redis:

```
ws:subs:EquityMinute   → "AAPL,MSFT,GOOGL,..."
ws:subs:IndexMinute    → "VIX,SPX,NDX"
```

Keys have 24-hour TTL and are used for auto re-subscription on reconnect.

### Rate Limiting & Circuit Breaker

- **Batch Size**: Maximum 500 symbols per subscription message
- **429 Handling**: 2-second retry delay, circuit breaker after 3 consecutive errors
- **Exponential Backoff**: 1s, 2s, 4s, 8s, 16s, 32s (max) for reconnection attempts

### Metrics

Prometheus metrics are automatically recorded:

```
websocket_reconnect_total{channel="EquityMinute"}
websocket_subscribed_symbols{channel="EquityMinute"}
```

## Usage

### Basic Setup

```csharp
// Configure services
services.AddSingleton<IAlpacaClientFactory, AlpacaClientFactory>();
services.AddSingleton<IPolygonClientFactory, PolygonClientFactory>();
services.AddSingleton<IStreamingDataService, StreamingDataService>();

// Get service
var streamingService = serviceProvider.GetRequiredService<IStreamingDataService>();
```

### Environment Variables Required

```bash
# Alpaca API credentials
APCA_API_KEY_ID=your-alpaca-key-id
APCA_API_SECRET_KEY=your-alpaca-secret-key
APCA_API_ENV=paper  # or live

# Polygon API key
POLY_API_KEY=your-polygon-api-key
```

### Connecting to Streams

```csharp
// Connect to Alpaca for equity data
await streamingService.ConnectAlpacaStreamAsync();

// Connect to Polygon for index data
await streamingService.ConnectPolygonStreamAsync();
```

### Event Handling

```csharp
// Real-time quotes from Alpaca
streamingService.QuoteReceived += (sender, args) =>
{
    Console.WriteLine($"Quote: {args.Symbol} Bid: {args.BidPrice} Ask: {args.AskPrice}");
};

// Real-time bars from Alpaca
streamingService.BarReceived += (sender, args) =>
{
    Console.WriteLine($"Bar: {args.Symbol} OHLCV: {args.Open}/{args.High}/{args.Low}/{args.Close}/{args.Volume}");
};

// Trade updates from Alpaca
streamingService.TradeUpdated += (sender, args) =>
{
    Console.WriteLine($"Trade: {args.Symbol} {args.Side} {args.Quantity} @ {args.Price}");
};

// Index updates from Polygon (VIX spikes, etc.)
streamingService.IndexUpdated += (sender, args) =>
{
    Console.WriteLine($"Index: {args.IndexSymbol} = {args.Value} ({args.ChangePercent:F2}%)");
    
    // Detect VIX spikes
    if (args.IndexSymbol == "I:VIX" && args.Value > 30)
    {
        Console.WriteLine("VIX SPIKE DETECTED!");
    }
};
```

### Subscriptions

```csharp
// Subscribe to equity quotes and bars
var symbols = new[] { "AAPL", "MSFT", "SPY" };
await streamingService.SubscribeToQuotesAsync(symbols);
await streamingService.SubscribeToBarsAsync(symbols);

// Subscribe to trade updates (your own trades)
await streamingService.SubscribeToTradeUpdatesAsync();

// Subscribe to index data for volatility triggers
var indices = new[] { "I:VIX", "I:SPX", "I:NDX" };
await streamingService.SubscribeToIndexUpdatesAsync(indices);
```

## Data Sources

### Alpaca Markets
- **Subscription**: Levels 1-3 options + AlgoTrader Plus market data
- **Usage**: Account/positions/fills, equity/ETF bars, real-time quotes
- **Rate Limit**: 200 requests/minute
- **WebSocket**: Trading updates, market data streaming

### Polygon.io
- **Subscription**: Indices Starter + Options Starter
- **Usage**: Index data (SPX/VIX), options Greeks/IV/OI
- **Rate Limit**: 5 requests/second
- **WebSocket**: Real-time index values and volatility data

## Connection Management

### Automatic Reconnection

Both services implement robust reconnection logic:

```csharp
// Exponential backoff with jitter
var delay = TimeSpan.FromMilliseconds(baseDelay * Math.Pow(2, attempt - 1));
delay = delay.Add(TimeSpan.FromMilliseconds(Random.Shared.Next(0, 1000)));
```

### Error Handling

- **Connection Failures**: Automatic retry with exponential backoff
- **Authentication Errors**: Logged and reported via events
- **Network Issues**: Graceful degradation and reconnection
- **API Rate Limits**: Built-in respect for rate limits

### Graceful Shutdown

```csharp
// Unsubscribe from all streams
await streamingService.UnsubscribeAllAsync();

// Disconnect from all services
await streamingService.DisconnectAllAsync();
```

## Testing

### Unit Tests
- Comprehensive mocking of external dependencies
- Event handler testing
- Connection lifecycle testing
- Error scenario testing

### Integration Tests
- Real API connectivity (marked as skipped by default)
- End-to-end streaming workflows
- Performance and reliability testing

## Examples

See `Examples/StreamingExample.cs` for a complete working example demonstrating:
- Service setup and configuration
- Event handler registration
- Connection management
- Subscription management
- VIX spike detection
- Proper cleanup

## Implementation Notes

### Current Status
- ✅ Core architecture implemented
- ✅ Polygon WebSocket client complete
- ✅ Connection management and resilience
- ✅ Comprehensive unit tests
- ⚠️ Alpaca streaming API placeholders (pending API confirmation)

### Alpaca Streaming API
The Alpaca.Markets SDK streaming methods are implemented as placeholders pending confirmation of the exact API. The structure is in place and can be completed once the correct method signatures are verified.

### Performance Considerations
- **Memory Usage**: Event handlers are lightweight and non-blocking
- **Threading**: All async operations use proper cancellation tokens
- **Resource Management**: Proper disposal patterns implemented
- **Logging**: Structured logging for monitoring and debugging

## QuoteVolatilityGuard

### Overview

The `QuoteVolatilityGuard` is a real-time quote volatility monitoring service that detects unusual bid-ask spread volatility and automatically halts trading for affected symbols. It complements the `AnomalyDetectorService` by focusing specifically on quote-level anomalies.

### Key Features

- **Real-time Spread Analysis**: Monitors bid-ask spreads using rolling 120-quote window (≈ 2 minutes)
- **Sensitive Detection**: Uses 2σ threshold (vs 3σ in AnomalyDetector) for faster anomaly detection
- **Automatic Trading Halts**: Sets Redis halt keys with 2-minute TTL to block trade execution
- **Prometheus Metrics**: Comprehensive instrumentation for monitoring and alerting
- **Thread-safe**: Concurrent processing of multiple symbols without interference

### Integration

```csharp
// Automatic integration in PolygonWebSocketManager
public void OnQuote(string symbol, decimal bid, decimal ask)
{
    _anomalyDetector?.OnQuote(symbol, bid, ask, timestamp);
    _quoteVolatilityGuard?.OnQuote(symbol, bid, ask);  // Added integration
}
```

### Configuration

- **Window Size**: 120 quotes (≈ 2 minutes of data)
- **Threshold**: 2.0 standard deviations
- **Halt Duration**: 2 minutes
- **Redis Key Pattern**: `halt:{symbol}` with value `"volatility"`

### Metrics

| Metric | Type | Description | Labels |
|--------|------|-------------|---------|
| `quote_vol_halts_total` | Counter | Total quote volatility halts | `symbol` |
| `quote_vol_halted` | Gauge | Quote volatility halt active | `symbol` |
| `spread_z_scores` | Histogram | Distribution of spread z-scores | `symbol` |
| `spread_checks_total` | Counter | Total spread checks performed | `symbol` |

### Interaction with AnomalyDetector

Both services use the same `halt:{symbol}` Redis key pattern but with different values:
- **QuoteVolatilityGuard**: Sets value to `"volatility"`
- **AnomalyDetectorService**: Sets value to `"1"`

The existing halt checking logic in `TradeExecutor` and `SimpleSignalGenerator` works with both services since they only check for key existence.

## Monitoring and Observability

The implementation includes comprehensive logging at key points:
- Connection status changes
- Subscription/unsubscription events
- Data reception rates
- Error conditions and recovery attempts
- Performance metrics
- Quote volatility anomaly detection and halt events

This enables effective monitoring of the streaming infrastructure in production environments.
