using SmaTrendFollower.Models;

namespace SmaTrendFollower.Services;

/// <summary>
/// Adaptive learning service that continuously optimizes strategy parameters based on performance feedback
/// </summary>
public interface IAdaptiveLearningService
{
    /// <summary>
    /// Learns from recent trading outcomes and adjusts strategy parameters
    /// </summary>
    Task<ParameterAdjustmentResult> LearnFromOutcomesAsync(
        IEnumerable<TradeOutcome> recentOutcomes, 
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Predicts optimal parameters for current market conditions
    /// </summary>
    Task<OptimalParameterPrediction> PredictOptimalParametersAsync(
        MarketConditions currentConditions, 
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Updates the learning model with new performance data
    /// </summary>
    Task<ModelUpdateResult> UpdateModelAsync(
        IEnumerable<PerformanceSample> samples, 
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Evaluates if strategy parameters should be adjusted based on recent performance
    /// </summary>
    Task<ParameterAdjustmentRecommendation> EvaluateParameterAdjustmentAsync(
        TimeSpan evaluationPeriod, 
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets current model performance and learning statistics
    /// </summary>
    Task<LearningModelMetrics> GetModelMetricsAsync();

    /// <summary>
    /// Performs online learning update with latest trade result
    /// </summary>
    Task UpdateOnlineAsync(TradeOutcome outcome, MarketConditions conditions);

    /// <summary>
    /// Validates model predictions against actual outcomes
    /// </summary>
    Task<ModelValidationMetrics> ValidateModelAsync(
        IEnumerable<ValidationSample> testSamples, 
        CancellationToken cancellationToken = default);
}

/// <summary>
/// Trade outcome for learning
/// </summary>
public record TradeOutcome(
    string Symbol,
    DateTime EntryTime,
    DateTime? ExitTime,
    decimal EntryPrice,
    decimal? ExitPrice,
    decimal Quantity,
    decimal PnL,
    decimal PnLPercent,
    string ExitReason,
    MarketConditions EntryConditions,
    MarketConditions? ExitConditions,
    StrategyParameters UsedParameters
);

/// <summary>
/// Market conditions at time of trade
/// </summary>
public record MarketConditions(
    DateTime Timestamp,
    decimal VIX,
    decimal SPYPrice,
    decimal SPY_SMA50,
    decimal SPY_SMA200,
    MarketRegime Regime,
    decimal MarketVolatility,
    decimal SectorRotation,
    TimeOfDay TimeOfDay,
    DayOfWeek DayOfWeek,
    bool IsEarningsWeek,
    bool IsFOMCWeek,
    decimal DollarIndex,
    decimal TenYearYield
);

/// <summary>
/// Strategy parameters used for trade
/// </summary>
public record StrategyParameters(
    decimal SMA50Period,
    decimal SMA200Period,
    decimal ATRMultiplier,
    decimal VolatilityThreshold,
    decimal RiskPerTrade,
    decimal MaxPositionSize,
    int LookbackPeriod,
    decimal MomentumThreshold,
    bool UseTrailingStop,
    decimal TrailingStopATR
);

/// <summary>
/// Result of parameter adjustment learning
/// </summary>
public record ParameterAdjustmentResult(
    bool ShouldAdjust,
    StrategyParameters RecommendedParameters,
    StrategyParameters CurrentParameters,
    decimal ExpectedImprovement,
    AdjustmentConfidence Confidence,
    IReadOnlyList<ParameterChange> Changes,
    string Rationale,
    DateTime GeneratedAt
);

/// <summary>
/// Individual parameter change
/// </summary>
public record ParameterChange(
    string ParameterName,
    decimal OldValue,
    decimal NewValue,
    decimal ExpectedImpact,
    string Reason
);

/// <summary>
/// Optimal parameter prediction
/// </summary>
public record OptimalParameterPrediction(
    StrategyParameters PredictedParameters,
    decimal PredictedSharpeRatio,
    decimal PredictedWinRate,
    decimal PredictedMaxDrawdown,
    PredictionConfidence Confidence,
    IReadOnlyList<ParameterImportance> ParameterImportances,
    DateTime PredictedAt
);

/// <summary>
/// Parameter importance for prediction
/// </summary>
public record ParameterImportance(
    string ParameterName,
    decimal Importance,
    string Impact
);

/// <summary>
/// Model update result
/// </summary>
public record ModelUpdateResult(
    bool UpdateSuccessful,
    int SamplesProcessed,
    decimal ModelAccuracyBefore,
    decimal ModelAccuracyAfter,
    decimal ImprovementPercent,
    IReadOnlyList<string> UpdatedFeatures,
    DateTime UpdatedAt
);

/// <summary>
/// Performance sample for learning
/// </summary>
public record PerformanceSample(
    StrategyParameters Parameters,
    MarketConditions Conditions,
    decimal ActualReturn,
    decimal ActualSharpe,
    decimal ActualMaxDrawdown,
    int TradeCount,
    DateTime PeriodStart,
    DateTime PeriodEnd
);

/// <summary>
/// Parameter adjustment recommendation
/// </summary>
public record ParameterAdjustmentRecommendation(
    bool RecommendAdjustment,
    AdjustmentUrgency Urgency,
    IReadOnlyList<ParameterAdjustment> Adjustments,
    decimal CurrentPerformanceScore,
    decimal ExpectedPerformanceScore,
    string PrimaryReason,
    IReadOnlyList<string> SupportingReasons,
    DateTime RecommendedAt
);

/// <summary>
/// Learning model metrics
/// </summary>
public record LearningModelMetrics(
    string ModelVersion,
    decimal PredictionAccuracy,
    decimal ParameterStability,
    int TrainingSamples,
    int ValidationSamples,
    DateTime LastTrainingDate,
    DateTime LastUpdateDate,
    IReadOnlyDictionary<string, decimal> FeatureImportances,
    ModelPerformanceMetrics Performance
);

/// <summary>
/// Model performance metrics
/// </summary>
public record ModelPerformanceMetrics(
    decimal MeanAbsoluteError,
    decimal RootMeanSquareError,
    decimal R2Score,
    decimal PredictionVariance,
    decimal CalibrationScore
);

/// <summary>
/// Validation sample for model testing
/// </summary>
public record ValidationSample(
    StrategyParameters Parameters,
    MarketConditions Conditions,
    decimal ActualOutcome,
    decimal PredictedOutcome,
    DateTime SampleDate
);

/// <summary>
/// Model validation metrics
/// </summary>
public record ModelValidationMetrics(
    decimal OverallAccuracy,
    decimal PrecisionScore,
    decimal RecallScore,
    decimal F1Score,
    decimal CalibrationError,
    IReadOnlyList<ValidationResult> DetailedResults,
    DateTime ValidatedAt
);

/// <summary>
/// Individual validation result
/// </summary>
public record ValidationResult(
    DateTime TestDate,
    decimal PredictedValue,
    decimal ActualValue,
    decimal AbsoluteError,
    decimal PercentageError,
    bool WithinTolerance
);

/// <summary>
/// Enums for adaptive learning
/// </summary>
public enum AdjustmentConfidence
{
    VeryLow,
    Low,
    Medium,
    High,
    VeryHigh
}

public enum PredictionConfidence
{
    VeryLow,
    Low,
    Medium,
    High,
    VeryHigh
}

public enum AdjustmentUrgency
{
    Low,
    Medium,
    High,
    Critical
}

// Using MarketRegime from SmaTrendFollower.Services namespace

public enum TimeOfDay
{
    PreMarket,
    Opening,
    MidMorning,
    Midday,
    Afternoon,
    Closing,
    AfterHours
}

/// <summary>
/// Learning algorithm types
/// </summary>
public enum LearningAlgorithm
{
    OnlineGradientDescent,
    AdaptiveRandomForest,
    BayesianOptimization,
    ReinforcementLearning,
    EnsembleMethod
}

/// <summary>
/// Feature engineering for market conditions
/// </summary>
public static class MarketConditionsExtensions
{
    /// <summary>
    /// Converts market conditions to feature vector for ML
    /// </summary>
    public static Dictionary<string, decimal> ToFeatureVector(this MarketConditions conditions)
    {
        return new Dictionary<string, decimal>
        {
            ["VIX"] = conditions.VIX,
            ["SPY_Price"] = conditions.SPYPrice,
            ["SPY_SMA50_Ratio"] = conditions.SPYPrice / conditions.SPY_SMA50,
            ["SPY_SMA200_Ratio"] = conditions.SPYPrice / conditions.SPY_SMA200,
            ["Market_Volatility"] = conditions.MarketVolatility,
            ["Sector_Rotation"] = conditions.SectorRotation,
            ["Dollar_Index"] = conditions.DollarIndex,
            ["Ten_Year_Yield"] = conditions.TenYearYield,
            ["Is_Trending_Up"] = conditions.Regime == MarketRegime.TrendingUp ? 1m : 0m,
            ["Is_Trending_Down"] = conditions.Regime == MarketRegime.TrendingDown ? 1m : 0m,
            ["Is_Sideways"] = conditions.Regime == MarketRegime.Sideways ? 1m : 0m,
            ["Is_Panic"] = conditions.Regime == MarketRegime.Panic ? 1m : 0m,
            ["Is_Opening"] = conditions.TimeOfDay == TimeOfDay.Opening ? 1m : 0m,
            ["Is_Closing"] = conditions.TimeOfDay == TimeOfDay.Closing ? 1m : 0m,
            ["Is_Monday"] = conditions.DayOfWeek == DayOfWeek.Monday ? 1m : 0m,
            ["Is_Friday"] = conditions.DayOfWeek == DayOfWeek.Friday ? 1m : 0m,
            ["Is_Earnings_Week"] = conditions.IsEarningsWeek ? 1m : 0m,
            ["Is_FOMC_Week"] = conditions.IsFOMCWeek ? 1m : 0m
        };
    }

    /// <summary>
    /// Converts strategy parameters to feature vector for ML
    /// </summary>
    public static Dictionary<string, decimal> ToFeatureVector(this StrategyParameters parameters)
    {
        return new Dictionary<string, decimal>
        {
            ["SMA50_Period"] = parameters.SMA50Period,
            ["SMA200_Period"] = parameters.SMA200Period,
            ["ATR_Multiplier"] = parameters.ATRMultiplier,
            ["Volatility_Threshold"] = parameters.VolatilityThreshold,
            ["Risk_Per_Trade"] = parameters.RiskPerTrade,
            ["Max_Position_Size"] = parameters.MaxPositionSize,
            ["Lookback_Period"] = parameters.LookbackPeriod,
            ["Momentum_Threshold"] = parameters.MomentumThreshold,
            ["Use_Trailing_Stop"] = parameters.UseTrailingStop ? 1m : 0m,
            ["Trailing_Stop_ATR"] = parameters.TrailingStopATR
        };
    }
}
