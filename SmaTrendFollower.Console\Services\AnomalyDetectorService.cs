using System.Collections.Concurrent;
using Microsoft.Extensions.Logging;
using Prometheus;
using StackExchange.Redis;
using SmaTrendFollower.Configuration;

namespace SmaTrendFollower.Services;

/// <summary>
/// Real-time anomaly detection service that monitors quote spreads and price returns
/// for unusual market behavior. Automatically halts trading for symbols showing
/// statistical anomalies (z-score > 3) to prevent execution during market disruptions.
/// </summary>
public sealed class AnomalyDetectorService : IDisposable
{
    private readonly IDatabase _redis;
    private readonly ILogger<AnomalyDetectorService> _logger;
    private readonly ConcurrentDictionary<string, SymbolAnomalyTracker> _trackers = new();
    private readonly SemaphoreSlim _cleanupSemaphore = new(1, 1);
    private readonly Timer _cleanupTimer;
    private bool _disposed;

    // Configuration constants
    private const int DefaultWindowSize = 200;
    private const double AnomalyThreshold = 3.0; // Z-score threshold for anomaly detection
    private const int HaltDurationMinutes = 5;
    private const int MaxTrackersPerSymbol = 1000; // Memory protection

    // Prometheus metrics
    private static readonly Counter AnomalyEvents =
        Metrics.CreateCounter("anomaly_events_total", "Total anomaly detections",
            new CounterConfiguration { LabelNames = new[] { "symbol", "type" } });

    private static readonly Gauge HaltedSymbols =
        Metrics.CreateGauge("anomaly_halted", "Trading halt active for symbol",
            new GaugeConfiguration { LabelNames = new[] { "symbol" } });

    private static readonly Histogram AnomalyZScores =
        Metrics.CreateHistogram("anomaly_z_scores", "Distribution of anomaly z-scores",
            new HistogramConfiguration 
            { 
                LabelNames = new[] { "symbol", "type" },
                Buckets = new[] { 1.0, 2.0, 3.0, 4.0, 5.0, 6.0, 8.0, 10.0 }
            });

    private static readonly Counter AnomalyChecks =
        Metrics.CreateCounter("anomaly_checks_total", "Total anomaly checks performed",
            new CounterConfiguration { LabelNames = new[] { "symbol", "type" } });

    public AnomalyDetectorService(
        IConnectionMultiplexer connectionMultiplexer,
        ILogger<AnomalyDetectorService> logger)
    {
        _redis = connectionMultiplexer?.GetDatabase() ?? throw new ArgumentNullException(nameof(connectionMultiplexer));
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));

        // Setup cleanup timer to run every 5 minutes
        _cleanupTimer = new Timer(CleanupExpiredTrackers, null, TimeSpan.FromMinutes(5), TimeSpan.FromMinutes(5));

        _logger.LogInformation("AnomalyDetectorService initialized with threshold {Threshold} and window size {WindowSize}",
            AnomalyThreshold, DefaultWindowSize);
    }

    /// <summary>
    /// Processes a quote update and checks for spread anomalies
    /// </summary>
    /// <param name="symbol">Trading symbol</param>
    /// <param name="bid">Bid price</param>
    /// <param name="ask">Ask price</param>
    /// <param name="timestamp">Quote timestamp</param>
    public void OnQuote(string symbol, decimal bid, decimal ask, DateTime timestamp)
    {
        if (string.IsNullOrEmpty(symbol) || bid <= 0 || ask <= 0 || ask <= bid)
            return;

        try
        {
            var tracker = GetOrCreateTracker(symbol);
            var midPrice = (bid + ask) / 2;
            var spreadBps = (double)((ask - bid) / midPrice * 10000); // Spread in basis points

            tracker.SpreadStats.Add(spreadBps);
            AnomalyChecks.WithLabels(symbol, "spread").Inc();

            // Check for spread anomaly
            if (tracker.SpreadStats.HasSufficientData)
            {
                var zScore = tracker.SpreadStats.CalculateZScore(spreadBps);
                AnomalyZScores.WithLabels(symbol, "spread").Observe(Math.Abs(zScore));

                if (Math.Abs(zScore) > AnomalyThreshold)
                {
                    _ = Task.Run(() => HaltTradingAsync(symbol, "spread", zScore, spreadBps));
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Error processing quote anomaly for {Symbol}", symbol);
        }
    }

    /// <summary>
    /// Processes a trade update and checks for price return anomalies
    /// </summary>
    /// <param name="symbol">Trading symbol</param>
    /// <param name="price">Trade price</param>
    /// <param name="timestamp">Trade timestamp</param>
    public void OnTrade(string symbol, decimal price, DateTime timestamp)
    {
        if (string.IsNullOrEmpty(symbol) || price <= 0)
            return;

        try
        {
            var tracker = GetOrCreateTracker(symbol);
            
            // Calculate return if we have previous price
            if (tracker.LastPrice > 0)
            {
                var returnPct = (double)((price / tracker.LastPrice) - 1) * 100; // Return in percentage
                tracker.ReturnStats.Add(returnPct);
                AnomalyChecks.WithLabels(symbol, "return").Inc();

                // Check for return anomaly
                if (tracker.ReturnStats.HasSufficientData)
                {
                    var zScore = tracker.ReturnStats.CalculateZScore(returnPct);
                    AnomalyZScores.WithLabels(symbol, "return").Observe(Math.Abs(zScore));

                    if (Math.Abs(zScore) > AnomalyThreshold)
                    {
                        _ = Task.Run(() => HaltTradingAsync(symbol, "return", zScore, returnPct));
                    }
                }
            }

            tracker.LastPrice = price;
            tracker.LastUpdate = timestamp;
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Error processing trade anomaly for {Symbol}", symbol);
        }
    }

    /// <summary>
    /// Checks if trading is currently halted for a symbol
    /// </summary>
    /// <param name="symbol">Trading symbol to check</param>
    /// <returns>True if trading is halted, false otherwise</returns>
    public async Task<bool> IsTradingHaltedAsync(string symbol)
    {
        if (string.IsNullOrEmpty(symbol))
            return false;

        try
        {
            var haltKey = $"halt:{symbol}";
            return await _redis.KeyExistsAsync(haltKey);
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Error checking halt status for {Symbol}", symbol);
            return false; // Default to allowing trading if Redis is unavailable
        }
    }

    /// <summary>
    /// Gets anomaly statistics for a symbol
    /// </summary>
    public AnomalyStats? GetAnomalyStats(string symbol)
    {
        if (!_trackers.TryGetValue(symbol, out var tracker))
            return null;

        return new AnomalyStats(
            Symbol: symbol,
            SpreadStats: tracker.SpreadStats.GetSnapshot(),
            ReturnStats: tracker.ReturnStats.GetSnapshot(),
            LastUpdate: tracker.LastUpdate,
            LastPrice: tracker.LastPrice
        );
    }

    private SymbolAnomalyTracker GetOrCreateTracker(string symbol)
    {
        return _trackers.GetOrAdd(symbol, _ => new SymbolAnomalyTracker(DefaultWindowSize));
    }

    private async Task HaltTradingAsync(string symbol, string anomalyType, double zScore, double value)
    {
        try
        {
            var haltKey = $"halt:{symbol}";
            await _redis.StringSetAsync(haltKey, "1", TimeSpan.FromMinutes(HaltDurationMinutes));

            // Update metrics
            HaltedSymbols.WithLabels(symbol).Set(1);
            AnomalyEvents.WithLabels(symbol, anomalyType).Inc();

            _logger.LogWarning(
                "Anomaly detected: {Type} for {Symbol}, z-score={ZScore:F2}, value={Value:F4} - trading halted for {Duration}m",
                anomalyType, symbol, zScore, value, HaltDurationMinutes);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to halt trading for {Symbol} after {Type} anomaly", symbol, anomalyType);
        }
    }

    private async void CleanupExpiredTrackers(object? state)
    {
        if (_disposed || !await _cleanupSemaphore.WaitAsync(100))
            return;

        try
        {
            var cutoffTime = DateTime.UtcNow.AddHours(-1);
            var expiredSymbols = _trackers
                .Where(kvp => kvp.Value.LastUpdate < cutoffTime)
                .Select(kvp => kvp.Key)
                .Take(100) // Limit cleanup batch size
                .ToList();

            foreach (var symbol in expiredSymbols)
            {
                if (_trackers.TryRemove(symbol, out _))
                {
                    _logger.LogDebug("Cleaned up expired anomaly tracker for {Symbol}", symbol);
                }
            }

            // Enforce memory limits
            if (_trackers.Count > MaxTrackersPerSymbol)
            {
                var oldestSymbols = _trackers
                    .OrderBy(kvp => kvp.Value.LastUpdate)
                    .Take(_trackers.Count - MaxTrackersPerSymbol)
                    .Select(kvp => kvp.Key)
                    .ToList();

                foreach (var symbol in oldestSymbols)
                {
                    _trackers.TryRemove(symbol, out _);
                }

                _logger.LogInformation("Enforced memory limit: removed {Count} oldest anomaly trackers", oldestSymbols.Count);
            }
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Error during anomaly tracker cleanup");
        }
        finally
        {
            _cleanupSemaphore.Release();
        }
    }

    public void Dispose()
    {
        if (_disposed)
            return;

        _disposed = true;
        _cleanupTimer?.Dispose();
        _cleanupSemaphore?.Dispose();
        _trackers.Clear();
    }
}

/// <summary>
/// Tracks anomaly detection statistics for a single symbol
/// </summary>
internal sealed class SymbolAnomalyTracker
{
    public RollingStats SpreadStats { get; }
    public RollingStats ReturnStats { get; }
    public decimal LastPrice { get; set; }
    public DateTime LastUpdate { get; set; } = DateTime.UtcNow;

    public SymbolAnomalyTracker(int windowSize)
    {
        SpreadStats = new RollingStats(windowSize);
        ReturnStats = new RollingStats(windowSize);
    }
}

/// <summary>
/// Immutable snapshot of anomaly statistics for a symbol
/// </summary>
public readonly record struct AnomalyStats(
    string Symbol,
    RollingStatsSnapshot SpreadStats,
    RollingStatsSnapshot ReturnStats,
    DateTime LastUpdate,
    decimal LastPrice
);
