using Microsoft.Extensions.Logging;
using StackExchange.Redis;
using System.Text.Json;
using MathNet.Numerics.LinearRegression;
using Quartz;
using SmaTrendFollower.Models;

namespace SmaTrendFollower.Services;

/// <summary>
/// Weekly trainer for synthetic VIX regression weights using machine learning
/// Uses MarketDataService for unified data retrieval with Polygon/Alpaca fallback
/// Implements Quartz.NET job for automated weekly training
/// </summary>
[DisallowConcurrentExecution]
public sealed class SyntheticVixTrainer : IJob, IDisposable
{
    private readonly IMarketDataService _marketDataService;
    private readonly IDatabase _redis;
    private readonly ILogger<SyntheticVixTrainer> _logger;
    private readonly SemaphoreSlim _trainingLock = new(1, 1);
    private bool _disposed;

    public SyntheticVixTrainer(
        IMarketDataService marketDataService,
        ConnectionMultiplexer connectionMultiplexer,
        ILogger<SyntheticVixTrainer> logger)
    {
        _marketDataService = marketDataService ?? throw new ArgumentNullException(nameof(marketDataService));
        _redis = connectionMultiplexer?.GetDatabase() ?? throw new ArgumentNullException(nameof(connectionMultiplexer));
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
    }

    /// <summary>
    /// Quartz.NET job execution entry point
    /// </summary>
    public async Task Execute(IJobExecutionContext context)
    {
        try
        {
            _logger.LogInformation("Starting weekly synthetic VIX regression training");
            await TrainAsync(context.CancellationToken);
            _logger.LogInformation("Weekly synthetic VIX regression training completed successfully");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during weekly synthetic VIX regression training");
            throw; // Re-throw to let Quartz handle the failure
        }
    }

    /// <summary>
    /// Trains regression weights using historical VIX and ETF data
    /// Uses 1-year lookback period for robust coefficient estimation
    /// </summary>
    public async Task TrainAsync(CancellationToken cancellationToken = default)
    {
        if (_disposed)
            throw new ObjectDisposedException(nameof(SyntheticVixTrainer));

        await _trainingLock.WaitAsync(cancellationToken);
        try
        {
            _logger.LogInformation("Training synthetic VIX regression weights using historical data");

            // Define training period (1 year lookback for robust estimation)
            var endDate = DateTime.UtcNow.Date;
            var startDate = endDate.AddYears(-1);

            _logger.LogDebug("Training period: {StartDate:yyyy-MM-dd} to {EndDate:yyyy-MM-dd}", startDate, endDate);

            // Fetch historical data for all symbols
            var symbols = new[] { "I:VIX", "VXX", "UVXY", "SVXY", "SPY" };
            var historicalData = new Dictionary<string, List<DailyBar>>();

            foreach (var symbol in symbols)
            {
                _logger.LogDebug("Fetching historical data for {Symbol}", symbol);
                var bars = await LoadDailyBarsAsync(symbol, startDate, endDate, cancellationToken);
                
                if (bars == null || bars.Count < 50) // Require minimum data points
                {
                    _logger.LogError("Insufficient historical data for {Symbol}: {Count} bars", symbol, bars?.Count ?? 0);
                    return;
                }

                historicalData[symbol] = bars;
                _logger.LogDebug("Loaded {Count} daily bars for {Symbol}", bars.Count, symbol);
            }

            // Align data by date (ensure all symbols have data for same dates)
            var alignedData = AlignHistoricalData(historicalData);
            if (alignedData.Count < 50)
            {
                _logger.LogError("Insufficient aligned data points: {Count}", alignedData.Count);
                return;
            }

            _logger.LogInformation("Training regression with {Count} aligned data points", alignedData.Count);

            // Prepare regression data
            var vixValues = alignedData.Select(d => (double)d.VixClose).ToArray();
            var features = alignedData.Select(d => new[]
            {
                (double)d.VxxClose,   // VXX coefficient
                (double)d.UvxyClose,  // UVXY coefficient  
                (double)d.SvxyClose,  // SVXY coefficient
                (double)d.SpyClose,   // SPY coefficient
                1.0                   // Intercept
            }).ToArray();

            // Train multiple regression model using QR decomposition
            var coefficients = MultipleRegression.QR(features, vixValues, intercept: false);

            // Calculate R-squared for model quality assessment
            var rSquared = CalculateRSquared(vixValues, features, coefficients);

            // Validate model quality
            if (rSquared < 0.5m)
            {
                _logger.LogWarning("Poor model quality (R²={RSquared:F3}), keeping existing weights", rSquared);
                return;
            }

            // Create and store new weights
            var weights = new SyntheticVixWeights
            {
                VxxCoefficient = (decimal)coefficients[0],
                UvxyCoefficient = (decimal)coefficients[1],
                SvxyCoefficient = (decimal)coefficients[2],
                SpyCoefficient = (decimal)coefficients[3],
                Intercept = (decimal)coefficients[4],
                TrainedAt = DateTime.UtcNow,
                RSquared = rSquared,
                SampleSize = alignedData.Count
            };

            await StoreTrainedWeightsAsync(weights);

            _logger.LogInformation("Regression training completed successfully: R²={RSquared:F3}, samples={SampleSize}",
                weights.RSquared, weights.SampleSize);
            _logger.LogDebug("Trained coefficients: VXX={VXX:F4}, UVXY={UVXY:F4}, SVXY={SVXY:F4}, SPY={SPY:F4}, Intercept={Intercept:F4}",
                weights.VxxCoefficient, weights.UvxyCoefficient, weights.SvxyCoefficient, weights.SpyCoefficient, weights.Intercept);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during regression training");
            throw;
        }
        finally
        {
            _trainingLock.Release();
        }
    }

    private async Task<List<DailyBar>?> LoadDailyBarsAsync(string symbol, DateTime startDate, DateTime endDate, CancellationToken cancellationToken)
    {
        try
        {
            _logger.LogDebug("Loading daily bars for {Symbol} using MarketDataService with Polygon/Alpaca fallback", symbol);

            // Use MarketDataService for unified data access with built-in fallback
            if (symbol.StartsWith("I:") || symbol == "VIX")
            {
                // Use index data method for VIX
                var indexBars = await _marketDataService.GetIndexBarsAsync(symbol, startDate, endDate);
                if (indexBars?.Any() == true)
                {
                    return indexBars.Select(bar => new DailyBar
                    {
                        Date = bar.TimeUtc.Date,
                        Close = bar.Close
                    }).OrderBy(b => b.Date).ToList();
                }
                else
                {
                    _logger.LogWarning("No index bars found for {Symbol}", symbol);
                    return null;
                }
            }
            else
            {
                // Use stock data method for ETFs (VXX, UVXY, SVXY, SPY)
                var stockBars = await _marketDataService.GetStockBarsAsync(symbol, startDate, endDate);
                if (stockBars?.Items?.Any() == true)
                {
                    return stockBars.Items.Select(bar => new DailyBar
                    {
                        Date = bar.TimeUtc.Date,
                        Close = bar.Close
                    }).OrderBy(b => b.Date).ToList();
                }
                else
                {
                    _logger.LogWarning("No stock bars found for {Symbol}", symbol);
                    return null;
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error loading daily bars for {Symbol} via MarketDataService", symbol);
            return null;
        }
    }

    private List<AlignedDailyData> AlignHistoricalData(Dictionary<string, List<DailyBar>> historicalData)
    {
        var alignedData = new List<AlignedDailyData>();

        // Get common dates across all symbols
        var allDates = historicalData.Values
            .SelectMany(bars => bars.Select(b => b.Date))
            .Distinct()
            .OrderBy(d => d)
            .ToList();

        foreach (var date in allDates)
        {
            // Check if all symbols have data for this date
            var vixBar = historicalData["I:VIX"].FirstOrDefault(b => b.Date == date);
            var vxxBar = historicalData["VXX"].FirstOrDefault(b => b.Date == date);
            var uvxyBar = historicalData["UVXY"].FirstOrDefault(b => b.Date == date);
            var svxyBar = historicalData["SVXY"].FirstOrDefault(b => b.Date == date);
            var spyBar = historicalData["SPY"].FirstOrDefault(b => b.Date == date);

            if (vixBar != null && vxxBar != null && uvxyBar != null && svxyBar != null && spyBar != null)
            {
                alignedData.Add(new AlignedDailyData
                {
                    Date = date,
                    VixClose = vixBar.Close,
                    VxxClose = vxxBar.Close,
                    UvxyClose = uvxyBar.Close,
                    SvxyClose = svxyBar.Close,
                    SpyClose = spyBar.Close
                });
            }
        }

        return alignedData;
    }

    private static decimal CalculateRSquared(double[] actual, double[][] features, double[] coefficients)
    {
        // Calculate predicted values
        var predicted = features.Select(f => f.Zip(coefficients).Sum(pair => pair.First * pair.Second)).ToArray();

        // Calculate R-squared
        var actualMean = actual.Average();
        var totalSumSquares = actual.Sum(y => Math.Pow(y - actualMean, 2));
        var residualSumSquares = actual.Zip(predicted).Sum(pair => Math.Pow(pair.First - pair.Second, 2));

        var rSquared = 1.0 - (residualSumSquares / totalSumSquares);
        return (decimal)Math.Max(0.0, rSquared); // Ensure non-negative
    }

    private async Task StoreTrainedWeightsAsync(SyntheticVixWeights weights)
    {
        try
        {
            var weightsJson = JsonSerializer.Serialize(weights);
            await _redis.StringSetAsync("vix:weights", weightsJson, TimeSpan.FromDays(30));

            // Store individual coefficients for backward compatibility
            await _redis.StringSetAsync("vix:weights:a", (double)weights.VxxCoefficient, TimeSpan.FromDays(30));
            await _redis.StringSetAsync("vix:weights:b", (double)weights.UvxyCoefficient, TimeSpan.FromDays(30));
            await _redis.StringSetAsync("vix:weights:c", (double)weights.SvxyCoefficient, TimeSpan.FromDays(30));
            await _redis.StringSetAsync("vix:weights:d", (double)weights.SpyCoefficient, TimeSpan.FromDays(30));
            await _redis.StringSetAsync("vix:weights:e", (double)weights.Intercept, TimeSpan.FromDays(30));

            // Store training metadata
            var metadata = new
            {
                TrainedAt = weights.TrainedAt,
                RSquared = weights.RSquared,
                SampleSize = weights.SampleSize,
                Version = "1.0"
            };

            await _redis.StringSetAsync("vix:weights:metadata", 
                JsonSerializer.Serialize(metadata), TimeSpan.FromDays(30));

            _logger.LogInformation("Trained regression weights stored successfully in Redis");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error storing trained weights in Redis");
            throw;
        }
    }

    public void Dispose()
    {
        if (!_disposed)
        {
            _trainingLock?.Dispose();
            _disposed = true;
        }
    }
}

/// <summary>
/// Daily bar data for training
/// </summary>
public record DailyBar
{
    public DateTime Date { get; init; }
    public decimal Close { get; init; }
}

/// <summary>
/// Aligned daily data across all symbols for regression training
/// </summary>
public record AlignedDailyData
{
    public DateTime Date { get; init; }
    public decimal VixClose { get; init; }
    public decimal VxxClose { get; init; }
    public decimal UvxyClose { get; init; }
    public decimal SvxyClose { get; init; }
    public decimal SpyClose { get; init; }
}
