using FluentAssertions;
using Microsoft.Extensions.Logging;
using NSubstitute;
using SmaTrendFollower.Configuration;
using SmaTrendFollower.Infrastructure.Security;
using Xunit;
using Xunit.Abstractions;

namespace SmaTrendFollower.Tests.Unit.Security;

/// <summary>
/// Tests for vault fallback behavior and secret provider selection
/// </summary>
public class VaultFallbackTests
{
    private readonly ITestOutputHelper _output;
    private readonly ILogger _logger;

    public VaultFallbackTests(ITestOutputHelper output)
    {
        _output = output;
        _logger = Substitute.For<ILogger>();
    }

    [Fact]
    public void CreateSecretProvider_ShouldReturnEnvProvider_WhenVaultAddrNotSet()
    {
        // Arrange
        var originalVaultAddr = Environment.GetEnvironmentVariable("VAULT_ADDR");
        var originalVaultToken = Environment.GetEnvironmentVariable("VAULT_TOKEN");

        try
        {
            Environment.SetEnvironmentVariable("VAULT_ADDR", null);
            Environment.SetEnvironmentVariable("VAULT_TOKEN", "test-token");

            // Act
            var provider = ServiceConfiguration.CreateSecretProvider(_logger);

            // Assert
            provider.Should().BeOfType<EnvSecretProvider>();
            provider.ProviderName.Should().Be("Environment Variables");
            provider.IsConfigured.Should().BeTrue();
        }
        finally
        {
            // Restore original values
            Environment.SetEnvironmentVariable("VAULT_ADDR", originalVaultAddr);
            Environment.SetEnvironmentVariable("VAULT_TOKEN", originalVaultToken);
        }
    }

    [Fact]
    public void CreateSecretProvider_ShouldReturnEnvProvider_WhenVaultTokenNotSet()
    {
        // Arrange
        var originalVaultAddr = Environment.GetEnvironmentVariable("VAULT_ADDR");
        var originalVaultToken = Environment.GetEnvironmentVariable("VAULT_TOKEN");

        try
        {
            Environment.SetEnvironmentVariable("VAULT_ADDR", "http://vault:8200");
            Environment.SetEnvironmentVariable("VAULT_TOKEN", null);

            // Act
            var provider = ServiceConfiguration.CreateSecretProvider(_logger);

            // Assert
            provider.Should().BeOfType<EnvSecretProvider>();
            provider.ProviderName.Should().Be("Environment Variables");
            provider.IsConfigured.Should().BeTrue();
        }
        finally
        {
            // Restore original values
            Environment.SetEnvironmentVariable("VAULT_ADDR", originalVaultAddr);
            Environment.SetEnvironmentVariable("VAULT_TOKEN", originalVaultToken);
        }
    }

    [Fact]
    public void CreateSecretProvider_ShouldReturnEnvProvider_WhenBothVaultVarsEmpty()
    {
        // Arrange
        var originalVaultAddr = Environment.GetEnvironmentVariable("VAULT_ADDR");
        var originalVaultToken = Environment.GetEnvironmentVariable("VAULT_TOKEN");

        try
        {
            Environment.SetEnvironmentVariable("VAULT_ADDR", "");
            Environment.SetEnvironmentVariable("VAULT_TOKEN", "");

            // Act
            var provider = ServiceConfiguration.CreateSecretProvider(_logger);

            // Assert
            provider.Should().BeOfType<EnvSecretProvider>();
            provider.ProviderName.Should().Be("Environment Variables");
            provider.IsConfigured.Should().BeTrue();
        }
        finally
        {
            // Restore original values
            Environment.SetEnvironmentVariable("VAULT_ADDR", originalVaultAddr);
            Environment.SetEnvironmentVariable("VAULT_TOKEN", originalVaultToken);
        }
    }

    [Fact]
    public void CreateSecretProvider_ShouldFallbackToEnvProvider_WhenVaultInitializationFails()
    {
        // Arrange
        var originalVaultAddr = Environment.GetEnvironmentVariable("VAULT_ADDR");
        var originalVaultToken = Environment.GetEnvironmentVariable("VAULT_TOKEN");

        try
        {
            // Set invalid vault configuration that will cause initialization to fail
            Environment.SetEnvironmentVariable("VAULT_ADDR", "invalid-url");
            Environment.SetEnvironmentVariable("VAULT_TOKEN", "invalid-token");

            // Act
            var provider = ServiceConfiguration.CreateSecretProvider(_logger);

            // Assert
            provider.Should().BeOfType<EnvSecretProvider>();
            provider.ProviderName.Should().Be("Environment Variables");
            provider.IsConfigured.Should().BeTrue();

            _output.WriteLine($"Provider type: {provider.GetType().Name}");
            _output.WriteLine($"Provider name: {provider.ProviderName}");
        }
        finally
        {
            // Restore original values
            Environment.SetEnvironmentVariable("VAULT_ADDR", originalVaultAddr);
            Environment.SetEnvironmentVariable("VAULT_TOKEN", originalVaultToken);
        }
    }

    [Fact]
    public void CreateSecretProvider_ShouldAttemptVaultProvider_WhenBothVaultVarsSet()
    {
        // Arrange
        var originalVaultAddr = Environment.GetEnvironmentVariable("VAULT_ADDR");
        var originalVaultToken = Environment.GetEnvironmentVariable("VAULT_TOKEN");

        try
        {
            Environment.SetEnvironmentVariable("VAULT_ADDR", "http://vault:8200");
            Environment.SetEnvironmentVariable("VAULT_TOKEN", "hvs.test-token");

            // Act
            var provider = ServiceConfiguration.CreateSecretProvider(_logger);

            // Assert
            // Since we don't have a real Vault instance, it should fallback to EnvSecretProvider
            // In a real environment with Vault available, this would return VaultSecretProvider
            provider.Should().NotBeNull();
            provider.IsConfigured.Should().BeTrue();

            _output.WriteLine($"Provider type: {provider.GetType().Name}");
            _output.WriteLine($"Provider name: {provider.ProviderName}");
        }
        finally
        {
            // Restore original values
            Environment.SetEnvironmentVariable("VAULT_ADDR", originalVaultAddr);
            Environment.SetEnvironmentVariable("VAULT_TOKEN", originalVaultToken);
        }
    }

    [Fact]
    public void SecretProviderIntegration_ShouldWorkWithCommonSecrets()
    {
        // Arrange
        var testSecrets = new Dictionary<string, string>
        {
            ["TEST_POLYGON_API_KEY"] = "test-polygon-key",
            ["TEST_ALPACA_KEY_ID"] = "test-alpaca-key",
            ["TEST_ALPACA_SECRET"] = "test-alpaca-secret",
            ["TEST_DISCORD_BOT_TOKEN"] = "test-discord-token"
        };

        // Set up test environment variables
        foreach (var (key, value) in testSecrets)
        {
            Environment.SetEnvironmentVariable(key, value);
        }

        try
        {
            var provider = new EnvSecretProvider();

            // Act & Assert
            foreach (var (key, expectedValue) in testSecrets)
            {
                // Test Get method
                var actualValue = provider.Get(key);
                actualValue.Should().Be(expectedValue);

                // Test TryGet method
                var success = provider.TryGet(key, out var tryGetValue);
                success.Should().BeTrue();
                tryGetValue.Should().Be(expectedValue);

                // Test Exists method
                var exists = provider.Exists(key);
                exists.Should().BeTrue();
            }

            _output.WriteLine($"Successfully tested {testSecrets.Count} secrets with {provider.ProviderName}");
        }
        finally
        {
            // Cleanup test environment variables
            foreach (var key in testSecrets.Keys)
            {
                Environment.SetEnvironmentVariable(key, null);
            }
        }
    }

    [Fact]
    public void SecretProvider_ShouldHandleNonExistentSecrets_Gracefully()
    {
        // Arrange
        var provider = new EnvSecretProvider();
        var nonExistentKey = "DEFINITELY_NONEXISTENT_SECRET_KEY_12345";

        // Act & Assert
        // Get should throw
        Assert.Throws<KeyNotFoundException>(() => provider.Get(nonExistentKey));

        // TryGet should return false
        var success = provider.TryGet(nonExistentKey, out var value);
        success.Should().BeFalse();
        value.Should().BeNull();

        // Exists should return false
        var exists = provider.Exists(nonExistentKey);
        exists.Should().BeFalse();
    }
}
