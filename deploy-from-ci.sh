#!/bin/bash
# Deploy SmaTrendFollower from CI artifacts
# This script downloads and deploys the latest build from GitHub Actions

set -e

# Default values
DEPLOY_PATH="${DEPLOY_PATH:-/opt/smatrendfollower}"
GITHUB_TOKEN="${GITHUB_TOKEN}"
REPOSITORY="${REPOSITORY:-patco1/SmaTrendFollower}"
BACKUP="${BACKUP:-true}"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

echo -e "${GREEN}SmaTrendFollower CI Deployment Script${NC}"
echo -e "${GREEN}=====================================${NC}"

# Validate parameters
if [ -z "$GITHUB_TOKEN" ]; then
    echo -e "${RED}Error: GitHub token is required. Set GITHUB_TOKEN environment variable.${NC}"
    exit 1
fi

# Create deployment directory if it doesn't exist
if [ ! -d "$DEPLOY_PATH" ]; then
    echo -e "${YELLOW}Creating deployment directory: $DEPLOY_PATH${NC}"
    sudo mkdir -p "$DEPLOY_PATH"
fi

# Backup existing deployment if requested
if [ "$BACKUP" = "true" ] && [ -f "$DEPLOY_PATH/SmaTrendFollower.Console" ]; then
    BACKUP_PATH="$DEPLOY_PATH/backup-$(date +%Y%m%d-%H%M%S)"
    echo -e "${YELLOW}Creating backup at: $BACKUP_PATH${NC}"
    sudo mkdir -p "$BACKUP_PATH"
    sudo cp -r "$DEPLOY_PATH"/* "$BACKUP_PATH"/ 2>/dev/null || true
    sudo find "$BACKUP_PATH" -name "backup-*" -type d -exec rm -rf {} + 2>/dev/null || true
fi

# Get latest successful workflow run
echo -e "${CYAN}Fetching latest successful build...${NC}"
RUNS_URL="https://api.github.com/repos/$REPOSITORY/actions/runs?status=success&per_page=1"

LATEST_RUN=$(curl -s -H "Authorization: token $GITHUB_TOKEN" \
                  -H "Accept: application/vnd.github.v3+json" \
                  "$RUNS_URL")

RUN_ID=$(echo "$LATEST_RUN" | grep -o '"id":[0-9]*' | head -1 | cut -d':' -f2)

if [ -z "$RUN_ID" ]; then
    echo -e "${RED}Error: No successful workflow runs found.${NC}"
    exit 1
fi

echo -e "${GREEN}Latest successful run ID: $RUN_ID${NC}"

# Get artifacts for the run
ARTIFACTS_URL="https://api.github.com/repos/$REPOSITORY/actions/runs/$RUN_ID/artifacts"
ARTIFACTS=$(curl -s -H "Authorization: token $GITHUB_TOKEN" \
                 -H "Accept: application/vnd.github.v3+json" \
                 "$ARTIFACTS_URL")

DOWNLOAD_URL=$(echo "$ARTIFACTS" | grep -A 10 '"name":"bot-build"' | grep '"archive_download_url"' | cut -d'"' -f4)

if [ -z "$DOWNLOAD_URL" ]; then
    echo -e "${RED}Error: Build artifact 'bot-build' not found in latest run.${NC}"
    exit 1
fi

# Download artifact
echo -e "${CYAN}Downloading build artifact...${NC}"
ARTIFACT_PATH="/tmp/sma-build-$RUN_ID.zip"

curl -L -H "Authorization: token $GITHUB_TOKEN" \
     -H "Accept: application/vnd.github.v3+json" \
     -o "$ARTIFACT_PATH" \
     "$DOWNLOAD_URL"

echo -e "${GREEN}Downloaded to: $ARTIFACT_PATH${NC}"

# Extract artifact
echo -e "${CYAN}Extracting build to deployment directory...${NC}"
sudo unzip -o "$ARTIFACT_PATH" -d "$DEPLOY_PATH"

# Make executable
sudo chmod +x "$DEPLOY_PATH/SmaTrendFollower.Console"

# Clean up
rm -f "$ARTIFACT_PATH"

echo -e "${GREEN}Deployment completed successfully!${NC}"
echo -e "${CYAN}Application deployed to: $DEPLOY_PATH${NC}"
echo -e "${CYAN}Run with: $DEPLOY_PATH/SmaTrendFollower.Console${NC}"
