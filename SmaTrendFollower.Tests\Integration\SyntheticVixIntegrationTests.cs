using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Xunit;
using FluentAssertions;
using StackExchange.Redis;
using SmaTrendFollower.Services;
using SmaTrendFollower.Configuration;

namespace SmaTrendFollower.Tests.Integration;

/// <summary>
/// Integration tests for the SyntheticVixService and SyntheticVixTrainer
/// Tests the complete workflow from training to estimation
/// </summary>
public class SyntheticVixIntegrationTests : IDisposable
{
    private readonly ServiceProvider _serviceProvider;
    private readonly IConfiguration _configuration;

    public SyntheticVixIntegrationTests()
    {
        // Setup configuration
        var configBuilder = new ConfigurationBuilder()
            .AddInMemoryCollection(new Dictionary<string, string?>
            {
                ["POLYGON_API_KEY"] = "test-api-key",
                ["ALPACA_API_KEY"] = "test-alpaca-key",
                ["ALPACA_SECRET_KEY"] = "test-alpaca-secret",
                ["REDIS_CONNECTION"] = "localhost:6379"
            });

        _configuration = configBuilder.Build();

        // Setup services
        var services = new ServiceCollection();
        
        // Add logging
        services.AddLogging(builder => builder.AddConsole().SetMinimumLevel(LogLevel.Debug));
        
        // Add configuration
        services.AddSingleton(_configuration);
        
        // Add HTTP client factory
        services.AddHttpClient();
        
        // Add Redis (mock for testing)
        services.AddSingleton<ConnectionMultiplexer>(provider =>
        {
            // For integration tests, we'd normally use a test Redis instance
            // For now, we'll skip Redis-dependent tests
            throw new NotImplementedException("Redis not available in test environment");
        });

        // Add our services
        services.AddSingleton<ISyntheticVixService, SyntheticVixService>();
        services.AddSingleton<SyntheticVixTrainer>();

        _serviceProvider = services.BuildServiceProvider();
    }

    [Fact]
    public void SyntheticVixService_ShouldBeRegistered()
    {
        // Act
        var service = _serviceProvider.GetService<ISyntheticVixService>();

        // Assert
        service.Should().NotBeNull();
        service.Should().BeOfType<SyntheticVixService>();
    }

    [Fact]
    public void SyntheticVixTrainer_ShouldBeRegistered()
    {
        // Act
        var trainer = _serviceProvider.GetService<SyntheticVixTrainer>();

        // Assert
        trainer.Should().NotBeNull();
        trainer.Should().BeOfType<SyntheticVixTrainer>();
    }

    [Fact]
    public void ServiceConfiguration_ShouldRegisterSyntheticVixServices()
    {
        // Arrange
        var services = new ServiceCollection();
        services.AddLogging();
        services.AddHttpClient();
        
        // Mock Redis
        services.AddSingleton<ConnectionMultiplexer>(provider =>
        {
            throw new NotImplementedException("Redis not available in test environment");
        });

        // Act
        services.AddMarketDataServices();

        // Assert
        var serviceProvider = services.BuildServiceProvider();
        
        // Verify services are registered (will throw if Redis is accessed)
        var syntheticVixServiceDescriptor = services.FirstOrDefault(s => s.ServiceType == typeof(ISyntheticVixService));
        var trainerServiceDescriptor = services.FirstOrDefault(s => s.ServiceType == typeof(SyntheticVixTrainer));

        syntheticVixServiceDescriptor.Should().NotBeNull();
        syntheticVixServiceDescriptor!.ImplementationType.Should().Be(typeof(SyntheticVixService));
        syntheticVixServiceDescriptor.Lifetime.Should().Be(ServiceLifetime.Singleton);

        trainerServiceDescriptor.Should().NotBeNull();
        trainerServiceDescriptor!.ImplementationType.Should().Be(typeof(SyntheticVixTrainer));
        trainerServiceDescriptor.Lifetime.Should().Be(ServiceLifetime.Singleton);
    }

    [Fact]
    public void SyntheticVixWeights_ShouldSerializeCorrectly()
    {
        // Arrange
        var weights = new SyntheticVixWeights
        {
            VxxCoefficient = 1.45m,
            UvxyCoefficient = 0.30m,
            SvxyCoefficient = -0.20m,
            SpyCoefficient = -0.05m,
            Intercept = 8.2m,
            TrainedAt = DateTime.UtcNow,
            RSquared = 0.85m,
            SampleSize = 252
        };

        // Act
        var json = System.Text.Json.JsonSerializer.Serialize(weights);
        var deserialized = System.Text.Json.JsonSerializer.Deserialize<SyntheticVixWeights>(json);

        // Assert
        deserialized.Should().NotBeNull();
        deserialized!.VxxCoefficient.Should().Be(weights.VxxCoefficient);
        deserialized.UvxyCoefficient.Should().Be(weights.UvxyCoefficient);
        deserialized.SvxyCoefficient.Should().Be(weights.SvxyCoefficient);
        deserialized.SpyCoefficient.Should().Be(weights.SpyCoefficient);
        deserialized.Intercept.Should().Be(weights.Intercept);
        deserialized.RSquared.Should().Be(weights.RSquared);
        deserialized.SampleSize.Should().Be(weights.SampleSize);
    }

    [Fact]
    public void EtfPriceSnapshot_ShouldCalculateDataAgeCorrectly()
    {
        // Arrange
        var timestamp = DateTime.UtcNow.AddMinutes(-10);
        var snapshot = new EtfPriceSnapshot
        {
            VxxPrice = 25.50m,
            UvxyPrice = 12.75m,
            SvxyPrice = 45.20m,
            SpyPrice = 485.30m,
            Timestamp = timestamp,
            DataAge = DateTime.UtcNow - timestamp
        };

        // Assert
        snapshot.DataAge.Should().BeCloseTo(TimeSpan.FromMinutes(10), TimeSpan.FromSeconds(1));
        snapshot.VxxPrice.Should().Be(25.50m);
        snapshot.UvxyPrice.Should().Be(12.75m);
        snapshot.SvxyPrice.Should().Be(45.20m);
        snapshot.SpyPrice.Should().Be(485.30m);
    }

    [Fact]
    public void DailyBar_ShouldStoreDataCorrectly()
    {
        // Arrange
        var date = DateTime.UtcNow.Date;
        var close = 123.45m;

        // Act
        var bar = new DailyBar
        {
            Date = date,
            Close = close
        };

        // Assert
        bar.Date.Should().Be(date);
        bar.Close.Should().Be(close);
    }

    [Fact]
    public void AlignedDailyData_ShouldStoreAllSymbolData()
    {
        // Arrange
        var date = DateTime.UtcNow.Date;
        var data = new AlignedDailyData
        {
            Date = date,
            VixClose = 20.5m,
            VxxClose = 25.3m,
            UvxyClose = 12.8m,
            SvxyClose = 45.1m,
            SpyClose = 485.2m
        };

        // Assert
        data.Date.Should().Be(date);
        data.VixClose.Should().Be(20.5m);
        data.VxxClose.Should().Be(25.3m);
        data.UvxyClose.Should().Be(12.8m);
        data.SvxyClose.Should().Be(45.1m);
        data.SpyClose.Should().Be(485.2m);
    }

    public void Dispose()
    {
        _serviceProvider?.Dispose();
    }
}
