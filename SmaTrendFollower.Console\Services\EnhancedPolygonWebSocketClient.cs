using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using SmaTrendFollower.Models;
using System.Collections.Concurrent;
using System.Net.WebSockets;
using System.Text;
using System.Text.Json;

namespace SmaTrendFollower.Services;

/// <summary>
/// Enhanced Polygon WebSocket client implementation for Developer subscription
/// Supports unlimited streaming with multiple connection management
/// </summary>
public sealed class EnhancedPolygonWebSocketClient : IEnhancedPolygonWebSocketClient, IDisposable
{
    private readonly IConfiguration _configuration;
    private readonly ILogger<EnhancedPolygonWebSocketClient> _logger;
    private readonly PolygonConnectionConfiguration _config;
    
    private readonly ConcurrentDictionary<string, ClientWebSocket> _connections = new();
    private readonly ConcurrentDictionary<string, PolygonDataTypes> _subscriptions = new();
    private readonly ConcurrentDictionary<string, Task> _receiveTasks = new();
    private readonly ConcurrentDictionary<string, CancellationTokenSource> _cancellationTokens = new();
    
    private PolygonConnectionStatus _connectionStatus = PolygonConnectionStatus.Disconnected;
    private PerformanceMetrics _performanceMetrics = new();
    private ConnectionQualityMetrics _connectionQuality = new();
    private readonly SemaphoreSlim _connectionSemaphore = new(1, 1);
    private Timer? _metricsTimer;
    private Timer? _healthCheckTimer;
    private bool _disposed;

    // Connection URLs for different asset classes
    private const string StocksStreamUrl = "wss://socket.polygon.io/stocks";
    private const string OptionsStreamUrl = "wss://socket.polygon.io/options";
    private const string ForexStreamUrl = "wss://socket.polygon.io/forex";
    private const string CryptoStreamUrl = "wss://socket.polygon.io/crypto";
    private const string IndicesStreamUrl = "wss://socket.polygon.io/indices";

    public EnhancedPolygonWebSocketClient(
        IConfiguration configuration,
        ILogger<EnhancedPolygonWebSocketClient> logger)
    {
        _configuration = configuration ?? throw new ArgumentNullException(nameof(configuration));
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        
        _config = new PolygonConnectionConfiguration();
        configuration.GetSection("PolygonWebSocket").Bind(_config);
        
        InitializeMetrics();
    }

    // === Events from IPolygonWebSocketClient ===
    
    public event EventHandler<PolygonConnectionStatusEventArgs>? ConnectionStatusChanged;
    public event EventHandler<PolygonIndexUpdateEventArgs>? IndexUpdated;
    public event EventHandler<PolygonTradeUpdateEventArgs>? TradeUpdated;
    public event EventHandler<PolygonQuoteUpdateEventArgs>? QuoteUpdated;
    public event EventHandler<PolygonAggregateUpdateEventArgs>? AggregateUpdated;

#pragma warning disable CS0067 // Event is never used - reserved for future implementation
    public event EventHandler<PolygonErrorEventArgs>? ErrorOccurred;

    // === Enhanced Events ===

    public event EventHandler<PolygonOptionsUpdateEventArgs>? OptionsUpdated;
    public event EventHandler<PolygonForexUpdateEventArgs>? ForexUpdated;
    public event EventHandler<PolygonCryptoUpdateEventArgs>? CryptoUpdated;
#pragma warning restore CS0067
    public event EventHandler<ConnectionMetricsEventArgs>? MetricsUpdated;

    // === Properties ===
    
    public PolygonConnectionStatus ConnectionStatus => _connectionStatus;
    public int ActiveConnections => _connections.Count(kvp => kvp.Value.State == WebSocketState.Open);
    public double MessageProcessingRate => _performanceMetrics.MessagesPerSecond;
    public long TotalMessagesReceived => _performanceMetrics.MessagesReceived;
    public ConnectionQualityMetrics ConnectionQuality => _connectionQuality;

    // === Connection Management ===

    public async Task ConnectAsync(CancellationToken cancellationToken = default)
    {
        await ConnectAsync(_config, cancellationToken);
    }

    public async Task ConnectAsync(PolygonConnectionConfiguration config, CancellationToken cancellationToken = default)
    {
        await _connectionSemaphore.WaitAsync(cancellationToken);
        try
        {
            if (_connectionStatus == PolygonConnectionStatus.Connected || _connectionStatus == PolygonConnectionStatus.Connecting)
                return;

            SetConnectionStatus(PolygonConnectionStatus.Connecting, "Connecting to Polygon WebSocket...");
            _logger.LogInformation("Starting enhanced Polygon WebSocket connections...");

            // Create connections for different asset classes
            var connectionTasks = new List<Task>
            {
                CreateConnectionAsync("stocks", StocksStreamUrl, cancellationToken),
                CreateConnectionAsync("indices", IndicesStreamUrl, cancellationToken)
            };

            // Add optional connections based on configuration
            if (config.MaxConnections > 2)
            {
                connectionTasks.Add(CreateConnectionAsync("options", OptionsStreamUrl, cancellationToken));
            }
            if (config.MaxConnections > 3)
            {
                connectionTasks.Add(CreateConnectionAsync("forex", ForexStreamUrl, cancellationToken));
                connectionTasks.Add(CreateConnectionAsync("crypto", CryptoStreamUrl, cancellationToken));
            }

            await Task.WhenAll(connectionTasks);

            SetConnectionStatus(PolygonConnectionStatus.Connected, "Connected to Polygon WebSocket");
            
            // Start monitoring timers
            StartMonitoring();
            
            _logger.LogInformation("Enhanced Polygon WebSocket client connected with {Count} connections", ActiveConnections);
        }
        catch (Exception ex)
        {
            SetConnectionStatus(PolygonConnectionStatus.Error, "Failed to connect", ex);
            _logger.LogError(ex, "Failed to connect enhanced Polygon WebSocket client");
            throw;
        }
        finally
        {
            _connectionSemaphore.Release();
        }
    }

    public async Task DisconnectAsync(CancellationToken cancellationToken = default)
    {
        await _connectionSemaphore.WaitAsync(cancellationToken);
        try
        {
            SetConnectionStatus(PolygonConnectionStatus.Disconnected, "Disconnecting...");
            _logger.LogInformation("Disconnecting enhanced Polygon WebSocket client...");

            // Stop monitoring
            StopMonitoring();

            // Cancel all receive tasks
            foreach (var cts in _cancellationTokens.Values)
            {
                cts.Cancel();
            }

            // Close all connections
            var disconnectTasks = _connections.Values.Select(async ws =>
            {
                try
                {
                    if (ws.State == WebSocketState.Open)
                    {
                        await ws.CloseAsync(WebSocketCloseStatus.NormalClosure, "Client disconnect", cancellationToken);
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogWarning(ex, "Error closing WebSocket connection");
                }
                finally
                {
                    ws.Dispose();
                }
            });

            await Task.WhenAll(disconnectTasks);

            // Wait for receive tasks to complete
            await Task.WhenAll(_receiveTasks.Values);

            // Clear collections
            _connections.Clear();
            _receiveTasks.Clear();
            _cancellationTokens.Clear();

            SetConnectionStatus(PolygonConnectionStatus.Disconnected, "Disconnected");
            _logger.LogInformation("Enhanced Polygon WebSocket client disconnected");
        }
        finally
        {
            _connectionSemaphore.Release();
        }
    }

    public async Task ForceReconnectAsync(CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("Force reconnecting enhanced Polygon WebSocket client...");
        await DisconnectAsync(cancellationToken);
        await Task.Delay(1000, cancellationToken); // Brief delay
        await ConnectAsync(cancellationToken);
    }

    // === Enhanced Subscription Management ===

    public async Task SubscribeToMultipleDataTypesAsync(string symbol, PolygonDataTypes dataTypes, CancellationToken cancellationToken = default)
    {
        var tasks = new List<Task>();

        if (dataTypes.HasFlag(PolygonDataTypes.Trades))
            tasks.Add(SubscribeToTradeUpdatesAsync(new[] { symbol }, cancellationToken));
        
        if (dataTypes.HasFlag(PolygonDataTypes.Quotes))
            tasks.Add(SubscribeToQuoteUpdatesAsync(new[] { symbol }, cancellationToken));
        
        if (dataTypes.HasFlag(PolygonDataTypes.Aggregates))
            tasks.Add(SubscribeToAggregateUpdatesAsync(new[] { symbol }, cancellationToken));
        
        if (dataTypes.HasFlag(PolygonDataTypes.IndexValues))
            tasks.Add(SubscribeToIndexUpdatesAsync(new[] { symbol }, cancellationToken));
        
        if (dataTypes.HasFlag(PolygonDataTypes.Options))
            tasks.Add(SubscribeToOptionsUpdatesAsync(new[] { symbol }, cancellationToken));

        await Task.WhenAll(tasks);
        _subscriptions.AddOrUpdate(symbol, dataTypes, (_, existing) => existing | dataTypes);
    }

    public async Task BulkSubscribeAsync(IDictionary<string, PolygonDataTypes> subscriptions, CancellationToken cancellationToken = default)
    {
        var tasks = subscriptions.Select(kvp => SubscribeToMultipleDataTypesAsync(kvp.Key, kvp.Value, cancellationToken));
        await Task.WhenAll(tasks);
    }

    public PolygonSubscriptionStatus GetSubscriptionStatus(string symbol)
    {
        return _subscriptions.ContainsKey(symbol) ? PolygonSubscriptionStatus.Subscribed : PolygonSubscriptionStatus.NotSubscribed;
    }

    public IReadOnlyDictionary<string, PolygonDataTypes> GetActiveSubscriptions()
    {
        return _subscriptions.ToDictionary(kvp => kvp.Key, kvp => kvp.Value);
    }

    // === Enhanced Subscription Methods ===

    public async Task SubscribeToOptionsUpdatesAsync(IEnumerable<string> optionSymbols, CancellationToken cancellationToken = default)
    {
        if (!_connections.TryGetValue("options", out var optionsConnection) || optionsConnection.State != WebSocketState.Open)
        {
            _logger.LogWarning("Options connection not available for subscription");
            return;
        }

        var symbols = optionSymbols.ToList();
        _logger.LogInformation("Subscribing to options updates for {Count} symbols", symbols.Count);

        var subscribeMessage = new
        {
            action = "subscribe",
            @params = string.Join(",", symbols.Select(s => $"T.{s}"))
        };

        await SendMessageAsync(optionsConnection, subscribeMessage, cancellationToken);
    }

    public async Task SubscribeToForexUpdatesAsync(IEnumerable<string> forexPairs, CancellationToken cancellationToken = default)
    {
        if (!_connections.TryGetValue("forex", out var forexConnection) || forexConnection.State != WebSocketState.Open)
        {
            _logger.LogWarning("Forex connection not available for subscription");
            return;
        }

        var pairs = forexPairs.ToList();
        _logger.LogInformation("Subscribing to forex updates for {Count} pairs", pairs.Count);

        var subscribeMessage = new
        {
            action = "subscribe",
            @params = string.Join(",", pairs.Select(p => $"C.{p}"))
        };

        await SendMessageAsync(forexConnection, subscribeMessage, cancellationToken);
    }

    public async Task SubscribeToCryptoUpdatesAsync(IEnumerable<string> cryptoPairs, CancellationToken cancellationToken = default)
    {
        if (!_connections.TryGetValue("crypto", out var cryptoConnection) || cryptoConnection.State != WebSocketState.Open)
        {
            _logger.LogWarning("Crypto connection not available for subscription");
            return;
        }

        var pairs = cryptoPairs.ToList();
        _logger.LogInformation("Subscribing to crypto updates for {Count} pairs", pairs.Count);

        var subscribeMessage = new
        {
            action = "subscribe",
            @params = string.Join(",", pairs.Select(p => $"XT.{p}"))
        };

        await SendMessageAsync(cryptoConnection, subscribeMessage, cancellationToken);
    }

    // === Standard IPolygonWebSocketClient Methods ===

    public async Task SubscribeToIndexUpdatesAsync(IEnumerable<string> indexSymbols, CancellationToken cancellationToken = default)
    {
        if (!_connections.TryGetValue("indices", out var indicesConnection) || indicesConnection.State != WebSocketState.Open)
        {
            _logger.LogWarning("Indices connection not available for subscription");
            return;
        }

        var symbols = indexSymbols.ToList();
        _logger.LogInformation("Subscribing to index updates for {Count} symbols", symbols.Count);

        var subscribeMessage = new
        {
            action = "subscribe",
            @params = string.Join(",", symbols.Select(s => $"V.{s}"))
        };

        await SendMessageAsync(indicesConnection, subscribeMessage, cancellationToken);
    }

    public async Task SubscribeToTradeUpdatesAsync(IEnumerable<string> stockSymbols, CancellationToken cancellationToken = default)
    {
        if (!_connections.TryGetValue("stocks", out var stocksConnection) || stocksConnection.State != WebSocketState.Open)
        {
            _logger.LogWarning("Stocks connection not available for subscription");
            return;
        }

        var symbols = stockSymbols.ToList();
        _logger.LogInformation("Subscribing to trade updates for {Count} symbols", symbols.Count);

        var subscribeMessage = new
        {
            action = "subscribe",
            @params = string.Join(",", symbols.Select(s => $"T.{s}"))
        };

        await SendMessageAsync(stocksConnection, subscribeMessage, cancellationToken);
    }

    public async Task SubscribeToQuoteUpdatesAsync(IEnumerable<string> stockSymbols, CancellationToken cancellationToken = default)
    {
        if (!_connections.TryGetValue("stocks", out var stocksConnection) || stocksConnection.State != WebSocketState.Open)
        {
            _logger.LogWarning("Stocks connection not available for subscription");
            return;
        }

        var symbols = stockSymbols.ToList();
        _logger.LogInformation("Subscribing to quote updates for {Count} symbols", symbols.Count);

        var subscribeMessage = new
        {
            action = "subscribe",
            @params = string.Join(",", symbols.Select(s => $"Q.{s}"))
        };

        await SendMessageAsync(stocksConnection, subscribeMessage, cancellationToken);
    }

    public async Task SubscribeToAggregateUpdatesAsync(IEnumerable<string> stockSymbols, CancellationToken cancellationToken = default)
    {
        if (!_connections.TryGetValue("stocks", out var stocksConnection) || stocksConnection.State != WebSocketState.Open)
        {
            _logger.LogWarning("Stocks connection not available for subscription");
            return;
        }

        var symbols = stockSymbols.ToList();
        _logger.LogInformation("Subscribing to aggregate updates for {Count} symbols", symbols.Count);

        var subscribeMessage = new
        {
            action = "subscribe",
            @params = string.Join(",", symbols.Select(s => $"A.{s}"))
        };

        await SendMessageAsync(stocksConnection, subscribeMessage, cancellationToken);
    }

    public async Task UnsubscribeFromIndexUpdatesAsync(IEnumerable<string> indexSymbols, CancellationToken cancellationToken = default)
    {
        // Implementation would send unsubscribe messages
        await Task.CompletedTask;
    }

    public async Task UnsubscribeAllAsync(CancellationToken cancellationToken = default)
    {
        _subscriptions.Clear();
        
        var unsubscribeTasks = _connections.Values.Select(async connection =>
        {
            try
            {
                var unsubscribeMessage = new { action = "unsubscribe", @params = "*" };
                await SendMessageAsync(connection, unsubscribeMessage, cancellationToken);
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Error unsubscribing from connection");
            }
        });

        await Task.WhenAll(unsubscribeTasks);
    }

    // === Performance Monitoring ===

    public PerformanceMetrics GetPerformanceMetrics()
    {
        return new PerformanceMetrics
        {
            MessagesReceived = _performanceMetrics.MessagesReceived,
            MessagesProcessed = _performanceMetrics.MessagesProcessed,
            MessagesFailed = _performanceMetrics.MessagesFailed,
            AverageProcessingTime = _performanceMetrics.AverageProcessingTime,
            MessagesPerSecond = _performanceMetrics.MessagesPerSecond,
            StartTime = _performanceMetrics.StartTime,
            Uptime = DateTime.UtcNow - _performanceMetrics.StartTime,
            MemoryUsage = GC.GetTotalMemory(false)
        };
    }

    public void ResetPerformanceCounters()
    {
        _performanceMetrics = new PerformanceMetrics
        {
            StartTime = DateTime.UtcNow
        };
    }

    public void ConfigurePerformanceMonitoring(PerformanceMonitoringConfiguration config)
    {
        // Update monitoring configuration
        StopMonitoring();
        StartMonitoring();
    }

    public async Task<ConnectionHealthStatus> GetConnectionHealthAsync()
    {
        var issues = new List<string>();
        var healthyConnections = 0;

        foreach (var kvp in _connections)
        {
            if (kvp.Value.State == WebSocketState.Open)
                healthyConnections++;
            else
                issues.Add($"{kvp.Key} connection is {kvp.Value.State}");
        }

        var isHealthy = healthyConnections > 0 && issues.Count == 0;

        await Task.CompletedTask;
        return new ConnectionHealthStatus
        {
            IsHealthy = isHealthy,
            Issues = issues,
            LastHealthCheck = DateTime.UtcNow,
            Uptime = DateTime.UtcNow - _performanceMetrics.StartTime,
            ReconnectionCount = 0 // Would track actual reconnections
        };
    }

    // === Private Methods ===

    private async Task CreateConnectionAsync(string connectionName, string url, CancellationToken cancellationToken)
    {
        try
        {
            var webSocket = new ClientWebSocket();
            var cts = new CancellationTokenSource();
            
            await webSocket.ConnectAsync(new Uri(url), cancellationToken);
            
            _connections.TryAdd(connectionName, webSocket);
            _cancellationTokens.TryAdd(connectionName, cts);
            
            // Authenticate
            await AuthenticateConnectionAsync(webSocket, cancellationToken);
            
            // Start receiving messages
            var receiveTask = ReceiveMessagesAsync(connectionName, webSocket, cts.Token);
            _receiveTasks.TryAdd(connectionName, receiveTask);
            
            _logger.LogDebug("Created {ConnectionName} connection to {Url}", connectionName, url);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to create {ConnectionName} connection", connectionName);
            throw;
        }
    }

    private async Task AuthenticateConnectionAsync(ClientWebSocket webSocket, CancellationToken cancellationToken)
    {
        var apiKey = _configuration["POLY_API_KEY"];
        if (string.IsNullOrEmpty(apiKey))
        {
            throw new InvalidOperationException("POLY_API_KEY not configured");
        }

        var authMessage = new { action = "auth", @params = apiKey };
        await SendMessageAsync(webSocket, authMessage, cancellationToken);
    }

    private async Task SendMessageAsync(ClientWebSocket webSocket, object message, CancellationToken cancellationToken)
    {
        var json = JsonSerializer.Serialize(message);
        var bytes = Encoding.UTF8.GetBytes(json);
        await webSocket.SendAsync(new ArraySegment<byte>(bytes), WebSocketMessageType.Text, true, cancellationToken);
    }

    private async Task ReceiveMessagesAsync(string connectionName, ClientWebSocket webSocket, CancellationToken cancellationToken)
    {
        var buffer = new byte[4096];
        
        try
        {
            while (webSocket.State == WebSocketState.Open && !cancellationToken.IsCancellationRequested)
            {
                var result = await webSocket.ReceiveAsync(new ArraySegment<byte>(buffer), cancellationToken);
                
                if (result.MessageType == WebSocketMessageType.Text)
                {
                    var message = Encoding.UTF8.GetString(buffer, 0, result.Count);
                    await ProcessMessageAsync(connectionName, message);
                    
                    // Update metrics
                    var currentMetrics = _performanceMetrics;
                    _performanceMetrics = new PerformanceMetrics
                    {
                        MessagesReceived = currentMetrics.MessagesReceived + 1,
                        MessagesProcessed = currentMetrics.MessagesProcessed + 1,
                        MessagesFailed = currentMetrics.MessagesFailed,
                        AverageProcessingTime = currentMetrics.AverageProcessingTime,
                        MessagesPerSecond = currentMetrics.MessagesPerSecond,
                        StartTime = currentMetrics.StartTime,
                        Uptime = currentMetrics.Uptime,
                        MemoryUsage = currentMetrics.MemoryUsage
                    };
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error receiving messages from {ConnectionName}", connectionName);
            var currentMetrics = _performanceMetrics;
            _performanceMetrics = new PerformanceMetrics
            {
                MessagesReceived = currentMetrics.MessagesReceived,
                MessagesProcessed = currentMetrics.MessagesProcessed,
                MessagesFailed = currentMetrics.MessagesFailed + 1,
                AverageProcessingTime = currentMetrics.AverageProcessingTime,
                MessagesPerSecond = currentMetrics.MessagesPerSecond,
                StartTime = currentMetrics.StartTime,
                Uptime = currentMetrics.Uptime,
                MemoryUsage = currentMetrics.MemoryUsage
            };
        }
    }

    private async Task ProcessMessageAsync(string connectionName, string message)
    {
        try
        {
            using var document = JsonDocument.Parse(message);
            if (document.RootElement.ValueKind == JsonValueKind.Array)
            {
                foreach (var element in document.RootElement.EnumerateArray())
                {
                    await ProcessSingleMessageAsync(connectionName, element);
                }
            }
            else
            {
                await ProcessSingleMessageAsync(connectionName, document.RootElement);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing message from {ConnectionName}: {Message}", connectionName, message);
            var currentMetrics = _performanceMetrics;
            _performanceMetrics = new PerformanceMetrics
            {
                MessagesReceived = currentMetrics.MessagesReceived,
                MessagesProcessed = currentMetrics.MessagesProcessed,
                MessagesFailed = currentMetrics.MessagesFailed + 1,
                AverageProcessingTime = currentMetrics.AverageProcessingTime,
                MessagesPerSecond = currentMetrics.MessagesPerSecond,
                StartTime = currentMetrics.StartTime,
                Uptime = currentMetrics.Uptime,
                MemoryUsage = currentMetrics.MemoryUsage
            };
        }
    }

    private async Task ProcessSingleMessageAsync(string connectionName, JsonElement element)
    {
        await Task.CompletedTask; // Make async for future expansion

        if (!element.TryGetProperty("ev", out var eventTypeElement))
            return;

        var eventType = eventTypeElement.GetString();

        // Process different message types based on connection and event type
        switch (eventType)
        {
            case "status":
                ProcessStatusMessage(element);
                break;
            case "V": // Index value update
                ProcessIndexValueMessage(element);
                break;
            case "T": // Trade update
                ProcessTradeMessage(element);
                break;
            case "Q": // Quote update
                ProcessQuoteMessage(element);
                break;
            case "A": // Aggregate update
                ProcessAggregateMessage(element);
                break;
            default:
                _logger.LogDebug("Received unknown message type: {EventType} from {ConnectionName}", eventType, connectionName);
                break;
        }
    }

    private void ProcessStatusMessage(JsonElement element)
    {
        if (element.TryGetProperty("status", out var statusElement))
        {
            var status = statusElement.GetString();
            if (status == "auth_success")
            {
                SetConnectionStatus(PolygonConnectionStatus.Authenticated, "Authenticated successfully");
            }
        }
    }

    private void ProcessIndexValueMessage(JsonElement element)
    {
        try
        {
            if (element.TryGetProperty("sym", out var symElement) &&
                element.TryGetProperty("val", out var valElement) &&
                element.TryGetProperty("t", out var timestampElement))
            {
                var symbol = symElement.GetString() ?? "";
                var value = valElement.GetDecimal();
                var timestamp = DateTimeOffset.FromUnixTimeMilliseconds(timestampElement.GetInt64()).DateTime;

                IndexUpdated?.Invoke(this, new PolygonIndexUpdateEventArgs
                {
                    IndexSymbol = symbol,
                    Value = value,
                    Change = 0m, // Would calculate from previous value
                    ChangePercent = 0m, // Would calculate from previous value
                    Timestamp = timestamp,
                    Volume = 0L // Would get from message if available
                });
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing index value message");
        }
    }

    private void ProcessTradeMessage(JsonElement element)
    {
        try
        {
            if (element.TryGetProperty("sym", out var symElement) &&
                element.TryGetProperty("p", out var priceElement) &&
                element.TryGetProperty("s", out var sizeElement) &&
                element.TryGetProperty("t", out var timestampElement))
            {
                var symbol = symElement.GetString() ?? "";
                var price = priceElement.GetDecimal();
                var size = sizeElement.GetInt64();
                var timestamp = DateTimeOffset.FromUnixTimeMilliseconds(timestampElement.GetInt64()).DateTime;

                TradeUpdated?.Invoke(this, new PolygonTradeUpdateEventArgs
                {
                    Symbol = symbol,
                    Price = price,
                    Size = size,
                    Timestamp = timestamp,
                    Exchange = element.TryGetProperty("x", out var exElement) ? exElement.GetString() ?? "" : ""
                });
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing trade message");
        }
    }

    private void ProcessQuoteMessage(JsonElement element)
    {
        try
        {
            if (element.TryGetProperty("sym", out var symElement) &&
                element.TryGetProperty("bp", out var bidElement) &&
                element.TryGetProperty("ap", out var askElement) &&
                element.TryGetProperty("t", out var timestampElement))
            {
                var symbol = symElement.GetString() ?? "";
                var bidPrice = bidElement.GetDecimal();
                var askPrice = askElement.GetDecimal();
                var timestamp = DateTimeOffset.FromUnixTimeMilliseconds(timestampElement.GetInt64()).DateTime;

                QuoteUpdated?.Invoke(this, new PolygonQuoteUpdateEventArgs
                {
                    Symbol = symbol,
                    BidPrice = bidPrice,
                    AskPrice = askPrice,
                    Timestamp = timestamp,
                    BidSize = element.TryGetProperty("bs", out var bsElement) ? bsElement.GetInt64() : 0,
                    AskSize = element.TryGetProperty("as", out var asElement) ? asElement.GetInt64() : 0
                });
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing quote message");
        }
    }

    private void ProcessAggregateMessage(JsonElement element)
    {
        try
        {
            if (element.TryGetProperty("sym", out var symElement) &&
                element.TryGetProperty("c", out var closeElement) &&
                element.TryGetProperty("t", out var timestampElement))
            {
                var symbol = symElement.GetString() ?? "";
                var close = closeElement.GetDecimal();
                var timestamp = DateTimeOffset.FromUnixTimeMilliseconds(timestampElement.GetInt64()).DateTime;

                AggregateUpdated?.Invoke(this, new PolygonAggregateUpdateEventArgs
                {
                    Symbol = symbol,
                    Close = close,
                    Timestamp = timestamp,
                    Open = element.TryGetProperty("o", out var openElement) ? openElement.GetDecimal() : close,
                    High = element.TryGetProperty("h", out var highElement) ? highElement.GetDecimal() : close,
                    Low = element.TryGetProperty("l", out var lowElement) ? lowElement.GetDecimal() : close,
                    Volume = element.TryGetProperty("v", out var volumeElement) ? volumeElement.GetInt64() : 0
                });
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing aggregate message");
        }
    }

    private void SetConnectionStatus(PolygonConnectionStatus status, string? message = null, Exception? exception = null)
    {
        _connectionStatus = status;
        ConnectionStatusChanged?.Invoke(this, new PolygonConnectionStatusEventArgs
        {
            Status = status,
            Message = message,
            Exception = exception
        });
    }

    private void InitializeMetrics()
    {
        _performanceMetrics = new PerformanceMetrics
        {
            StartTime = DateTime.UtcNow
        };
        
        _connectionQuality = new ConnectionQualityMetrics
        {
            Quality = SmaTrendFollower.Services.ConnectionQuality.Good,
            LastUpdate = DateTime.UtcNow,
            Latency = 0.0,
            PacketLoss = 0.0,
            Throughput = 0.0
        };
    }

    private void StartMonitoring()
    {
        _metricsTimer = new Timer(UpdateMetrics, null, TimeSpan.Zero, TimeSpan.FromSeconds(10));
        _healthCheckTimer = new Timer(PerformHealthCheck, null, TimeSpan.FromMinutes(1), TimeSpan.FromMinutes(1));
    }

    private void StopMonitoring()
    {
        _metricsTimer?.Dispose();
        _healthCheckTimer?.Dispose();
        _metricsTimer = null;
        _healthCheckTimer = null;
    }

    private void UpdateMetrics(object? state)
    {
        var now = DateTime.UtcNow;
        var elapsed = now - _performanceMetrics.StartTime;
        
        _performanceMetrics = new PerformanceMetrics
        {
            MessagesReceived = _performanceMetrics.MessagesReceived,
            MessagesProcessed = _performanceMetrics.MessagesProcessed,
            MessagesFailed = _performanceMetrics.MessagesFailed,
            MessagesPerSecond = elapsed.TotalSeconds > 0 ? _performanceMetrics.MessagesReceived / elapsed.TotalSeconds : 0,
            AverageProcessingTime = 1.0, // Would calculate actual processing time
            StartTime = _performanceMetrics.StartTime,
            MemoryUsage = GC.GetTotalMemory(false),
            Uptime = elapsed
        };

        MetricsUpdated?.Invoke(this, new ConnectionMetricsEventArgs
        {
            Metrics = _performanceMetrics,
            Timestamp = now
        });
    }

    private void PerformHealthCheck(object? state)
    {
        // Perform periodic health checks
        var healthyConnections = _connections.Count(kvp => kvp.Value.State == WebSocketState.Open);
        var quality = healthyConnections switch
        {
            >= 3 => SmaTrendFollower.Services.ConnectionQuality.Excellent,
            >= 2 => SmaTrendFollower.Services.ConnectionQuality.Good,
            >= 1 => SmaTrendFollower.Services.ConnectionQuality.Fair,
            _ => SmaTrendFollower.Services.ConnectionQuality.Poor
        };

        _connectionQuality = new ConnectionQualityMetrics
        {
            Quality = quality,
            LastUpdate = DateTime.UtcNow,
            Latency = 50.0, // Would measure actual latency
            PacketLoss = _connectionQuality.PacketLoss,
            Throughput = _performanceMetrics.MessagesPerSecond
        };
    }

    public async Task SubscribeToEquityUpdatesAsync(IEnumerable<string> stockSymbols, CancellationToken cancellationToken = default)
    {
        if (!_connections.TryGetValue("stocks", out var stocksConnection) || stocksConnection.State != WebSocketState.Open)
        {
            _logger.LogWarning("Stocks connection not available for subscription");
            return;
        }

        await _connectionSemaphore.WaitAsync(cancellationToken);
        try
        {
            _logger.LogInformation("Subscribing to equity updates for symbols: {Symbols}", string.Join(", ", stockSymbols));

            foreach (var symbol in stockSymbols)
            {
                // Subscribe to both trades and quotes
                var tradeSubscriptionMessage = new
                {
                    action = "subscribe",
                    @params = $"T.{symbol}"
                };

                var quoteSubscriptionMessage = new
                {
                    action = "subscribe",
                    @params = $"Q.{symbol}"
                };

                await SendMessageAsync(stocksConnection, tradeSubscriptionMessage, cancellationToken);
                await SendMessageAsync(stocksConnection, quoteSubscriptionMessage, cancellationToken);

                _subscriptions.TryAdd($"T.{symbol}", PolygonDataTypes.Trades);
                _subscriptions.TryAdd($"Q.{symbol}", PolygonDataTypes.Quotes);
            }

            _logger.LogDebug("Successfully subscribed to {Count} equity symbols", stockSymbols.Count());
        }
        finally
        {
            _connectionSemaphore.Release();
        }
    }

    public async Task UnsubscribeFromEquityUpdatesAsync(IEnumerable<string> stockSymbols, CancellationToken cancellationToken = default)
    {
        if (!_connections.TryGetValue("stocks", out var stocksConnection) || stocksConnection.State != WebSocketState.Open)
        {
            _logger.LogWarning("Stocks connection not available for unsubscription");
            return;
        }

        await _connectionSemaphore.WaitAsync(cancellationToken);
        try
        {
            _logger.LogInformation("Unsubscribing from equity updates for symbols: {Symbols}", string.Join(", ", stockSymbols));

            foreach (var symbol in stockSymbols)
            {
                // Unsubscribe from both trades and quotes
                var tradeUnsubscriptionMessage = new
                {
                    action = "unsubscribe",
                    @params = $"T.{symbol}"
                };

                var quoteUnsubscriptionMessage = new
                {
                    action = "unsubscribe",
                    @params = $"Q.{symbol}"
                };

                await SendMessageAsync(stocksConnection, tradeUnsubscriptionMessage, cancellationToken);
                await SendMessageAsync(stocksConnection, quoteUnsubscriptionMessage, cancellationToken);

                _subscriptions.TryRemove($"T.{symbol}", out _);
                _subscriptions.TryRemove($"Q.{symbol}", out _);
            }

            _logger.LogDebug("Successfully unsubscribed from {Count} equity symbols", stockSymbols.Count());
        }
        finally
        {
            _connectionSemaphore.Release();
        }
    }

    public void Dispose()
    {
        if (_disposed)
            return;

        DisconnectAsync().GetAwaiter().GetResult();
        _connectionSemaphore?.Dispose();
        _disposed = true;
    }
}
