using Microsoft.Extensions.Logging;
using SmaTrendFollower.Services;
using System.Runtime.CompilerServices;
using System.Text.Json;
using System.Text.Json.Serialization;

namespace SmaTrendFollower.Backtesting.Replay;

/// <summary>
/// Loads historical tick data from Polygon API for backtesting replay
/// </summary>
public class PolygonTickLoader
{
    private readonly IPolygonClientFactory _polygonFactory;
    private readonly ILogger<PolygonTickLoader> _logger;

    public PolygonTickLoader(
        IPolygonClientFactory polygonFactory,
        ILogger<PolygonTickLoader> logger)
    {
        _polygonFactory = polygonFactory;
        _logger = logger;
    }

    /// <summary>
    /// Loads historical trades for a symbol within the specified date range
    /// </summary>
    public async IAsyncEnumerable<TradeEvent> LoadTradesAsync(
        string symbol,
        DateTime startDate,
        DateTime endDate,
        [EnumeratorCancellation] CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("Loading trades for {Symbol} from {StartDate} to {EndDate}", 
            symbol, startDate, endDate);

        var httpClient = _polygonFactory.CreateClient();
        var rateLimitHelper = _polygonFactory.GetRateLimitHelper();
        
        var currentDate = startDate.Date;
        
        while (currentDate <= endDate.Date && !cancellationToken.IsCancellationRequested)
        {
            var dateStr = currentDate.ToString("yyyy-MM-dd");
            var url = $"v3/trades/{symbol}?timestamp.gte={dateStr}&timestamp.lt={currentDate.AddDays(1):yyyy-MM-dd}&limit=50000";
            
            await foreach (var trade in LoadTradesForDateAsync(httpClient, rateLimitHelper, url, symbol, cancellationToken))
            {
                yield return trade;
            }
            
            currentDate = currentDate.AddDays(1);
        }
    }

    /// <summary>
    /// Loads historical quotes for a symbol within the specified date range
    /// </summary>
    public async IAsyncEnumerable<QuoteEvent> LoadQuotesAsync(
        string symbol,
        DateTime startDate,
        DateTime endDate,
        [EnumeratorCancellation] CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("Loading quotes for {Symbol} from {StartDate} to {EndDate}", 
            symbol, startDate, endDate);

        var httpClient = _polygonFactory.CreateClient();
        var rateLimitHelper = _polygonFactory.GetRateLimitHelper();
        
        var currentDate = startDate.Date;
        
        while (currentDate <= endDate.Date && !cancellationToken.IsCancellationRequested)
        {
            var dateStr = currentDate.ToString("yyyy-MM-dd");
            var url = $"v3/quotes/{symbol}?timestamp.gte={dateStr}&timestamp.lt={currentDate.AddDays(1):yyyy-MM-dd}&limit=50000";
            
            await foreach (var quote in LoadQuotesForDateAsync(httpClient, rateLimitHelper, url, symbol, cancellationToken))
            {
                yield return quote;
            }
            
            currentDate = currentDate.AddDays(1);
        }
    }

    private async IAsyncEnumerable<TradeEvent> LoadTradesForDateAsync(
        HttpClient httpClient,
        IPolygonRateLimitHelper rateLimitHelper,
        string initialUrl,
        string symbol,
        [EnumeratorCancellation] CancellationToken cancellationToken)
    {
        var url = initialUrl;
        var tradesLoaded = 0;

        while (!string.IsNullOrEmpty(url) && !cancellationToken.IsCancellationRequested)
        {
            HttpResponseMessage? response = null;
            PolygonTradesResponse? tradesResponse = null;

            try
            {
                response = await rateLimitHelper.ExecuteAsync(async () =>
                {
                    var urlWithApiKey = _polygonFactory.AddApiKeyToUrl(url);
                    return await httpClient.GetAsync(urlWithApiKey, cancellationToken);
                }, $"TradesLoad_{symbol}");

                if (!response.IsSuccessStatusCode)
                {
                    _logger.LogWarning("Failed to load trades for {Symbol}: {StatusCode}", symbol, response.StatusCode);
                    break;
                }

                var content = await response.Content.ReadAsStringAsync(cancellationToken);
                tradesResponse = JsonSerializer.Deserialize<PolygonTradesResponse>(content);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error loading trades for {Symbol}", symbol);
                break;
            }

            if (tradesResponse?.Results != null)
            {
                foreach (var trade in tradesResponse.Results)
                {
                    var tradeEvent = new TradeEvent(
                        Symbol: symbol,
                        TimestampUtc: DateTimeOffset.FromUnixTimeMilliseconds(trade.Timestamp / 1_000_000).UtcDateTime,
                        Price: trade.Price,
                        Size: trade.Size
                    );
                    yield return tradeEvent;
                    tradesLoaded++;
                }
            }

            url = tradesResponse?.NextUrl;

            if (tradesLoaded % 10000 == 0 && tradesLoaded > 0)
            {
                _logger.LogDebug("Loaded {Count} trades for {Symbol}", tradesLoaded, symbol);
            }
        }

        _logger.LogInformation("Completed loading {Count} trades for {Symbol}", tradesLoaded, symbol);
    }

    private async IAsyncEnumerable<QuoteEvent> LoadQuotesForDateAsync(
        HttpClient httpClient,
        IPolygonRateLimitHelper rateLimitHelper,
        string initialUrl,
        string symbol,
        [EnumeratorCancellation] CancellationToken cancellationToken)
    {
        var url = initialUrl;
        var quotesLoaded = 0;

        while (!string.IsNullOrEmpty(url) && !cancellationToken.IsCancellationRequested)
        {
            HttpResponseMessage? response = null;
            PolygonQuotesResponse? quotesResponse = null;

            try
            {
                response = await rateLimitHelper.ExecuteAsync(async () =>
                {
                    var urlWithApiKey = _polygonFactory.AddApiKeyToUrl(url);
                    return await httpClient.GetAsync(urlWithApiKey, cancellationToken);
                }, $"QuotesLoad_{symbol}");

                if (!response.IsSuccessStatusCode)
                {
                    _logger.LogWarning("Failed to load quotes for {Symbol}: {StatusCode}", symbol, response.StatusCode);
                    break;
                }

                var content = await response.Content.ReadAsStringAsync(cancellationToken);
                quotesResponse = JsonSerializer.Deserialize<PolygonQuotesResponse>(content);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error loading quotes for {Symbol}", symbol);
                break;
            }

            if (quotesResponse?.Results != null)
            {
                foreach (var quote in quotesResponse.Results)
                {
                    var quoteEvent = new QuoteEvent(
                        Symbol: symbol,
                        TimestampUtc: DateTimeOffset.FromUnixTimeMilliseconds(quote.Timestamp / 1_000_000).UtcDateTime,
                        Bid: quote.Bid,
                        Ask: quote.Ask
                    );
                    yield return quoteEvent;
                    quotesLoaded++;
                }
            }

            url = quotesResponse?.NextUrl;

            if (quotesLoaded % 10000 == 0 && quotesLoaded > 0)
            {
                _logger.LogDebug("Loaded {Count} quotes for {Symbol}", quotesLoaded, symbol);
            }
        }

        _logger.LogInformation("Completed loading {Count} quotes for {Symbol}", quotesLoaded, symbol);
    }
}

// Polygon API response models for tick data
internal class PolygonTradesResponse
{
    [JsonPropertyName("results")]
    public List<PolygonTrade>? Results { get; set; }

    [JsonPropertyName("next_url")]
    public string? NextUrl { get; set; }
}

internal class PolygonQuotesResponse
{
    [JsonPropertyName("results")]
    public List<PolygonQuote>? Results { get; set; }

    [JsonPropertyName("next_url")]
    public string? NextUrl { get; set; }
}

internal class PolygonTrade
{
    [JsonPropertyName("participant_timestamp")]
    public long Timestamp { get; set; }

    [JsonPropertyName("price")]
    public decimal Price { get; set; }

    [JsonPropertyName("size")]
    public ulong Size { get; set; }
}

internal class PolygonQuote
{
    [JsonPropertyName("participant_timestamp")]
    public long Timestamp { get; set; }

    [JsonPropertyName("bid")]
    public decimal Bid { get; set; }

    [JsonPropertyName("ask")]
    public decimal Ask { get; set; }
}
