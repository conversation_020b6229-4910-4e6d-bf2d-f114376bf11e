using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using SmaTrendFollower.Console.Configuration;
using SmaTrendFollower.Services;
using System.Diagnostics;
using System.Text.Json;
using StackExchange.Redis;
using Discord;
using Discord.Rest;

namespace SmaTrendFollower.ConnectivityTest;

/// <summary>
/// Comprehensive connectivity test utility for pre-production validation
/// Tests all API endpoints and Redis connectivity with detailed reporting
/// </summary>
public class Program
{
    private static readonly List<TestResult> TestResults = new();
    private static ILogger<Program>? _logger;
    private static IConfiguration? _configuration;

    public static async Task<int> Main(string[] args)
    {
        System.Console.WriteLine("🚀 SmaTrendFollower Pre-Production Connectivity Test");
        System.Console.WriteLine(new string('=', 60));
        System.Console.WriteLine($"Test started at: {DateTime.UtcNow:yyyy-MM-dd HH:mm:ss} UTC");
        System.Console.WriteLine();

        try
        {
            // Build configuration and services
            var host = CreateHostBuilder(args).Build();
            _logger = host.Services.GetRequiredService<ILogger<Program>>();
            _configuration = host.Services.GetRequiredService<IConfiguration>();

            _logger.LogInformation("Starting comprehensive connectivity tests...");

            // Run all connectivity tests
            await RunRedisConnectivityTests();
            await RunAlpacaConnectivityTests(host.Services);
            await RunPolygonConnectivityTests(host.Services);
            await RunDiscordConnectivityTests();

            // Generate final report
            GenerateFinalReport();

            var failedTests = TestResults.Count(r => !r.Success);
            if (failedTests == 0)
            {
                System.Console.WriteLine("✅ ALL TESTS PASSED - SYSTEM READY FOR PRODUCTION!");
                return 0;
            }
            else
            {
                System.Console.WriteLine($"❌ {failedTests} TESTS FAILED - REVIEW REQUIRED BEFORE PRODUCTION");
                return 1;
            }
        }
        catch (Exception ex)
        {
            System.Console.WriteLine($"❌ Fatal error during connectivity testing: {ex.Message}");
            System.Console.WriteLine(ex.StackTrace);
            return 1;
        }
    }

    private static IHostBuilder CreateHostBuilder(string[] args) =>
        Host.CreateDefaultBuilder(args)
            .UseEnvironment("LocalProd")
            .ConfigureAppConfiguration((context, config) =>
            {
                config.AddJsonFile("appsettings.json", optional: false)
                      .AddJsonFile("appsettings.LocalProd.json", optional: false)
                      .AddEnvironmentVariables();
            })
            .ConfigureServices((context, services) =>
            {
                var configuration = context.Configuration;
                
                // Add core services
                services.AddSingleton(configuration);
                services.AddLogging(builder => builder.AddConsole().SetMinimumLevel(LogLevel.Information));

                // Add basic services needed for connectivity testing
                services.AddHttpClient();

                // Configure Polygon HTTP client
                services.AddHttpClient("Polygon", client =>
                {
                    client.BaseAddress = new Uri("https://api.polygon.io/");
                    client.Timeout = TimeSpan.FromSeconds(30);
                });

                // Add client factories
                services.AddSingleton<IAlpacaClientFactory, AlpacaClientFactory>();
                services.AddSingleton<IPolygonClientFactory, PolygonClientFactory>();
                services.AddSingleton<IApiHealthMonitor, ApiHealthMonitor>();
            });

    private static async Task RunRedisConnectivityTests()
    {
        System.Console.WriteLine("🔍 Testing Redis Connectivity...");

        // Test LocalProd Redis (*************:6379) - Production environment
        await TestRedisConnection("LocalProd", "*************:6379");
    }

    private static async Task TestRedisConnection(string environment, string connectionString)
    {
        var testName = $"Redis-{environment}";
        var stopwatch = Stopwatch.StartNew();
        
        try
        {
            System.Console.WriteLine($"  📡 Testing {environment} Redis at {connectionString}...");

            var configOptions = ConfigurationOptions.Parse(connectionString);
            configOptions.AbortOnConnectFail = false;
            configOptions.ConnectTimeout = 5000;
            configOptions.SyncTimeout = 5000;
            configOptions.ConnectRetry = 3;

            using var redis = ConnectionMultiplexer.Connect(configOptions);
            var db = redis.GetDatabase();

            // Test ping
            var pingResult = await db.PingAsync();

            // Test set/get operations
            var testKey = $"connectivity:test:{DateTime.UtcNow:yyyyMMddHHmmss}";
            var testValue = $"Test from {environment} at {DateTime.UtcNow}";

            await db.StringSetAsync(testKey, testValue, TimeSpan.FromMinutes(1));
            var retrievedValue = await db.StringGetAsync(testKey);
            await db.KeyDeleteAsync(testKey);

            stopwatch.Stop();

            if (redis.IsConnected && retrievedValue == testValue)
            {
                TestResults.Add(new TestResult
                {
                    TestName = testName,
                    Success = true,
                    ResponseTime = stopwatch.Elapsed,
                    Details = $"Ping: {pingResult.TotalMilliseconds:F2}ms, Read/Write: OK"
                });
                System.Console.WriteLine($"    ✅ {environment} Redis: Connected ({pingResult.TotalMilliseconds:F2}ms ping)");
            }
            else
            {
                throw new Exception("Connection established but read/write test failed");
            }
        }
        catch (Exception ex)
        {
            stopwatch.Stop();
            TestResults.Add(new TestResult
            {
                TestName = testName,
                Success = false,
                ResponseTime = stopwatch.Elapsed,
                ErrorMessage = ex.Message,
                Details = $"Failed to connect to {connectionString}"
            });
            System.Console.WriteLine($"    ❌ {environment} Redis: {ex.Message}");
        }
    }

    private static async Task RunAlpacaConnectivityTests(IServiceProvider services)
    {
        System.Console.WriteLine("🔍 Testing Alpaca API Connectivity...");

        try
        {
            var alpacaFactory = services.GetRequiredService<IAlpacaClientFactory>();
            var healthMonitor = services.GetRequiredService<IApiHealthMonitor>();

            // Test live trading environment (production)
            await TestAlpacaEnvironment("Alpaca-Live", alpacaFactory, healthMonitor, false);
        }
        catch (Exception ex)
        {
            TestResults.Add(new TestResult
            {
                TestName = "Alpaca-Setup",
                Success = false,
                ErrorMessage = ex.Message,
                Details = "Failed to initialize Alpaca services"
            });
            System.Console.WriteLine($"    ❌ Alpaca Setup: {ex.Message}");
        }
    }

    private static async Task TestAlpacaEnvironment(string testName, IAlpacaClientFactory factory, IApiHealthMonitor healthMonitor, bool isPaper)
    {
        var stopwatch = Stopwatch.StartNew();
        
        try
        {
            System.Console.WriteLine($"  📡 Testing {(isPaper ? "Paper" : "Live")} Trading API...");

            // Use health monitor for comprehensive test
            var healthStatus = await healthMonitor.GetAlpacaHealthAsync();

            stopwatch.Stop();

            if (healthStatus.IsHealthy)
            {
                TestResults.Add(new TestResult
                {
                    TestName = testName,
                    Success = true,
                    ResponseTime = stopwatch.Elapsed,
                    Details = $"Account Status: {healthStatus.AdditionalMetrics.GetValueOrDefault("AccountStatus", "Unknown")}"
                });
                System.Console.WriteLine($"    ✅ {(isPaper ? "Paper" : "Live")} Trading: Connected ({healthStatus.ResponseTime.TotalMilliseconds:F2}ms)");
            }
            else
            {
                throw new Exception(healthStatus.ErrorMessage ?? "Health check failed");
            }
        }
        catch (Exception ex)
        {
            stopwatch.Stop();
            TestResults.Add(new TestResult
            {
                TestName = testName,
                Success = false,
                ResponseTime = stopwatch.Elapsed,
                ErrorMessage = ex.Message,
                Details = $"Failed to connect to {(isPaper ? "paper" : "live")} trading API"
            });
            System.Console.WriteLine($"    ❌ {(isPaper ? "Paper" : "Live")} Trading: {ex.Message}");
        }
    }

    private static async Task RunPolygonConnectivityTests(IServiceProvider services)
    {
        System.Console.WriteLine("🔍 Testing Polygon API Connectivity...");

        try
        {
            var healthMonitor = services.GetRequiredService<IApiHealthMonitor>();
            var stopwatch = Stopwatch.StartNew();

            System.Console.WriteLine("  📡 Testing Polygon Market Data API...");

            var healthStatus = await healthMonitor.GetPolygonHealthAsync();

            stopwatch.Stop();

            if (healthStatus.IsHealthy)
            {
                TestResults.Add(new TestResult
                {
                    TestName = "Polygon-API",
                    Success = true,
                    ResponseTime = stopwatch.Elapsed,
                    Details = $"Market Status: {healthStatus.AdditionalMetrics.GetValueOrDefault("MarketStatus", "Unknown")}"
                });
                System.Console.WriteLine($"    ✅ Polygon API: Connected ({healthStatus.ResponseTime.TotalMilliseconds:F2}ms)");
            }
            else
            {
                throw new Exception(healthStatus.ErrorMessage ?? "Health check failed");
            }

            // Test WebSocket connectivity (basic connection test)
            await TestPolygonWebSocket();
        }
        catch (Exception ex)
        {
            TestResults.Add(new TestResult
            {
                TestName = "Polygon-API",
                Success = false,
                ErrorMessage = ex.Message,
                Details = "Failed to connect to Polygon API"
            });
            System.Console.WriteLine($"    ❌ Polygon API: {ex.Message}");
        }
    }

    private static async Task TestPolygonWebSocket()
    {
        var testName = "Polygon-WebSocket";
        var stopwatch = Stopwatch.StartNew();
        
        try
        {
            System.Console.WriteLine("  📡 Testing Polygon WebSocket connectivity...");

            // Basic WebSocket connection test (without full subscription)
            using var client = new System.Net.WebSockets.ClientWebSocket();
            var uri = new Uri("wss://socket.polygon.io/stocks");

            var cts = new CancellationTokenSource(TimeSpan.FromSeconds(10));
            await client.ConnectAsync(uri, cts.Token);

            stopwatch.Stop();

            if (client.State == System.Net.WebSockets.WebSocketState.Open)
            {
                TestResults.Add(new TestResult
                {
                    TestName = testName,
                    Success = true,
                    ResponseTime = stopwatch.Elapsed,
                    Details = "WebSocket connection established successfully"
                });
                System.Console.WriteLine($"    ✅ Polygon WebSocket: Connected ({stopwatch.ElapsedMilliseconds}ms)");

                await client.CloseAsync(System.Net.WebSockets.WebSocketCloseStatus.NormalClosure, "Test complete", CancellationToken.None);
            }
            else
            {
                throw new Exception($"WebSocket connection failed, state: {client.State}");
            }
        }
        catch (Exception ex)
        {
            stopwatch.Stop();
            TestResults.Add(new TestResult
            {
                TestName = testName,
                Success = false,
                ResponseTime = stopwatch.Elapsed,
                ErrorMessage = ex.Message,
                Details = "Failed to establish WebSocket connection"
            });
            System.Console.WriteLine($"    ❌ Polygon WebSocket: {ex.Message}");
        }
    }

    private static async Task RunDiscordConnectivityTests()
    {
        System.Console.WriteLine("🔍 Testing Discord Bot Connectivity...");

        var testName = "Discord-Bot";
        var stopwatch = Stopwatch.StartNew();

        try
        {
            var botToken = _configuration?["Discord:BotToken"];
            var channelIdStr = _configuration?["Discord:ChannelId"];

            if (string.IsNullOrEmpty(botToken) || string.IsNullOrEmpty(channelIdStr))
            {
                throw new Exception("Discord configuration missing");
            }

            if (!ulong.TryParse(channelIdStr, out var channelId))
            {
                throw new Exception("Invalid Discord channel ID format");
            }

            System.Console.WriteLine("  📡 Testing Discord bot authentication...");

            var client = new DiscordRestClient();
            await client.LoginAsync(TokenType.Bot, botToken);

            // Test channel access
            var channel = await client.GetChannelAsync(channelId) as ITextChannel;
            if (channel == null)
            {
                throw new Exception("Cannot access specified Discord channel");
            }

            stopwatch.Stop();

            TestResults.Add(new TestResult
            {
                TestName = testName,
                Success = true,
                ResponseTime = stopwatch.Elapsed,
                Details = $"Bot authenticated, channel '{channel.Name}' accessible"
            });
            System.Console.WriteLine($"    ✅ Discord Bot: Connected to #{channel.Name} ({stopwatch.ElapsedMilliseconds}ms)");

            await client.LogoutAsync();
        }
        catch (Exception ex)
        {
            stopwatch.Stop();
            TestResults.Add(new TestResult
            {
                TestName = testName,
                Success = false,
                ResponseTime = stopwatch.Elapsed,
                ErrorMessage = ex.Message,
                Details = "Failed to authenticate Discord bot or access channel"
            });
            System.Console.WriteLine($"    ❌ Discord Bot: {ex.Message}");
        }
    }

    private static void GenerateFinalReport()
    {
        System.Console.WriteLine();
        System.Console.WriteLine("📊 CONNECTIVITY TEST REPORT");
        System.Console.WriteLine(new string('=', 60));

        var totalTests = TestResults.Count;
        var passedTests = TestResults.Count(r => r.Success);
        var failedTests = totalTests - passedTests;

        System.Console.WriteLine($"Total Tests: {totalTests}");
        System.Console.WriteLine($"Passed: {passedTests} ✅");
        System.Console.WriteLine($"Failed: {failedTests} ❌");
        System.Console.WriteLine($"Success Rate: {(double)passedTests / totalTests * 100:F1}%");
        System.Console.WriteLine();

        // Detailed results
        System.Console.WriteLine("DETAILED RESULTS:");
        System.Console.WriteLine(new string('-', 40));

        foreach (var result in TestResults.OrderBy(r => r.TestName))
        {
            var status = result.Success ? "✅ PASS" : "❌ FAIL";
            var timing = result.ResponseTime.HasValue ? $"({result.ResponseTime.Value.TotalMilliseconds:F0}ms)" : "";

            System.Console.WriteLine($"{status} {result.TestName} {timing}");
            if (!string.IsNullOrEmpty(result.Details))
            {
                System.Console.WriteLine($"    Details: {result.Details}");
            }
            if (!string.IsNullOrEmpty(result.ErrorMessage))
            {
                System.Console.WriteLine($"    Error: {result.ErrorMessage}");
            }
        }

        System.Console.WriteLine();

        // Production readiness assessment
        if (failedTests == 0)
        {
            System.Console.WriteLine("🎉 PRODUCTION READINESS: EXCELLENT");
            System.Console.WriteLine("All systems are operational and ready for live trading.");
        }
        else if (failedTests <= 2)
        {
            System.Console.WriteLine("⚠️  PRODUCTION READINESS: REVIEW REQUIRED");
            System.Console.WriteLine("Some non-critical systems have issues. Review failed tests before going live.");
        }
        else
        {
            System.Console.WriteLine("🚨 PRODUCTION READINESS: NOT READY");
            System.Console.WriteLine("Critical systems have failures. DO NOT proceed with live trading until resolved.");
        }

        System.Console.WriteLine();
        System.Console.WriteLine($"Test completed at: {DateTime.UtcNow:yyyy-MM-dd HH:mm:ss} UTC");
    }

    private class TestResult
    {
        public string TestName { get; set; } = string.Empty;
        public bool Success { get; set; }
        public TimeSpan? ResponseTime { get; set; }
        public string? ErrorMessage { get; set; }
        public string? Details { get; set; }
    }
}
