# 🤖 Automated Weekly Signal-Ranker Retrain Implementation Guide

## 📋 Overview

This document describes the implementation of the automated weekly ML model retraining system for the SmaTrendFollower trading bot. The system provides hot-reload capabilities, Redis-based versioning, and comprehensive monitoring through Prometheus metrics.

## 🏗️ Architecture Components

### 1. **Enhanced MLModelRetrainerJob**
- **Location**: `SmaTrendFollower.Console/Scheduling/MLModelRetrainerJob.cs`
- **Schedule**: Weekly on Sunday at 6:00 PM ET (10:00 PM UTC)
- **Features**:
  - Exports fresh training data (last 24 months)
  - Trains new model using AutoML (5-minute timeout)
  - Updates Redis with model version for hot-reload
  - Records Prometheus metrics
  - Maintains model backups (keeps last 5)

### 2. **Hot-Reload SignalRanker**
- **Location**: `SmaTrendFollower.Console/MachineLearning/Prediction/SignalRanker.cs`
- **Features**:
  - Redis-based model version checking
  - Automatic hot-reload on version changes
  - Prometheus metrics integration
  - Zero-downtime model updates

### 3. **Redis Integration**
- **Model Version Key**: `model:signal:version`
- **Model Metadata Key**: `model:signal:metadata`
- **TTL**: 7 days (configurable via `RedisKeyConstants.RedisKeyTTL.MLModel`)

### 4. **Prometheus Metrics**
- `ml_retrain_total`: Counter for retraining runs (success/failure)
- `ml_model_accuracy`: Gauge for current model accuracy
- `ml_model_version`: Gauge for current model version timestamp
- `ml_retrain_duration_seconds`: Histogram for retraining duration
- `ml_predictions_total`: Counter for ML predictions (threshold_passed label)
- `ml_prediction_scores`: Histogram for prediction score distribution

## 🔧 Configuration

### Quartz.NET Scheduling
```csharp
// Schedule weekly on Sunday at 6:00 PM ET (10:00 PM UTC)
q.AddTrigger(t => t
    .ForJob(mlRetrainerJobKey)
    .WithIdentity("MLRetrainerTrigger")
    .StartNow()
    .WithCronSchedule("0 0 22 ? * SUN") // Weekly on Sunday at 10:00 PM UTC
    .WithDescription("Weekly ML model retraining on Sunday at 6:00 PM ET"));
```

### Redis Key Constants
```csharp
// TTL for ML model versioning and metadata (7 days)
public static readonly TimeSpan MLModel = TimeSpan.FromDays(7);

// Key patterns
public const string MLModel = "model:*";
public const string MLMetadata = "ml:*";
```

### Service Registration
```csharp
services.AddSingleton<ISignalRanker>(provider =>
{
    var mlContext = new MLContext(seed: 42);
    var configuration = provider.GetRequiredService<IConfiguration>();
    var logger = provider.GetRequiredService<ILogger<SignalRanker>>();
    var redisService = provider.GetService<OptimizedRedisConnectionService>();
    
    return new SignalRanker(mlContext, configuration, logger, redisService);
});
```

## 🚀 Usage

### Automatic Retraining
The system automatically retrains every Sunday at 6:00 PM ET. No manual intervention required.

### Manual Retraining
```bash
# Trigger manual retraining for testing
dotnet run -- ml-retrain
```

### Monitoring
```bash
# Check model information
dotnet run -- ml-info

# View Prometheus metrics
curl http://localhost:5000/metrics | grep ml_
```

## 🔄 Hot-Reload Process

1. **Model Training Completes**
   - New model saved to `Model/signal_model.zip`
   - Model metadata saved to `Model/signal_model.metadata.json`

2. **Redis Version Update**
   - `model:signal:version` set to file timestamp (ticks)
   - `model:signal:metadata` set to training results JSON

3. **Automatic Detection**
   - SignalRanker checks Redis version on each `Score()` call
   - If version changed, reloads model from disk
   - Updates internal version tracking

4. **Zero Downtime**
   - Old prediction engine disposed
   - New prediction engine created
   - Seamless transition for ongoing predictions

## 📊 Monitoring & Observability

### Prometheus Metrics Dashboard
```
# Retraining success rate
rate(ml_retrain_total[1h])

# Model accuracy over time
ml_model_accuracy

# Prediction volume and threshold pass rate
rate(ml_predictions_total[5m])

# Retraining duration trends
histogram_quantile(0.95, ml_retrain_duration_seconds)
```

### Logging
- Structured logging with Serilog
- Training progress and results
- Hot-reload events
- Error handling and recovery

## 🧪 Testing

### Integration Tests
- **Location**: `SmaTrendFollower.Tests/MachineLearning/MLRetrainerIntegrationTests.cs`
- **Coverage**:
  - MLModelRetrainerJob execution
  - SignalRanker hot-reload functionality
  - Redis version management
  - Prometheus metrics registration

### Manual Testing
```bash
# Run integration tests
dotnet test --filter "Category=Integration&MLRetrainer"

# Test manual retraining
dotnet run -- ml-retrain

# Verify metrics
dotnet run -- metrics-api
# Visit http://localhost:5000/metrics
```

## 🔒 Error Handling

### Retraining Failures
- Model backup restoration on training failure
- Prometheus failure metrics
- Detailed error logging
- Graceful degradation (keeps existing model)

### Hot-Reload Failures
- Fallback to existing prediction engine
- Warning logs for Redis connection issues
- Continues operation without hot-reload if Redis unavailable

### Data Quality Issues
- Feature validation before training
- Minimum sample size requirements (1000+ samples)
- Win rate validation and warnings

## 📈 Performance Considerations

### Training Performance
- 5-minute AutoML experiment timeout
- Parallel feature export
- Efficient CSV generation
- Background job execution

### Hot-Reload Performance
- Lightweight Redis version check
- Lazy loading on demand
- Minimal prediction latency impact
- Efficient model disposal

## 🔮 Future Enhancements

1. **A/B Testing**: Compare old vs new model performance
2. **Model Rollback**: Automatic rollback on performance degradation
3. **Multi-Model Ensemble**: Combine multiple model predictions
4. **Real-time Feature Updates**: Stream features for immediate retraining
5. **Advanced Metrics**: Model drift detection and feature importance tracking

## 📚 Related Documentation

- [Prometheus Observability Guide](PrometheusObservabilityGuide.md)
- [Redis Cache Management](../Services/RedisKeyConstants.cs)
- [Machine Learning Pipeline](../MachineLearning/README.md)
- [Quartz.NET Scheduling](../Scheduling/README.md)
