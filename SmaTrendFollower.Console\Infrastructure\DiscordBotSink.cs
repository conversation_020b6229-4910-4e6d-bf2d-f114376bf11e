using Serilog.Core;
using Serilog.Events;
using System.Net.Http.Headers;
using System.Text;
using System.Text.Json;
using SmaTrendFollower.Monitoring;

namespace SmaTrendFollower.Infrastructure;

/// <summary>
/// Discord bot-token alert sink that sends Warning+ level log events to Discord
/// Replaces webhook sink with bot token authentication for better reliability
/// </summary>
public sealed class DiscordBotSink : ILogEventSink
{
    private readonly HttpClient _http = new();
    private readonly string _channel;
    private readonly IFormatProvider? _fmt;

    public DiscordBotSink(string token, string channel, IFormatProvider? fmt = null)
    {
        _channel = channel;
        _fmt = fmt;
        _http.DefaultRequestHeaders.Authorization =
            new AuthenticationHeaderValue("Bot", token);
    }

    public void Emit(LogEvent logEvent)
    {
        if (logEvent.Level < LogEventLevel.Warning) return;   // send warn+error
        var msg = logEvent.RenderMessage(_fmt);
        var payload = new { content = $"**[{logEvent.Level}]** {msg}" };

        try
        {
            var json = JsonSerializer.Serialize(payload);
            var content = new StringContent(json, Encoding.UTF8, "application/json");
            _http.PostAsync(
                $"https://discord.com/api/v10/channels/{_channel}/messages",
                content).Wait();

            // Add metrics tracking
            MetricsRegistry.DiscordMessagesTotal
                .WithLabels(logEvent.Level.ToString()).Inc();
        }
        catch (Exception ex)
        {
            // Log errors to console to avoid infinite loops
            System.Console.WriteLine($"[DiscordBotSink] Error sending to Discord: {ex.Message}");
            MetricsRegistry.DiscordSinkErrorsTotal.Inc();
        }
    }
}


