global:
  scrape_interval: 15s
  evaluation_interval: 15s
  external_labels:
    monitor: 'sma-trend-follower'

rule_files:
  - "alerts.yml"

alerting:
  alertmanagers:
    - static_configs:
        - targets:
          - alertmanager:9093

scrape_configs:
  - job_name: 'sma-trend-follower'
    static_configs:
      - targets: ['bot:5000']  # Docker service name
    scrape_interval: 15s
    metrics_path: /metrics
    scrape_timeout: 10s
    honor_labels: false
    honor_timestamps: true
    scheme: http

  - job_name: 'prometheus'
    static_configs:
      - targets: ['localhost:9090']

  - job_name: 'vault'
    static_configs:
      - targets: ['vault:8200']
    scrape_interval: 30s
    metrics_path: /v1/sys/metrics
    params:
      format: ['prometheus']

  - job_name: 'redis'
    static_configs:
      - targets: ['redis:6379']
    scrape_interval: 30s
