using System.Threading.Tasks;
using FluentAssertions;
using StackExchange.Redis;
using SmaTrendFollower.Tests.Core.TestHelpers;
using Xunit;

namespace SmaTrendFollower.Tests.Core;

public class ExampleTests
{
    private readonly IDatabase _db = InMemoryRedis.Create();

    [Fact]
    public async Task Redis_Helper_Should_Clear_Database()
    {
        // Arrange
        var setResult = await _db.StringSetAsync("foo", "bar");
        setResult.Should().BeTrue();

        var getValue = await _db.StringGetAsync("foo");
        getValue.Should().Be("bar");

        var keyExists = await _db.KeyExistsAsync("foo");
        keyExists.Should().BeTrue();

        // Act – flush DB via helper
        await _db.ExecuteAsync("FLUSHDB");

        // Assert
        (await _db.KeyExistsAsync("foo")).Should().BeFalse();
    }
}
