# Vault Setup Script for SmaTrendFollower (PowerShell)
# This script initializes Vault with the required secrets

param(
    [string]$PolygonApiKey,
    [string]$AlpacaKeyId,
    [string]$AlpacaSecret,
    [string]$DiscordBotToken,
    [string]$DiscordChannelId,
    [string]$OpenAiApiKey
)

function Write-Status {
    param([string]$Message)
    Write-Host "[INFO] $Message" -ForegroundColor Blue
}

function Write-Success {
    param([string]$Message)
    Write-Host "[SUCCESS] $Message" -ForegroundColor Green
}

function Write-Warning {
    param([string]$Message)
    Write-Host "[WARNING] $Message" -ForegroundColor Yellow
}

# Vault configuration
$VaultAddr = "http://localhost:8200"
$VaultToken = "root"

Write-Status "Setting up Vault secrets for SmaTrendFollower..."

# Check if Vault is running
try {
    $response = Invoke-WebRequest -Uri "$VaultAddr/v1/sys/health" -UseBasicParsing -TimeoutSec 5
} catch {
    Write-Warning "Vault is not running. Please start the Docker stack first:"
    Write-Host "  docker-compose up -d vault" -ForegroundColor Gray
    exit 1
}

# Set environment variables for Vault CLI
$env:VAULT_ADDR = $VaultAddr
$env:VAULT_TOKEN = $VaultToken

Write-Status "Vault is running. Setting up secrets..."

# Prompt for API keys if not provided
if (-not $PolygonApiKey) {
    $PolygonApiKey = Read-Host "Enter Polygon API Key"
}

if (-not $AlpacaKeyId) {
    $AlpacaKeyId = Read-Host "Enter Alpaca Key ID"
}

if (-not $AlpacaSecret) {
    $AlpacaSecret = Read-Host "Enter Alpaca Secret" -AsSecureString
    $AlpacaSecret = [Runtime.InteropServices.Marshal]::PtrToStringAuto([Runtime.InteropServices.Marshal]::SecureStringToBSTR($AlpacaSecret))
}

if (-not $DiscordBotToken) {
    $DiscordBotToken = Read-Host "Enter Discord Bot Token (optional, press Enter to skip)"
}

if (-not $DiscordChannelId) {
    $DiscordChannelId = Read-Host "Enter Discord Channel ID (optional, press Enter to skip)"
}

if (-not $OpenAiApiKey) {
    $OpenAiApiKeySecure = Read-Host "Enter OpenAI API Key (optional, press Enter to skip)" -AsSecureString
    if ($OpenAiApiKeySecure.Length -gt 0) {
        $OpenAiApiKey = [Runtime.InteropServices.Marshal]::PtrToStringAuto([Runtime.InteropServices.Marshal]::SecureStringToBSTR($OpenAiApiKeySecure))
    }
}

# Store secrets in Vault
Write-Status "Storing secrets in Vault..."

$secretData = @{
    POLYGON_API_KEY = $PolygonApiKey
    ALPACA_KEY_ID = $AlpacaKeyId
    ALPACA_SECRET = $AlpacaSecret
}

# Add optional secrets if provided
if ($DiscordBotToken) { $secretData.DISCORD_BOT_TOKEN = $DiscordBotToken }
if ($DiscordChannelId) { $secretData.DISCORD_CHANNEL_ID = $DiscordChannelId }
if ($OpenAiApiKey) { $secretData.OPENAI_API_KEY = $OpenAiApiKey }

# Build vault command
$vaultArgs = @("kv", "put", "secret/data/sma")
foreach ($key in $secretData.Keys) {
    $vaultArgs += "$key=$($secretData[$key])"
}

try {
    & vault @vaultArgs
    Write-Success "Secrets stored successfully in Vault!"
} catch {
    Write-Error "Failed to store secrets in Vault: $_"
    exit 1
}

# Verify secrets
Write-Status "Verifying secrets..."
try {
    vault kv get secret/data/sma
    Write-Success "Vault setup completed successfully!"
    Write-Status "You can now start the full stack with: docker-compose up -d"
} catch {
    Write-Warning "Could not verify secrets, but they may have been stored successfully."
}
