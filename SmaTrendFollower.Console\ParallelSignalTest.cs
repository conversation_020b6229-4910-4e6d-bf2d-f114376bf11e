using System.Collections.Concurrent;
using System.Diagnostics;
using SmaTrendFollower.Monitoring;

namespace SmaTrendFollower.Console;

/// <summary>
/// Standalone test to verify parallel signal generation with rate limiting
/// </summary>
public static class ParallelSignalTest
{
    private static readonly SemaphoreSlim _rateGate = new(20); // 20 concurrent calls ≈ 80-90 req/s

    public static async Task RunTestAsync()
    {
        System.Console.WriteLine("🚀 Testing Parallel Signal Generation with Rate Limiting");
        System.Console.WriteLine($"🖥️  CPU Cores: {Environment.ProcessorCount}");
        System.Console.WriteLine($"🔒 Rate Gate: 20 concurrent operations");
        System.Console.WriteLine();

        // Test with different universe sizes
        await TestUniverseSize(50, "Small Universe");
        await TestUniverseSize(100, "Medium Universe");
        await TestUniverseSize(200, "Large Universe");

        System.Console.WriteLine("✅ All tests completed successfully!");
    }

    private static async Task TestUniverseSize(int symbolCount, string testName)
    {
        System.Console.WriteLine($"📊 {testName} ({symbolCount} symbols)");

        // Generate test symbols
        var symbols = Enumerable.Range(1, symbolCount)
            .Select(i => $"STOCK{i:D3}")
            .ToList();

        var signals = new ConcurrentBag<string>();
        var latencies = new ConcurrentBag<double>();

        var totalStopwatch = Stopwatch.StartNew();

        // Simulate parallel signal generation with rate limiting
        var tasks = symbols.Select(async symbol =>
        {
            var sw = Stopwatch.StartNew();
            await _rateGate.WaitAsync();
            try
            {
                // Simulate API call latency (10-50ms)
                var delay = Random.Shared.Next(10, 51);
                await Task.Delay(delay);

                // Simulate signal generation logic
                if (Random.Shared.NextDouble() > 0.7) // 30% signal rate
                {
                    signals.Add(symbol);
                }
            }
            finally
            {
                _rateGate.Release();
                var latencyMs = sw.Elapsed.TotalMilliseconds;
                latencies.Add(latencyMs);

                // Record metrics (similar to our implementation)
                MetricsRegistry.SignalLatencyMs.Observe(latencyMs);

                // Log slow signals (similar to our implementation)
                if (latencyMs > 500)
                {
                    System.Console.WriteLine($"⚠️  Slow signal {symbol} {latencyMs:F0} ms");
                }
            }
        });

        await Task.WhenAll(tasks);
        totalStopwatch.Stop();

        // Calculate statistics
        var avgLatency = latencies.Average();
        var maxLatency = latencies.Max();
        var minLatency = latencies.Min();
        var throughput = symbolCount / totalStopwatch.Elapsed.TotalSeconds;

        System.Console.WriteLine($"   ⏱️  Total Time: {totalStopwatch.Elapsed.TotalMilliseconds:F0}ms");
        System.Console.WriteLine($"   🎯 Signals Generated: {signals.Count}/{symbolCount} ({signals.Count * 100.0 / symbolCount:F1}%)");
        System.Console.WriteLine($"   📈 Throughput: {throughput:F1} symbols/sec");
        System.Console.WriteLine($"   ⚡ Latency - Avg: {avgLatency:F0}ms, Min: {minLatency:F0}ms, Max: {maxLatency:F0}ms");

        // Verify performance expectations
        var expectedMaxTime = symbolCount * 50 / 20 + 1000; // Theoretical max time + overhead
        if (totalStopwatch.Elapsed.TotalMilliseconds < expectedMaxTime)
        {
            System.Console.WriteLine($"   ✅ Performance: GOOD (under {expectedMaxTime:F0}ms limit)");
        }
        else
        {
            System.Console.WriteLine($"   ⚠️  Performance: SLOW (over {expectedMaxTime:F0}ms limit)");
        }

        System.Console.WriteLine();
    }
}
