using FluentAssertions;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging.Abstractions;
using Microsoft.ML;
using SmaTrendFollower.MachineLearning.Prediction;
using SmaTrendFollower.Models;
using Xunit;

namespace SmaTrendFollower.Tests.Core.Ml;

public class SignalRankerHotReloadTests
{
    [Fact]
    public void Score_HandlesNoRedisService()
    {
        // Arrange - No Redis service provided
        var configuration = TestConfiguration.WithoutRedis();
        var ranker = new SignalRanker(new MLContext(), configuration, NullLogger<SignalRanker>.Instance);

        var features = new SignalFeatures(0.1f, 0.02f, 50f, 0.8f, 16f, 0.15f, 1.2f, 0f);

        // Act & Assert - Should not throw when Redis service is null
        var act = () => ranker.Score(features);
        act.Should().NotThrow();

        var score = ranker.Score(features);
        score.Should().BeInRange(0f, 1f);
    }

    [Fact]
    public void Score_ReturnsDefaultWhenNoModel()
    {
        // Arrange - No model file exists
        var configuration = TestConfiguration.WithoutRedis();
        var ranker = new SignalRanker(new MLContext(), configuration, NullLogger<SignalRanker>.Instance);

        var features = new SignalFeatures(0.1f, 0.02f, 50f, 0.8f, 16f, 0.15f, 1.2f, 0f);

        // Act
        var score = ranker.Score(features);

        // Assert - Should return default score when no model is loaded
        score.Should().Be(0.5f);
        ranker.IsModelLoaded.Should().BeFalse();
    }

    [Fact]
    public void SignalRanker_CanBeInstantiated()
    {
        // Arrange & Act
        var configuration = TestConfiguration.WithoutRedis();
        var ranker = new SignalRanker(new MLContext(), configuration, NullLogger<SignalRanker>.Instance);

        // Assert
        ranker.Should().NotBeNull();
        ranker.IsModelLoaded.Should().BeFalse();
    }
}

/// <summary>
/// Test configuration helper for creating IConfiguration
/// </summary>
public static class TestConfiguration
{
    public static IConfiguration WithoutRedis()
    {
        var configData = new Dictionary<string, string?>
        {
            ["ML:ModelPath"] = "Model/signal_model.zip",
            ["ML:PositionModelPath"] = "Model/position_model.zip"
        };

        return new ConfigurationBuilder()
            .AddInMemoryCollection(configData)
            .Build();
    }
}
