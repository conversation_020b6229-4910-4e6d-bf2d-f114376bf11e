# Quote Volatility Guard Implementation

## Overview

The **QuoteVolatilityGuard** is a real-time quote volatility monitoring service that detects unusual bid-ask spread volatility and automatically halts trading for affected symbols. It complements the existing `AnomalyDetectorService` by focusing specifically on quote-level anomalies with more sensitive detection thresholds.

## 🎯 Key Features

### Real-time Spread Analysis
- Monitors bid-ask spreads using rolling 120-quote window (≈ 2 minutes)
- Calculates spread percentage relative to mid-price: `(ask - bid) / ((ask + bid) / 2)`
- Uses `RollingStats` class for efficient statistical calculations

### Sensitive Detection
- **2σ threshold** (vs 3σ in AnomalyDetector) for faster anomaly detection
- Triggers on spread volatility spikes that indicate market disruption
- Requires sufficient data (minimum 20 quotes) before analysis

### Automatic Trading Halts
- Sets Redis halt keys: `halt:{symbol}` with value `"volatility"`
- **2-minute TTL** for automatic expiration
- Integrates with existing halt checking logic in `TradeExecutor` and `SimpleSignalGenerator`

### Prometheus Metrics
- `quote_vol_halts_total`: Counter of total halts by symbol
- `quote_vol_halted`: Gauge indicating active halts
- `spread_z_scores`: Histogram of z-score distribution
- `spread_checks_total`: Counter of total checks performed

### Thread-safe Operations
- Concurrent processing of multiple symbols without interference
- Lock-free operations where possible using `ConcurrentDictionary`

## 🏗️ Architecture Integration

### PolygonWebSocketManager Integration
```csharp
// Quote processing in PolygonWebSocketManager
if (!string.IsNullOrEmpty(symbol))
{
    _anomalyDetector?.OnQuote(symbol, bid, ask, timestamp);
    _quoteVolatilityGuard?.OnQuote(symbol, bid, ask);  // ✅ Added
}
```

### Dependency Injection Registration
```csharp
// In ServiceConfiguration.cs
services.AddSingleton<QuoteVolatilityGuard>();

services.AddSingleton<PolygonWebSocketManager>(provider =>
{
    // ... existing dependencies
    var quoteVolatilityGuard = provider.GetService<QuoteVolatilityGuard>();
    return new PolygonWebSocketManager(/* ... */, quoteVolatilityGuard);
});
```

### Halt Mechanism Compatibility
Both `AnomalyDetectorService` and `QuoteVolatilityGuard` use the same Redis key pattern:
- **QuoteVolatilityGuard**: `halt:{symbol}` = `"volatility"`
- **AnomalyDetectorService**: `halt:{symbol}` = `"1"`

Existing halt checks work with both services since they only verify key existence.

## 📊 Configuration

| Parameter | Value | Description |
|-----------|-------|-------------|
| Window Size | 120 quotes | Rolling window for statistics (≈ 2 minutes) |
| Threshold | 2.0σ | Z-score threshold for anomaly detection |
| Halt Duration | 2 minutes | TTL for Redis halt keys |
| Redis Key Pattern | `halt:{symbol}` | Key format for trading halts |
| Redis Value | `"volatility"` | Identifies QuoteVolatilityGuard halts |

## 🔧 Implementation Files

### Core Service
- **`SmaTrendFollower.Console/Services/QuoteVolatilityGuard.cs`**
  - Main service implementation
  - Real-time quote processing and anomaly detection
  - Redis halt key management
  - Prometheus metrics integration

### Integration Points
- **`SmaTrendFollower.Console/Services/PolygonWebSocketManager.cs`**
  - Added QuoteVolatilityGuard dependency
  - Integrated quote processing pipeline

- **`SmaTrendFollower.Console/Configuration/ServiceConfiguration.cs`**
  - DI registration for QuoteVolatilityGuard
  - Updated PolygonWebSocketManager factory

### Documentation
- **`STREAMING_README.md`**
  - Updated with QuoteVolatilityGuard section
  - Integration details and metrics documentation

### Testing
- **`SmaTrendFollower.Tests/Services/QuoteVolatilityGuardTests.cs`**
  - Comprehensive unit tests
  - Mock Redis integration testing

- **`SmaTrendFollower.Tests/Integration/QuoteVolatilityGuardIntegrationTests.cs`**
  - End-to-end integration tests with real Redis
  - Multi-symbol independence testing
  - TTL expiration verification

### Examples
- **`SmaTrendFollower.Console/Examples/QuoteVolatilityGuardDemo.cs`**
  - Interactive demonstration script
  - Shows complete workflow from normal quotes to halt

## 🚀 Usage Example

```csharp
// Service automatically processes quotes from WebSocket
_quoteVolatilityGuard.OnQuote("AAPL", 150.00m, 150.10m);

// Check if trading is halted
var isHalted = await _quoteVolatilityGuard.IsTradingHaltedAsync("AAPL");

// Get spread statistics
var stats = _quoteVolatilityGuard.GetSpreadStats("AAPL");
if (stats.HasValue)
{
    Console.WriteLine($"Mean spread: {stats.Value.Mean:F6}");
    Console.WriteLine($"Std deviation: {stats.Value.StandardDeviation:F6}");
    Console.WriteLine($"Quote count: {stats.Value.Count}");
}
```

## 🔍 Monitoring

### Prometheus Metrics
Access metrics at `/metrics` endpoint:
```
# Quote volatility halts by symbol
quote_vol_halts_total{symbol="AAPL"} 3

# Active halt status
quote_vol_halted{symbol="AAPL"} 1

# Z-score distribution
spread_z_scores_bucket{symbol="AAPL",le="2.0"} 1250
spread_z_scores_bucket{symbol="AAPL",le="3.0"} 1251
```

### Redis Keys
Monitor halt keys directly:
```bash
# Check active halts
redis-cli KEYS "halt:*"

# Check specific symbol
redis-cli GET "halt:AAPL"
redis-cli TTL "halt:AAPL"
```

### Logging
Service logs key events:
- Quote volatility anomaly detection
- Trading halt activation
- Statistics cleanup operations
- Error conditions

## 🧪 Testing

### Run Unit Tests
```bash
dotnet test --filter "QuoteVolatilityGuardTests"
```

### Run Integration Tests
```bash
dotnet test --filter "QuoteVolatilityGuardIntegrationTests"
```

### Run Demo
```bash
dotnet run --project SmaTrendFollower.Console -- quote-volatility-demo
```

## 🔄 Interaction with Existing Systems

### TradeExecutor
- Existing halt check: `await _anomalyDetector.IsTradingHaltedAsync(symbol)`
- Works with both AnomalyDetector and QuoteVolatilityGuard halts
- No code changes required

### SimpleSignalGenerator
- Existing halt check: `await _anomalyDetector.IsTradingHaltedAsync(symbol)`
- Skips signal generation for halted symbols
- No code changes required

### ClearHaltGaugeJob
- Scheduled job clears expired halt gauges
- Works with both anomaly and volatility halts
- No code changes required

## ✅ Production Readiness

The QuoteVolatilityGuard is production-ready with:
- ✅ Comprehensive error handling
- ✅ Thread-safe concurrent operations
- ✅ Prometheus metrics integration
- ✅ Redis TTL-based cleanup
- ✅ Unit and integration test coverage
- ✅ Documentation and examples
- ✅ Seamless integration with existing halt mechanisms

The implementation follows the exact specification provided and integrates cleanly with the existing SmaTrendFollower architecture.
