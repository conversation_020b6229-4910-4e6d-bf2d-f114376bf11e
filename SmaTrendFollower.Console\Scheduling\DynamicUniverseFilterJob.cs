using Microsoft.Extensions.Logging;
using Quartz;
using SmaTrendFollower.Services;

namespace SmaTrendFollower.Scheduling;

/// <summary>
/// Interface for dynamic universe filter job
/// </summary>
public interface IDynamicUniverseFilterJob
{
    /// <summary>
    /// Run the universe filtering logic once
    /// Used by both Quartz scheduler and StartupDataBootstrapper
    /// </summary>
    Task RunOnceAsync(CancellationToken cancellationToken = default);
}

/// <summary>
/// Quartz job that handles daily universe filtering
/// Separated from UniverseJobs to allow independent execution by StartupDataBootstrapper
/// </summary>
[DisallowConcurrentExecution]
public class DynamicUniverseFilterJob : IDynamicUniverseFilterJob, IJob
{
    private readonly IDynamicUniverseProvider _dynamicUniverseProvider;
    private readonly ILogger<DynamicUniverseFilterJob> _logger;

    public DynamicUniverseFilterJob(
        IDynamicUniverseProvider dynamicUniverseProvider,
        ILogger<DynamicUniverseFilterJob> logger)
    {
        _dynamicUniverseProvider = dynamicUniverseProvider ?? throw new ArgumentNullException(nameof(dynamicUniverseProvider));
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
    }

    public async Task Execute(IJobExecutionContext context)
    {
        var jobKey = context.JobDetail.Key;
        var triggerKey = context.Trigger.Key;
        
        _logger.LogInformation("DynamicUniverseFilterJob started - Job: {JobKey}, Trigger: {TriggerKey}", jobKey, triggerKey);

        try
        {
            await RunOnceAsync(context.CancellationToken);
        }
        catch (OperationCanceledException)
        {
            _logger.LogWarning("DynamicUniverseFilterJob was cancelled");
            throw;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error executing DynamicUniverseFilterJob");
            throw;
        }

        _logger.LogInformation("DynamicUniverseFilterJob completed successfully");
    }

    /// <summary>
    /// Run the universe filtering logic once
    /// Used by both Quartz scheduler and StartupDataBootstrapper
    /// </summary>
    public async Task RunOnceAsync(CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("Building daily filtered universe");
        
        var universe = await _dynamicUniverseProvider.BuildUniverseAsync(null, cancellationToken);
        var universeList = universe.ToList();
        
        _logger.LogInformation("Daily universe filtering completed: {Count} symbols selected", universeList.Count);

        // Log some sample symbols for verification
        if (universeList.Count > 0)
        {
            var sampleSymbols = universeList.Take(10).ToList();
            _logger.LogInformation("Sample symbols: {Symbols}", string.Join(", ", sampleSymbols));
        }
    }
}
