using FluentAssertions;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Microsoft.EntityFrameworkCore;
using Moq;
using SmaTrendFollower.Data;
using SmaTrendFollower.MachineLearning.DataPrep;
using SmaTrendFollower.MachineLearning.ModelTraining;
using SmaTrendFollower.MachineLearning.Prediction;
using SmaTrendFollower.Models;
using SmaTrendFollower.Services;
using SmaTrendFollower.Tests.TestHelpers;
using System.Text.Json;
using Xunit;

namespace SmaTrendFollower.Tests.MachineLearning;

/// <summary>
/// Comprehensive tests for the slippage forecasting system including data export, model training, and prediction
/// </summary>
[Collection("Database")]
public class SlippageForecasterTests : IDisposable
{
    private readonly ServiceProvider _serviceProvider;
    private readonly IDbContextFactory<MLFeaturesDbContext> _dbContextFactory;
    private readonly IFeatureExportService _featureExportService;
    private readonly SlippageForecasterTrainer _trainer;
    private readonly ISlippageForecasterService _forecasterService;
    private readonly Mock<IOptimizedRedisConnectionService> _mockRedisService;
    private readonly string _testDatabasePath;

    public SlippageForecasterTests()
    {
        _testDatabasePath = Path.Combine(Path.GetTempPath(), $"test_ml_features_{Guid.NewGuid()}.db");
        
        var services = new ServiceCollection();
        
        // Add logging
        services.AddLogging(builder => builder.AddConsole().SetMinimumLevel(LogLevel.Debug));
        
        // Add test database
        services.AddDbContextFactory<MLFeaturesDbContext>(options =>
            options.UseSqlite($"Data Source={_testDatabasePath}"));
        
        // Add configuration
        var configuration = new ConfigurationBuilder()
            .AddInMemoryCollection(new Dictionary<string, string?>
            {
                ["SlippageTraining:ModelOutputPath"] = Path.Combine(Path.GetTempPath(), "test_slippage_model.zip"),
                ["SlippageTraining:NumberOfTrees"] = "50",
                ["SlippageTraining:NumberOfLeaves"] = "16",
                ["SlippageTraining:TrainingDataDays"] = "30"
            })
            .Build();
        services.AddSingleton<IConfiguration>(configuration);
        
        // Add services
        services.AddScoped<IFeatureExportService, FeatureExportService>();
        services.AddScoped<SlippageForecasterTrainer>();
        
        // Mock Redis service
        _mockRedisService = new Mock<IOptimizedRedisConnectionService>();
        services.AddSingleton(_mockRedisService.Object);
        
        // Add slippage forecaster service
        services.AddSingleton<ISlippageForecasterService, SlippageForecasterService>();
        
        _serviceProvider = services.BuildServiceProvider();
        
        // Get services
        _dbContextFactory = _serviceProvider.GetRequiredService<IDbContextFactory<MLFeaturesDbContext>>();
        _featureExportService = _serviceProvider.GetRequiredService<IFeatureExportService>();
        _trainer = _serviceProvider.GetRequiredService<SlippageForecasterTrainer>();
        _forecasterService = _serviceProvider.GetRequiredService<ISlippageForecasterService>();
        
        // Initialize database
        InitializeDatabaseAsync().Wait();
    }

    private async Task InitializeDatabaseAsync()
    {
        using var context = await _dbContextFactory.CreateDbContextAsync();
        await context.Database.EnsureCreatedAsync();
        
        // Seed test data
        await SeedTestFillDataAsync(context);
    }

    private async Task SeedTestFillDataAsync(MLFeaturesDbContext context)
    {
        var random = new Random(42); // Fixed seed for reproducible tests
        var baseTime = DateTime.UtcNow.AddDays(-30);
        
        for (int i = 0; i < 100; i++)
        {
            var fillTime = baseTime.AddHours(i * 6); // Every 6 hours
            var symbol = i % 3 == 0 ? "AAPL" : i % 3 == 1 ? "MSFT" : "GOOGL";
            var side = i % 2; // Alternate buy/sell
            
            var midPrice = 150m + (decimal)(random.NextDouble() * 50); // $150-200
            var spread = (decimal)(random.NextDouble() * 0.5); // 0-0.5% spread
            var fillPrice = side == 0 
                ? midPrice + spread/2 + (decimal)(random.NextDouble() * 0.1) // Buy: pay ask + slippage
                : midPrice - spread/2 - (decimal)(random.NextDouble() * 0.1); // Sell: receive bid - slippage
            
            var fillLog = new FillLog
            {
                TimeUtc = fillTime,
                Symbol = symbol,
                Side = side,
                Qty = 100m + (decimal)(random.NextDouble() * 900), // 100-1000 shares
                FillPrice = fillPrice,
                MidPrice = midPrice,
                SpreadPct = (float)(spread * 100),
                RankProb = 0.5f + (float)(random.NextDouble() * 0.4), // 0.5-0.9
                Regime = (float)random.NextDouble(), // 0-1
                ATR_Pct = 1f + (float)(random.NextDouble() * 4), // 1-5%
                VolumePct10d = 50f + (float)(random.NextDouble() * 100), // 50-150%
                CreatedAt = fillTime
            };
            
            context.FillsLog.Add(fillLog);
        }
        
        await context.SaveChangesAsync();
    }

    [Fact]
    public async Task FeatureExportService_ExportSlippageCsv_ShouldCreateValidCsvFile()
    {
        // Arrange
        var csvPath = Path.Combine(Path.GetTempPath(), $"test_slippage_{Guid.NewGuid()}.csv");
        var fromDate = DateTime.UtcNow.AddDays(-35);
        var toDate = DateTime.UtcNow.AddDays(-1);

        try
        {
            // Act
            await _featureExportService.ExportSlippageCsvAsync(csvPath, fromDate, toDate);

            // Assert
            File.Exists(csvPath).Should().BeTrue();
            
            var lines = await File.ReadAllLinesAsync(csvPath);
            lines.Should().NotBeEmpty();
            lines[0].Should().Contain("SpreadPct,RankProb,ATR_Pct,VolumePct10d,Regime,Side,Hour,Label");
            
            // Should have header + data rows
            lines.Length.Should().BeGreaterThan(1);
            
            // Verify data format
            var dataLine = lines[1].Split(',');
            dataLine.Length.Should().Be(8);
            
            // Verify numeric values can be parsed
            float.TryParse(dataLine[0], out _).Should().BeTrue(); // SpreadPct
            float.TryParse(dataLine[7], out _).Should().BeTrue(); // Label (slippage)
        }
        finally
        {
            if (File.Exists(csvPath))
                File.Delete(csvPath);
        }
    }

    [Fact]
    public async Task SlippageForecasterTrainer_TrainModelAsync_ShouldCreateModelFile()
    {
        // Arrange
        var modelPath = Path.Combine(Path.GetTempPath(), $"test_slippage_model_{Guid.NewGuid()}.zip");
        var csvPath = Path.Combine(Path.GetTempPath(), $"test_slippage_data_{Guid.NewGuid()}.csv");
        
        // First export training data
        await _featureExportService.ExportSlippageCsvAsync(csvPath, DateTime.UtcNow.AddDays(-35), DateTime.UtcNow.AddDays(-1));

        try
        {
            // Act
            var success = await _trainer.TrainModelAsync();

            // Assert
            success.Should().BeTrue();
            
            // Check if model file was created (using configured path)
            var configuredPath = _serviceProvider.GetRequiredService<IConfiguration>()["SlippageTraining:ModelOutputPath"];
            File.Exists(configuredPath).Should().BeTrue();
            
            // Check metadata file
            var metadataPath = Path.ChangeExtension(configuredPath, ".json");
            if (File.Exists(metadataPath))
            {
                var metadataJson = await File.ReadAllTextAsync(metadataPath);
                var metadata = JsonSerializer.Deserialize<SlippageModelMetadata>(metadataJson);
                metadata.Should().NotBeNull();
                metadata!.TrainingSamples.Should().BeGreaterThan(0);
                metadata.MeanAbsoluteError.Should().BeGreaterThan(0);
            }
        }
        finally
        {
            // Cleanup
            if (File.Exists(csvPath)) File.Delete(csvPath);
            if (File.Exists(modelPath)) File.Delete(modelPath);
            
            var configuredPath = _serviceProvider.GetRequiredService<IConfiguration>()["SlippageTraining:ModelOutputPath"];
            if (File.Exists(configuredPath)) File.Delete(configuredPath);
            
            var metadataPath = Path.ChangeExtension(configuredPath, ".json");
            if (File.Exists(metadataPath)) File.Delete(metadataPath);
        }
    }

    [Fact]
    public void SlippageForecasterService_PredictBps_ShouldReturnReasonableValues()
    {
        // Arrange
        var features = new SlippageSignalFeatures(
            Symbol: "AAPL",
            RankProb: 0.75f,
            ATR_Pct: 2.5f,
            VolumePct10d: 120f,
            Regime: 0.6f,
            Side: SmaOrderSide.Buy
        );
        
        var quote = new QuoteContext(
            MidPrice: 150m,
            SpreadPct: 0.1f,
            TimestampUtc: DateTime.UtcNow
        );

        // Act
        var slippageBps = _forecasterService.PredictBps(features, quote);

        // Assert
        slippageBps.Should().BeInRange(-50f, 50f); // Reasonable slippage range
        
        // Service should handle prediction even without trained model (fallback)
        slippageBps.Should().NotBe(0f);
    }

    [Fact]
    public void SlippageForecasterService_IsReady_ShouldIndicateServiceState()
    {
        // Act & Assert
        // Service might not be ready initially without a trained model
        _forecasterService.IsReady.Should().BeFalse();
        _forecasterService.ModelVersion.Should().Be(0);
    }

    [Fact]
    public async Task SlippageForecasterService_ReloadModelAsync_ShouldUpdateModelVersion()
    {
        // Arrange
        // First train a model
        await _trainer.TrainModelAsync();

        // Act
        await _forecasterService.ReloadModelAsync();

        // Assert
        // After reload, service should be ready if model exists
        var configuredPath = _serviceProvider.GetRequiredService<IConfiguration>()["SlippageTraining:ModelOutputPath"];
        if (File.Exists(configuredPath))
        {
            _forecasterService.IsReady.Should().BeTrue();
        }
    }

    [Fact]
    public async Task SlippageForecaster_EndToEndWorkflow_ShouldWorkCorrectly()
    {
        // Arrange
        var csvPath = Path.Combine(Path.GetTempPath(), $"e2e_slippage_{Guid.NewGuid()}.csv");

        try
        {
            // Step 1: Export training data
            await _featureExportService.ExportSlippageCsvAsync(csvPath, DateTime.UtcNow.AddDays(-35), DateTime.UtcNow.AddDays(-1));
            File.Exists(csvPath).Should().BeTrue();

            // Step 2: Train model
            var trainSuccess = await _trainer.TrainModelAsync();
            trainSuccess.Should().BeTrue();

            // Step 3: Reload model in forecaster service
            await _forecasterService.ReloadModelAsync();

            // Step 4: Make predictions
            var features = new SlippageSignalFeatures(
                Symbol: "AAPL",
                RankProb: 0.8f,
                ATR_Pct: 3.0f,
                VolumePct10d: 110f,
                Regime: 0.7f,
                Side: SmaOrderSide.Buy
            );

            var quote = new QuoteContext(
                MidPrice: 175m,
                SpreadPct: 0.15f,
                TimestampUtc: DateTime.UtcNow
            );

            // Act
            var prediction1 = _forecasterService.PredictBps(features, quote);
            var prediction2 = _forecasterService.PredictBps(features, quote);

            // Assert
            prediction1.Should().BeInRange(-50f, 50f);
            prediction2.Should().Be(prediction1); // Should be deterministic

            // Test different inputs produce different outputs
            var differentFeatures = features with { RankProb = 0.3f, ATR_Pct = 1.0f };
            var prediction3 = _forecasterService.PredictBps(differentFeatures, quote);
            prediction3.Should().NotBe(prediction1); // Different inputs should give different predictions
        }
        finally
        {
            if (File.Exists(csvPath)) File.Delete(csvPath);
        }
    }

    public void Dispose()
    {
        _serviceProvider?.Dispose();

        if (File.Exists(_testDatabasePath))
        {
            File.Delete(_testDatabasePath);
        }

        // Cleanup any test model files
        var configuredPath = _serviceProvider?.GetRequiredService<IConfiguration>()["SlippageTraining:ModelOutputPath"];
        if (!string.IsNullOrEmpty(configuredPath) && File.Exists(configuredPath))
        {
            File.Delete(configuredPath);
        }
    }
}

/// <summary>
/// Test data for slippage model metadata
/// </summary>
public record SlippageModelMetadata
{
    public DateTime TrainingDate { get; init; }
    public int TrainingSamples { get; init; }
    public float MeanAbsoluteError { get; init; }
    public float RootMeanSquaredError { get; init; }
    public float RSquared { get; init; }
    public int NumberOfTrees { get; init; }
    public int NumberOfLeaves { get; init; }
    public int TrainingDataDays { get; init; }
}
