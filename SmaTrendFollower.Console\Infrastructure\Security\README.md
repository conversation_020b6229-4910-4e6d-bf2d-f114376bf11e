# HashiCorp Vault Integration

This document describes the HashiCorp Vault KV v2 integration for secure secrets management in the SmaTrendFollower system.

## Overview

The SmaTrendFollower system supports two secret providers:

1. **VaultSecretProvider** - Production-ready HashiCorp Vault KV v2 integration
2. **EnvSecretProvider** - Development fallback using environment variables

The system automatically selects the appropriate provider based on environment configuration, ensuring seamless operation in both development and production environments.

## Architecture

### ISecretProvider Interface

```csharp
public interface ISecretProvider
{
    string Get(string key);                    // Throws if not found
    bool TryGet(string key, out string? value); // Safe retrieval
    bool Exists(string key);                   // Check existence
    string ProviderName { get; }               // Provider identification
    bool IsConfigured { get; }                 // Configuration status
}
```

### Provider Selection Logic

The system uses the following logic to select a secret provider:

1. Check for `VAULT_ADDR` and `VAULT_TOKEN` environment variables
2. If both are present and valid, attempt to initialize `VaultSecretProvider`
3. If Vault initialization fails or variables are missing, fallback to `EnvSecretProvider`

## Configuration

### Environment Variables

#### Vault Configuration (Production)
```bash
VAULT_ADDR=http://vault:8200          # Vault server address
VAULT_TOKEN=hvs.PASTE_YOUR_TOKEN      # Vault authentication token
```

#### Development Configuration
When Vault variables are not set, the system falls back to standard environment variables:
```bash
POLYGON_API_KEY=your_polygon_key
ALPACA_KEY_ID=your_alpaca_key
ALPACA_SECRET=your_alpaca_secret
OPENAI_API_KEY=your_openai_key
DISCORD_BOT_TOKEN=your_discord_token
DISCORD_CHANNEL_ID=your_channel_id
REDIS_URL=redis://localhost:6379
REDIS_PASSWORD=your_redis_password
```

### Vault KV v2 Setup

#### 1. Enable KV v2 Engine (if not already enabled)
```bash
vault secrets enable -path=secret kv-v2
```

#### 2. Store Secrets in Vault
```bash
vault kv put secret/data/sma \
    POLYGON_API_KEY=your_polygon_key \
    ALPACA_KEY_ID=your_alpaca_key \
    ALPACA_SECRET=your_alpaca_secret \
    OPENAI_API_KEY=your_openai_key \
    DISCORD_BOT_TOKEN=your_discord_token \
    DISCORD_CHANNEL_ID=your_channel_id \
    REDIS_URL=redis://localhost:6379 \
    REDIS_PASSWORD=your_redis_password
```

#### 3. Verify Secrets
```bash
vault kv get secret/data/sma
```

#### 4. Create Vault Policy (Optional)
```bash
# Create policy file: sma-policy.hcl
path "secret/data/sma" {
  capabilities = ["read"]
}

# Apply policy
vault policy write sma-policy sma-policy.hcl

# Create token with policy
vault token create -policy=sma-policy
```

## Docker Compose Integration

### Production Configuration
```yaml
version: '3.8'
services:
  vault:
    image: vault:latest
    ports:
      - "8200:8200"
    environment:
      VAULT_DEV_ROOT_TOKEN_ID: myroot
      VAULT_DEV_LISTEN_ADDRESS: 0.0.0.0:8200
    cap_add:
      - IPC_LOCK

  sma-trader:
    build: .
    environment:
      VAULT_ADDR: "http://vault:8200"
      VAULT_TOKEN: "hvs.PASTE_YOUR_TOKEN"
    depends_on:
      - vault
```

### Development Configuration
```yaml
version: '3.8'
services:
  sma-trader:
    build: .
    environment:
      # No VAULT_ADDR/VAULT_TOKEN - uses environment variables
      POLYGON_API_KEY: "your_polygon_key"
      ALPACA_KEY_ID: "your_alpaca_key"
      ALPACA_SECRET: "your_alpaca_secret"
      # ... other secrets
```

## Usage Examples

### Basic Usage
```csharp
// Automatic provider selection
var secrets = ServiceConfiguration.CreateSecretProvider(logger);

// Retrieve secrets
var polygonKey = secrets.Get("POLYGON_API_KEY");
var alpacaKey = secrets.Get("ALPACA_KEY_ID");

// Safe retrieval
if (secrets.TryGet("OPTIONAL_SECRET", out var optionalValue))
{
    // Use optional value
}

// Check existence
if (secrets.Exists("DISCORD_BOT_TOKEN"))
{
    var discordToken = secrets.Get("DISCORD_BOT_TOKEN");
}
```

### Dependency Injection
```csharp
// In Program.cs
var secrets = ServiceConfiguration.CreateSecretProvider(logger);
services.AddSingleton<ISecretProvider>(secrets);

// In service constructor
public class TradingService
{
    private readonly ISecretProvider _secrets;
    
    public TradingService(ISecretProvider secrets)
    {
        _secrets = secrets;
    }
    
    public void Initialize()
    {
        var apiKey = _secrets.Get("POLYGON_API_KEY");
        // Use API key...
    }
}
```

## Security Best Practices

### Production Deployment
1. **Never store secrets in code or configuration files**
2. **Use Vault with proper authentication (not dev mode)**
3. **Implement token rotation policies**
4. **Use least-privilege access policies**
5. **Enable Vault audit logging**
6. **Use TLS for Vault communication**

### Development Environment
1. **Use `.env` files for local development (not committed to git)**
2. **Use different secrets for development and production**
3. **Regularly rotate development secrets**

### Token Management
```bash
# Create limited-time token
vault token create -ttl=24h -policy=sma-policy

# Renew token
vault token renew

# Revoke token
vault token revoke <token>
```

## Monitoring and Troubleshooting

### Logging
The secret providers include comprehensive logging:
- Provider selection decisions
- Secret retrieval attempts
- Error conditions and fallbacks
- Cache operations (Vault provider)

### Common Issues

#### Vault Connection Failures
```
Error: Failed to initialize Vault client: connection refused
Solution: Check VAULT_ADDR and ensure Vault is running
```

#### Authentication Failures
```
Error: permission denied
Solution: Verify VAULT_TOKEN has correct permissions
```

#### Secret Not Found
```
Error: Secret key 'API_KEY' not found in Vault
Solution: Verify secret exists in Vault at correct path
```

### Health Checks
```csharp
// Check provider status
if (!secrets.IsConfigured)
{
    logger.LogError("Secret provider is not properly configured");
}

// Test secret retrieval
try
{
    var testSecret = secrets.Get("REQUIRED_SECRET");
    logger.LogInformation("Secret provider is working correctly");
}
catch (Exception ex)
{
    logger.LogError(ex, "Secret provider test failed");
}
```

## Migration Guide

### From Environment Variables to Vault

1. **Set up Vault server and authentication**
2. **Store existing secrets in Vault KV store**
3. **Set VAULT_ADDR and VAULT_TOKEN environment variables**
4. **Remove individual secret environment variables**
5. **Test application startup and secret retrieval**
6. **Monitor logs for any fallback to environment variables**

### Rollback Strategy
If Vault integration fails, the system automatically falls back to environment variables, ensuring zero-downtime deployment.

## Testing

### Unit Tests
```bash
# Run secret provider tests
dotnet test --filter "Category=Security"

# Run vault fallback tests
dotnet test --filter "FullyQualifiedName~VaultFallbackTests"
```

### Integration Tests
```bash
# Test with real Vault instance (requires Vault setup)
VAULT_ADDR=http://localhost:8200 VAULT_TOKEN=mytoken dotnet test
```

## Performance Considerations

### Vault Provider
- **Caching**: Retrieved secrets are cached in memory
- **Connection Pooling**: VaultSharp handles HTTP connection pooling
- **Retry Logic**: Built-in retry for transient failures

### Environment Provider
- **No Caching**: Direct environment variable access
- **Minimal Overhead**: Fastest secret retrieval method

## Support and Maintenance

### Regular Tasks
1. **Monitor Vault token expiration**
2. **Rotate secrets periodically**
3. **Review access logs**
4. **Update Vault policies as needed**
5. **Test disaster recovery procedures**

### Backup and Recovery
- **Vault Snapshots**: Regular automated snapshots
- **Secret Backup**: Encrypted backup of critical secrets
- **Recovery Testing**: Regular recovery procedure validation

## Quick Start Guide

### For Development (Environment Variables)
1. Create `.env` file in project root:
```bash
POLYGON_API_KEY=your_polygon_key
ALPACA_KEY_ID=your_alpaca_key
ALPACA_SECRET=your_alpaca_secret
DISCORD_BOT_TOKEN=your_discord_token
DISCORD_CHANNEL_ID=your_channel_id
```

2. Run the application:
```bash
dotnet run --project SmaTrendFollower.Console
```

The system will automatically use the EnvSecretProvider.

### For Production (HashiCorp Vault)
1. Set up Vault server and store secrets:
```bash
vault kv put secret/data/sma \
    POLYGON_API_KEY=your_polygon_key \
    ALPACA_KEY_ID=your_alpaca_key \
    ALPACA_SECRET=your_alpaca_secret
```

2. Set environment variables:
```bash
export VAULT_ADDR=http://vault:8200
export VAULT_TOKEN=hvs.your_token
```

3. Run the application:
```bash
dotnet run --project SmaTrendFollower.Console
```

The system will automatically use the VaultSecretProvider.
