groups:
- name: sma_trend_alerts
  rules:
  - alert: BotDown
    expr: up{job="sma_trend_follower"} == 0
    for: 2m
    labels:
      severity: critical
      service: sma-trend-follower
    annotations:
      summary: "SmaTrendFollower trading bot is down"
      description: "The SmaTrendFollower trading bot has been down for more than 2 minutes. Check the application logs and restart if necessary."
      runbook_url: "https://github.com/patco1/SmaTrendFollower/blob/main/docs/runbooks/bot-down.md"

  - alert: HighDrawdown
    expr: daily_pnl_usd < -5000
    for: 5m
    labels:
      severity: warning
      service: sma-trend-follower
    annotations:
      summary: "High drawdown detected in trading bot"
      description: "Daily P&L has dropped below -$5,000 (current: ${{ $value }}). Consider reviewing trading strategy or halting operations."
      runbook_url: "https://github.com/patco1/SmaTrendFollower/blob/main/docs/runbooks/high-drawdown.md"

  - alert: HighSlippage
    expr: rate(slippage_actual_bps_sum[15m]) / rate(slippage_actual_bps_count[15m]) * 10000 > 10
    for: 10m
    labels:
      severity: warning
      service: sma-trend-follower
    annotations:
      summary: "High execution slippage detected"
      description: "Average slippage over the last 15 minutes is {{ $value | humanize }}bps, which exceeds the 10bps threshold."
      runbook_url: "https://github.com/patco1/SmaTrendFollower/blob/main/docs/runbooks/high-slippage.md"

  - alert: SignalGenerationLatency
    expr: histogram_quantile(0.95, rate(signal_latency_ms_bucket[5m])) > 30000
    for: 5m
    labels:
      severity: warning
      service: sma-trend-follower
    annotations:
      summary: "Signal generation latency is high"
      description: "95th percentile signal generation latency is {{ $value | humanize }}ms, exceeding the 30-second threshold."
      runbook_url: "https://github.com/patco1/SmaTrendFollower/blob/main/docs/runbooks/signal-latency.md"

  - alert: HighErrorRate
    expr: rate(application_errors_total[5m]) > 0.1
    for: 3m
    labels:
      severity: critical
      service: sma-trend-follower
    annotations:
      summary: "High application error rate"
      description: "Application error rate is {{ $value | humanize }} errors/minute, exceeding the 0.1/min threshold."
      runbook_url: "https://github.com/patco1/SmaTrendFollower/blob/main/docs/runbooks/high-error-rate.md"

  - alert: OrderFailureRate
    expr: rate(order_failures_total[5m]) / (rate(trades_total[5m]) + rate(order_failures_total[5m])) > 0.05
    for: 5m
    labels:
      severity: warning
      service: sma-trend-follower
    annotations:
      summary: "High order failure rate"
      description: "Order failure rate is {{ $value | humanizePercentage }}, exceeding the 5% threshold."
      runbook_url: "https://github.com/patco1/SmaTrendFollower/blob/main/docs/runbooks/order-failures.md"

  - alert: WebSocketReconnects
    expr: rate(websocket_reconnect_total[5m]) > 0.5
    for: 2m
    labels:
      severity: warning
      service: sma-trend-follower
    annotations:
      summary: "Frequent WebSocket reconnections"
      description: "WebSocket reconnection rate is {{ $value | humanize }}/minute, indicating potential connectivity issues."
      runbook_url: "https://github.com/patco1/SmaTrendFollower/blob/main/docs/runbooks/websocket-issues.md"

  - alert: UniverseSizeDropped
    expr: universe_size < 50
    for: 5m
    labels:
      severity: warning
      service: sma-trend-follower
    annotations:
      summary: "Trading universe size has dropped significantly"
      description: "Current universe size is {{ $value }} symbols, which is below the expected minimum of 50."
      runbook_url: "https://github.com/patco1/SmaTrendFollower/blob/main/docs/runbooks/universe-size.md"

  - alert: HighVIX
    expr: current_vix > 35
    for: 10m
    labels:
      severity: warning
      service: sma-trend-follower
    annotations:
      summary: "VIX level is elevated"
      description: "Current VIX level is {{ $value }}, indicating high market volatility. Consider reducing position sizes."
      runbook_url: "https://github.com/patco1/SmaTrendFollower/blob/main/docs/runbooks/high-vix.md"

  - alert: TooManyPositions
    expr: current_positions > 10
    for: 1m
    labels:
      severity: warning
      service: sma-trend-follower
    annotations:
      summary: "Position count exceeds maximum"
      description: "Current position count is {{ $value }}, exceeding the maximum of 10 positions."
      runbook_url: "https://github.com/patco1/SmaTrendFollower/blob/main/docs/runbooks/position-limits.md"
