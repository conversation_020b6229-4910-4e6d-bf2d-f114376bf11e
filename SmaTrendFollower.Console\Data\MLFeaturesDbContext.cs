using Microsoft.EntityFrameworkCore;
using SmaTrendFollower.Models;

namespace SmaTrendFollower.Data;

/// <summary>
/// Database context for ML features and training data.
/// Manages the Features table used for machine learning model training.
/// </summary>
public class MLFeaturesDbContext : DbContext
{
    public MLFeaturesDbContext(DbContextOptions<MLFeaturesDbContext> options) : base(options)
    {
    }

    /// <summary>
    /// Signal features with forward returns for ML training
    /// </summary>
    public DbSet<SignalFeature> Features { get; set; } = null!;

    /// <summary>
    /// Order fill logs for slippage analysis and prediction
    /// </summary>
    public DbSet<FillLog> FillsLog { get; set; } = null!;

    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        base.OnModelCreating(modelBuilder);

        // Configure SignalFeature entity
        modelBuilder.Entity<SignalFeature>(entity =>
        {
            // Composite unique index for efficient queries and deduplication
            entity.HasIndex(e => new { e.Symbol, e.Date })
                  .IsUnique()
                  .HasDatabaseName("IX_Features_Symbol_Date");

            // Additional indexes for common query patterns
            entity.HasIndex(e => e.Date)
                  .HasDatabaseName("IX_Features_Date");

            entity.HasIndex(e => e.Symbol)
                  .HasDatabaseName("IX_Features_Symbol");

            entity.HasIndex(e => e.CreatedAt)
                  .HasDatabaseName("IX_Features_CreatedAt");

            // Index for ML training queries (filtering by forward returns)
            entity.HasIndex(e => e.Forward3DReturn)
                  .HasDatabaseName("IX_Features_Forward3DReturn");

            // Composite index for date range queries with symbol
            entity.HasIndex(e => new { e.Symbol, e.Date, e.Forward3DReturn })
                  .HasDatabaseName("IX_Features_Symbol_Date_Forward3DReturn");
        });

        // Configure FillLog entity
        modelBuilder.Entity<FillLog>(entity =>
        {
            // Composite index for efficient queries by symbol and time
            entity.HasIndex(e => new { e.Symbol, e.TimeUtc })
                  .HasDatabaseName("IX_FillsLog_Symbol_TimeUtc");

            // Additional indexes for common query patterns
            entity.HasIndex(e => e.TimeUtc)
                  .HasDatabaseName("IX_FillsLog_TimeUtc");

            entity.HasIndex(e => e.Symbol)
                  .HasDatabaseName("IX_FillsLog_Symbol");

            entity.HasIndex(e => e.CreatedAt)
                  .HasDatabaseName("IX_FillsLog_CreatedAt");

            // Index for slippage analysis queries
            entity.HasIndex(e => new { e.Symbol, e.Side, e.TimeUtc })
                  .HasDatabaseName("IX_FillsLog_Symbol_Side_TimeUtc");
        });
    }

    protected override void OnConfiguring(DbContextOptionsBuilder optionsBuilder)
    {
        if (!optionsBuilder.IsConfigured)
        {
            // Default configuration for development
            optionsBuilder.UseSqlite("Data Source=ml_features.db");
        }

        // Performance optimizations
        optionsBuilder.EnableSensitiveDataLogging(false); // Disable in production
        optionsBuilder.EnableServiceProviderCaching(true);
        optionsBuilder.EnableDetailedErrors(false); // Disable in production for performance
    }
}

/// <summary>
/// Extension methods for MLFeaturesDbContext
/// </summary>
public static class MLFeaturesDbContextExtensions
{
    /// <summary>
    /// Gets features for training within a date range
    /// </summary>
    public static IQueryable<SignalFeature> GetTrainingFeatures(
        this MLFeaturesDbContext context,
        DateTime fromDate,
        DateTime toDate)
    {
        return context.Features
            .Where(f => f.Date >= fromDate && f.Date <= toDate)
            .OrderBy(f => f.Date);
    }

    /// <summary>
    /// Gets features for a specific symbol within a date range
    /// </summary>
    public static IQueryable<SignalFeature> GetSymbolFeatures(
        this MLFeaturesDbContext context,
        string symbol,
        DateTime fromDate,
        DateTime toDate)
    {
        return context.Features
            .Where(f => f.Symbol == symbol && f.Date >= fromDate && f.Date <= toDate)
            .OrderBy(f => f.Date);
    }

    /// <summary>
    /// Gets the latest feature record for each symbol
    /// </summary>
    public static IQueryable<SignalFeature> GetLatestFeatures(this MLFeaturesDbContext context)
    {
        return context.Features
            .GroupBy(f => f.Symbol)
            .Select(g => g.OrderByDescending(f => f.Date).First());
    }

    /// <summary>
    /// Counts features by outcome (win/loss) for model evaluation
    /// </summary>
    public static async Task<(int Wins, int Losses, double WinRate)> GetOutcomeStatsAsync(
        this MLFeaturesDbContext context,
        float winThreshold = 0.01f)
    {
        var total = await context.Features.CountAsync();
        if (total == 0) return (0, 0, 0.0);

        var wins = await context.Features.CountAsync(f => f.Forward3DReturn >= winThreshold);
        var losses = total - wins;
        var winRate = (double)wins / total;

        return (wins, losses, winRate);
    }

    /// <summary>
    /// Gets feature statistics for data quality analysis
    /// </summary>
    public static async Task<Dictionary<string, (double Mean, double StdDev, double Min, double Max)>> 
        GetFeatureStatsAsync(this MLFeaturesDbContext context)
    {
        var features = await context.Features.ToListAsync();
        if (!features.Any()) return new Dictionary<string, (double, double, double, double)>();

        var stats = new Dictionary<string, (double Mean, double StdDev, double Min, double Max)>();

        // Calculate statistics for each numeric feature
        var featureProperties = new[]
        {
            (nameof(SignalFeature.SmaGap), features.Select(f => (double)f.SmaGap)),
            (nameof(SignalFeature.Volatility), features.Select(f => (double)f.Volatility)),
            (nameof(SignalFeature.Rsi), features.Select(f => (double)f.Rsi)),
            (nameof(SignalFeature.BreadthScore), features.Select(f => (double)f.BreadthScore)),
            (nameof(SignalFeature.VixLevel), features.Select(f => (double)f.VixLevel)),
            (nameof(SignalFeature.SixMonthReturn), features.Select(f => (double)f.SixMonthReturn)),
            (nameof(SignalFeature.RelativeVolume), features.Select(f => (double)f.RelativeVolume)),
            (nameof(SignalFeature.MarketRegime), features.Select(f => (double)f.MarketRegime)),
            (nameof(SignalFeature.Forward3DReturn), features.Select(f => (double)f.Forward3DReturn))
        };

        foreach (var (name, values) in featureProperties)
        {
            var valueList = values.ToList();
            var mean = valueList.Average();
            var variance = valueList.Select(v => Math.Pow(v - mean, 2)).Average();
            var stdDev = Math.Sqrt(variance);
            var min = valueList.Min();
            var max = valueList.Max();

            stats[name] = (mean, stdDev, min, max);
        }

        return stats;
    }
}
