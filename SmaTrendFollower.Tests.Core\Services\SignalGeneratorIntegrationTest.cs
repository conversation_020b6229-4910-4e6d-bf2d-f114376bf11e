using FluentAssertions;
using Microsoft.Extensions.Logging;
using NSubstitute;
using SmaTrendFollower.Models;
using SmaTrendFollower.Services;
using Alpaca.Markets;

namespace SmaTrendFollower.Tests.Core.Services;

public class SignalGeneratorIntegrationTest
{
    [Fact]
    public async Task SignalGenerator_WithRealData_ShouldWork()
    {
        // Arrange
        var marketDataService = Substitute.For<IMarketDataService>();
        var universeProvider = Substitute.For<IUniverseProvider>();
        var logger = Substitute.For<ILogger<SignalGenerator>>();

        // Create realistic mock data
        var bars = CreateRealisticBars();
        var mockPage = CreateMockPage(bars);

        universeProvider.GetSymbolsAsync().Returns(new[] { "TEST" });
        marketDataService.GetStockBarsAsync(Arg.Any<string>(), Arg.Any<DateTime>(), Arg.Any<DateTime>())
            .Returns(mockPage);

        var signalGenerator = new SignalGenerator(marketDataService, universeProvider, logger);

        // Act
        var result = await signalGenerator.RunAsync(10);

        // Assert
        // The test should not throw exceptions and should return some result
        result.Should().NotBeNull();
        
        // Log the result for debugging
        System.Console.WriteLine($"Generated {result.Count()} signals");
        foreach (var signal in result)
        {
            System.Console.WriteLine($"Signal: {signal.Symbol}, Price: {signal.Price:F2}, ATR: {signal.Atr:F2}, Momentum: {signal.Momentum:F2}");
        }
    }

    private static List<IBar> CreateRealisticBars()
    {
        var bars = new List<IBar>();
        var baseDate = DateTime.UtcNow.AddDays(-250);
        var basePrice = 50m;

        // Create a strong uptrend that should satisfy all conditions
        for (int i = 0; i < 250; i++)
        {
            var bar = Substitute.For<IBar>();
            
            // Strong uptrend: price increases by ~1% per day on average
            var trendGrowth = (decimal)Math.Pow(1.01, i);
            var price = basePrice * trendGrowth;
            
            // Add some realistic volatility
            var volatility = price * 0.02m * (decimal)(Math.Sin(i * 0.3) * 0.5 + 0.5);
            
            bar.TimeUtc.Returns(baseDate.AddDays(i));
            bar.Open.Returns(price - volatility);
            bar.High.Returns(price + volatility);
            bar.Low.Returns(price - volatility * 1.5m);
            bar.Close.Returns(price);
            bar.Volume.Returns((ulong)(1000000 + i * 50000)); // Increasing volume

            bars.Add(bar);
        }

        return bars;
    }

    private static IPage<IBar> CreateMockPage(List<IBar> bars)
    {
        var page = Substitute.For<IPage<IBar>>();
        page.Items.Returns(bars);
        page.NextPageToken.Returns((string?)null);
        return page;
    }
}
