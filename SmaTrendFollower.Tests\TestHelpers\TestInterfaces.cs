using Alpaca.Markets;
using SmaTrendFollower.Services;

namespace SmaTrendFollower.Tests.TestHelpers;

/// <summary>
/// Interface for index bar data used in testing.
/// Provides a mockable interface for IndexBar struct.
/// </summary>
public interface IIndexBar
{
    DateTime Timestamp { get; }
    decimal Open { get; }
    decimal High { get; }
    decimal Low { get; }
    decimal Close { get; }
    long Volume { get; }
}

/// <summary>
/// Wrapper that implements IIndexBar interface for IndexBar compatibility in tests.
/// </summary>
public class IndexBarTestWrapper : IIndexBar
{
    private readonly IndexBar _indexBar;

    public IndexBarTestWrapper(IndexBar indexBar)
    {
        _indexBar = indexBar;
    }

    public DateTime Timestamp => _indexBar.TimeUtc;
    public decimal Open => _indexBar.Open;
    public decimal High => _indexBar.High;
    public decimal Low => _indexBar.Low;
    public decimal Close => _indexBar.Close;
    public long Volume => _indexBar.Volume;
}

/// <summary>
/// Helper methods for test mocking
/// </summary>
public static class TestMockHelpers
{
    /// <summary>
    /// Creates IndexBar instances from IIndexBar mocks for ReturnsAsync compatibility
    /// </summary>
    public static IEnumerable<IndexBar> CreateIndexBars(params (DateTime timestamp, decimal open, decimal high, decimal low, decimal close, long volume)[] barData)
    {
        return barData.Select(data => new IndexBar(data.timestamp, data.open, data.high, data.low, data.close, data.volume));
    }

    /// <summary>
    /// Creates a single IndexBar for testing
    /// </summary>
    public static IndexBar CreateIndexBar(DateTime timestamp, decimal open, decimal high, decimal low, decimal close, long volume = 1000000)
    {
        return new IndexBar(timestamp, open, high, low, close, volume);
    }

    /// <summary>
    /// Creates VIX test data
    /// </summary>
    public static IEnumerable<IndexBar> CreateVixBars(decimal vixLevel = 20m, int count = 5)
    {
        var bars = new List<IndexBar>();
        for (int i = 0; i < count; i++)
        {
            bars.Add(new IndexBar(
                DateTime.UtcNow.AddDays(-i),
                vixLevel - 0.5m,
                vixLevel + 1.0m,
                vixLevel - 1.0m,
                vixLevel + (i % 3 - 1) * 0.5m,
                500000 + i * 10000
            ));
        }
        return bars.OrderBy(b => b.TimeUtc);
    }

    /// <summary>
    /// Creates SPX test data
    /// </summary>
    public static IEnumerable<IndexBar> CreateSpxBars(decimal momentum = 0.5m, int count = 10)
    {
        var basePrice = 4500m;
        var bars = new List<IndexBar>();
        for (int i = 0; i < count; i++)
        {
            var price = basePrice + (i * momentum * 10);
            bars.Add(new IndexBar(
                DateTime.UtcNow.AddDays(-i),
                price - 5,
                price + 10,
                price - 10,
                price,
                1000000 + i * 50000
            ));
        }
        return bars.OrderBy(b => b.TimeUtc);
    }

    /// <summary>
    /// Creates NDX test data
    /// </summary>
    public static IEnumerable<IndexBar> CreateNdxBars(decimal momentum = 0.5m, int count = 10)
    {
        var basePrice = 15000m;
        var bars = new List<IndexBar>();
        for (int i = 0; i < count; i++)
        {
            var price = basePrice + (i * momentum * 50);
            bars.Add(new IndexBar(
                DateTime.UtcNow.AddDays(-i),
                price - 25,
                price + 50,
                price - 50,
                price,
                800000 + i * 40000
            ));
        }
        return bars.OrderBy(b => b.TimeUtc);
    }
}
