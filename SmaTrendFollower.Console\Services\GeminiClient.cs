using System.Text.Json;
using System.Net.Http.Json;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;

namespace SmaTrendFollower.Services;

/// <summary>
/// Interface for Gemini AI client for sentiment analysis
/// </summary>
public interface IGeminiClient
{
    /// <summary>
    /// Gets sentiment score from Gemini AI for the given text
    /// </summary>
    /// <param name="text">Text to analyze</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Sentiment score between -1 (very negative) and +1 (very positive), or null if failed</returns>
    Task<double?> GetSentimentAsync(string text, CancellationToken cancellationToken = default);
}

/// <summary>
/// Gemini AI client for sentiment analysis
/// </summary>
public sealed class GeminiClient : IGeminiClient
{
    private readonly HttpClient _httpClient;
    private readonly string _apiKey;
    private readonly ILogger<GeminiClient> _logger;

    public GeminiClient(
        IConfiguration configuration,
        HttpClient httpClient,
        ILogger<GeminiClient> logger)
    {
        _httpClient = httpClient;
        _logger = logger;

        // Get API key from environment variable
        var apiKeyEnv = configuration["Gemini:ApiKeyEnv"] ?? "GEMINI_API_KEY";
        _apiKey = Environment.GetEnvironmentVariable(apiKeyEnv) 
                  ?? throw new InvalidOperationException($"Environment variable {apiKeyEnv} not found");

        // Configure HttpClient
        var endpoint = configuration["Gemini:Endpoint"] 
                      ?? "https://generativelanguage.googleapis.com/v1beta/models/gemini-pro:generateContent";
        _httpClient.BaseAddress = new Uri(endpoint);
        
        var timeoutSec = configuration.GetValue<int>("Gemini:TimeoutSec", 45);
        _httpClient.Timeout = TimeSpan.FromSeconds(timeoutSec);

        _logger.LogInformation("GeminiClient initialized with endpoint: {Endpoint}, timeout: {Timeout}s", 
            endpoint, timeoutSec);
    }

    public async Task<double?> GetSentimentAsync(string text, CancellationToken cancellationToken = default)
    {
        try
        {
            // Create the prompt for sentiment analysis
            var prompt = $"Analyze the sentiment of this financial news headline and return only a single number between -1 and +1, where -1 is very negative, 0 is neutral, and +1 is very positive. Do not include any explanation, just the number: \"{text}\"";

            // Create the request payload
            var payload = new
            {
                contents = new[]
                {
                    new
                    {
                        parts = new[]
                        {
                            new { text = prompt }
                        }
                    }
                }
            };

            // Make the API call
            var response = await _httpClient.PostAsJsonAsync($"?key={_apiKey}", payload, cancellationToken);

            if (!response.IsSuccessStatusCode)
            {
                _logger.LogWarning("Gemini API call failed with status {StatusCode}: {ReasonPhrase}", 
                    response.StatusCode, response.ReasonPhrase);
                return null;
            }

            // Parse the response
            var responseContent = await response.Content.ReadAsStringAsync(cancellationToken);
            var jsonDocument = JsonDocument.Parse(responseContent);

            if (!jsonDocument.RootElement.TryGetProperty("candidates", out var candidates) ||
                candidates.GetArrayLength() == 0)
            {
                _logger.LogWarning("No candidates found in Gemini response");
                return null;
            }

            var firstCandidate = candidates[0];
            if (!firstCandidate.TryGetProperty("content", out var content) ||
                !content.TryGetProperty("parts", out var parts) ||
                parts.GetArrayLength() == 0)
            {
                _logger.LogWarning("Invalid response structure from Gemini API");
                return null;
            }

            var firstPart = parts[0];
            if (!firstPart.TryGetProperty("text", out var textElement))
            {
                _logger.LogWarning("No text found in Gemini response");
                return null;
            }

            var scoreText = textElement.GetString()?.Trim();
            if (string.IsNullOrEmpty(scoreText))
            {
                _logger.LogWarning("Empty text response from Gemini API");
                return null;
            }

            // Try to parse the sentiment score
            if (double.TryParse(scoreText, out var score))
            {
                // Clamp the score to [-1, 1] range
                score = Math.Max(-1.0, Math.Min(1.0, score));
                _logger.LogDebug("Sentiment analysis successful: {Text} -> {Score:F3}", 
                    text.Length > 50 ? text[..50] + "..." : text, score);
                return score;
            }

            _logger.LogWarning("Failed to parse sentiment score from Gemini response: {ScoreText}", scoreText);
            return null;
        }
        catch (OperationCanceledException)
        {
            _logger.LogDebug("Gemini API call was cancelled");
            return null;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error calling Gemini API for sentiment analysis");
            return null;
        }
    }
}
