namespace SmaTrendFollower.Infrastructure.Security;

/// <summary>
/// Interface for secure secret retrieval from various sources (HashiCorp Vault, environment variables, etc.)
/// Provides a unified abstraction for accessing sensitive configuration data in production and development environments.
/// </summary>
public interface ISecretProvider
{
    /// <summary>
    /// Retrieves a secret value by key.
    /// </summary>
    /// <param name="key">The secret key to retrieve</param>
    /// <returns>The secret value</returns>
    /// <exception cref="KeyNotFoundException">Thrown when the secret key is not found</exception>
    /// <exception cref="InvalidOperationException">Thrown when the secret provider is not properly configured</exception>
    string Get(string key);

    /// <summary>
    /// Attempts to retrieve a secret value by key.
    /// </summary>
    /// <param name="key">The secret key to retrieve</param>
    /// <param name="value">The secret value if found, null otherwise</param>
    /// <returns>True if the secret was found, false otherwise</returns>
    bool TryGet(string key, out string? value);

    /// <summary>
    /// Checks if a secret exists without retrieving its value.
    /// </summary>
    /// <param name="key">The secret key to check</param>
    /// <returns>True if the secret exists, false otherwise</returns>
    bool Exists(string key);

    /// <summary>
    /// Gets the name/type of the secret provider for logging and diagnostics.
    /// </summary>
    string ProviderName { get; }

    /// <summary>
    /// Indicates whether the provider is properly configured and ready to use.
    /// </summary>
    bool IsConfigured { get; }
}
