using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using SmaTrendFollower.Services;
using SmaTrendFollower.Configuration;

// Simple VIX test program
var builder = Host.CreateApplicationBuilder();

// Configure logging
builder.Logging.ClearProviders();
builder.Logging.AddConsole();
builder.Logging.SetMinimumLevel(LogLevel.Information);

// Configure services
builder.Services.AddFullTradingSystem(builder.Configuration);

var host = builder.Build();

var logger = host.Services.GetRequiredService<ILogger<Program>>();

try
{
    // Test VIX fallback service directly
    var vixFallbackService = host.Services.GetRequiredService<IVixFallbackService>();
    
    logger.LogInformation("🔍 Testing VIX fallback service...");
    
    // Try web scraping first
    var webVix = await vixFallbackService.GetVixFromWebAsync();
    if (webVix.HasValue)
    {
        logger.LogInformation("✅ Web VIX: {VixValue:F2}", webVix.Value);
    }
    else
    {
        logger.LogInformation("⚠️ Web VIX: Not available");
    }
    
    // Try synthetic VIX calculation
    var syntheticVix = await vixFallbackService.CalculateSyntheticVixAsync();
    if (syntheticVix.HasValue)
    {
        logger.LogInformation("✅ Synthetic VIX: {VixValue:F2}", syntheticVix.Value);
    }
    else
    {
        logger.LogInformation("⚠️ Synthetic VIX: Not available");
    }
    
    // Try Brave Search fallback
    var braveVix = await vixFallbackService.GetVixFromBraveSearchAsync();
    if (braveVix.HasValue)
    {
        logger.LogInformation("✅ Brave Search VIX: {VixValue:F2}", braveVix.Value);
    }
    else
    {
        logger.LogInformation("⚠️ Brave Search VIX: Not available");
    }
    
    // Try cached VIX
    var cachedVix = await vixFallbackService.GetVixWithCachingAsync();
    if (cachedVix.HasValue)
    {
        logger.LogInformation("✅ Cached VIX: {VixValue:F2}", cachedVix.Value);
    }
    else
    {
        logger.LogInformation("⚠️ Cached VIX: Not available");
    }
    
    logger.LogInformation("🎯 VIX test completed");
}
catch (Exception ex)
{
    logger.LogError(ex, "❌ VIX test failed");
}
