# SmaTrendFollower Docker Deployment Script (PowerShell)
# Usage: .\scripts\deploy.ps1 [options]

param(
    [switch]$NoPull,
    [switch]$BuildFresh,
    [switch]$NoLogs,
    [string]$Environment = "production",
    [switch]$Help
)

# Function to write colored output
function Write-Status {
    param([string]$Message)
    Write-Host "[INFO] $Message" -ForegroundColor Blue
}

function Write-Success {
    param([string]$Message)
    Write-Host "[SUCCESS] $Message" -ForegroundColor Green
}

function Write-Warning {
    param([string]$Message)
    Write-Host "[WARNING] $Message" -ForegroundColor Yellow
}

function Write-Error {
    param([string]$Message)
    Write-Host "[ERROR] $Message" -ForegroundColor Red
}

# Function to show usage
function Show-Usage {
    Write-Host "SmaTrendFollower Docker Deployment Script" -ForegroundColor Cyan
    Write-Host ""
    Write-Host "Usage: .\scripts\deploy.ps1 [options]" -ForegroundColor White
    Write-Host ""
    Write-Host "Options:" -ForegroundColor White
    Write-Host "  -NoPull          Don't pull latest images" -ForegroundColor Gray
    Write-Host "  -BuildFresh      Force rebuild of bot image" -ForegroundColor Gray
    Write-Host "  -NoLogs          Don't show logs after deployment" -ForegroundColor Gray
    Write-Host "  -Environment ENV Set environment (production|development)" -ForegroundColor Gray
    Write-Host "  -Help            Show this help message" -ForegroundColor Gray
    Write-Host ""
    Write-Host "Examples:" -ForegroundColor White
    Write-Host "  .\scripts\deploy.ps1                 # Standard deployment" -ForegroundColor Gray
    Write-Host "  .\scripts\deploy.ps1 -BuildFresh     # Force rebuild and deploy" -ForegroundColor Gray
    Write-Host "  .\scripts\deploy.ps1 -NoLogs         # Deploy without showing logs" -ForegroundColor Gray
}

# Show help if requested
if ($Help) {
    Show-Usage
    exit 0
}

# Check if Docker is running
try {
    docker info | Out-Null
} catch {
    Write-Error "Docker is not running. Please start Docker and try again."
    exit 1
}

# Check if docker-compose is available
try {
    docker-compose --version | Out-Null
} catch {
    Write-Error "docker-compose is not installed. Please install docker-compose and try again."
    exit 1
}

Write-Status "Starting SmaTrendFollower deployment..."
Write-Status "Environment: $Environment"

# Pull latest code if in a git repository
if (Test-Path ".git") {
    Write-Status "Pulling latest code from repository..."
    try {
        git pull origin main
    } catch {
        Write-Warning "Failed to pull latest code. Continuing with local version."
    }
} else {
    Write-Warning "Not in a git repository. Using local code."
}

# Pull latest images if requested
if (-not $NoPull) {
    Write-Status "Pulling latest Docker images..."
    docker-compose pull vault redis prometheus grafana
}

# Build bot image
if ($BuildFresh) {
    Write-Status "Building bot image from scratch..."
    docker-compose build --no-cache bot
} else {
    Write-Status "Building bot image..."
    docker-compose build bot
}

# Stop existing containers
Write-Status "Stopping existing containers..."
docker-compose down

# Start services
Write-Status "Starting services..."
docker-compose up -d

# Wait for services to be healthy
Write-Status "Waiting for services to be healthy..."
Start-Sleep -Seconds 10

# Check service health
Write-Status "Checking service health..."
$services = @("vault", "redis", "prometheus", "grafana", "bot")
$allHealthy = $true

foreach ($service in $services) {
    $status = docker-compose ps $service
    if ($status -match "healthy|Up") {
        Write-Success "$service is running"
    } else {
        Write-Error "$service is not healthy"
        $allHealthy = $false
    }
}

if ($allHealthy) {
    Write-Success "All services are running successfully!"
    Write-Host ""
    Write-Status "Service URLs:"
    Write-Host "  🤖 Trading Bot:     http://localhost:5000" -ForegroundColor Cyan
    Write-Host "  🔐 Vault:           http://localhost:8200" -ForegroundColor Cyan
    Write-Host "  📊 Grafana:         http://localhost:3000 (admin/admin)" -ForegroundColor Cyan
    Write-Host "  📈 Prometheus:      http://localhost:9090" -ForegroundColor Cyan
    Write-Host "  🗄️  Redis:           localhost:6379" -ForegroundColor Cyan
    Write-Host ""
    Write-Status "To view logs: docker-compose logs -f"
    Write-Status "To stop:      docker-compose down"
} else {
    Write-Error "Some services failed to start properly. Check logs with: docker-compose logs"
}

# Show logs if requested
if (-not $NoLogs) {
    Write-Host ""
    Write-Status "Showing bot logs (Ctrl+C to exit)..."
    docker-compose logs -f bot
}
