using Alpaca.Markets;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using SmaTrendFollower.Services;
using StackExchange.Redis;

namespace SmaTrendFollower.Services;

/// <summary>
/// Background service that processes real-time news from Alpaca and analyzes sentiment using Gemini AI
/// </summary>
public sealed class NewsSentimentService : BackgroundService
{
    private readonly IAlpacaDataStreamingClient _newsStreamingClient;
    private readonly IGeminiClient _geminiClient;
    private readonly IDatabase _redisDatabase;
    private readonly IUniverseProvider _universeProvider;
    private readonly ILogger<NewsSentimentService> _logger;
    private readonly SemaphoreSlim _processingLimiter;
    private readonly string _alpacaKeyId;
    private readonly string _alpacaSecret;

    public NewsSentimentService(
        IAlpacaClientFactory alpacaClientFactory,
        IGeminiClient geminiClient,
        OptimizedRedisConnectionService redisConnectionService,
        IUniverseProvider universeProvider,
        IConfiguration configuration,
        ILogger<NewsSentimentService> logger)
    {
        _geminiClient = geminiClient;
        _universeProvider = universeProvider;
        _logger = logger;

        // Get Redis database
        _redisDatabase = redisConnectionService.GetDatabaseAsync().GetAwaiter().GetResult();

        // Get Alpaca credentials from environment
        var keyIdEnv = configuration["AlpacaNews:KeyIdEnv"] ?? "APCA_API_KEY_ID";
        var secretEnv = configuration["AlpacaNews:SecretEnv"] ?? "APCA_API_SECRET";
        
        _alpacaKeyId = Environment.GetEnvironmentVariable(keyIdEnv) 
                      ?? throw new InvalidOperationException($"Environment variable {keyIdEnv} not found");
        _alpacaSecret = Environment.GetEnvironmentVariable(secretEnv) 
                       ?? throw new InvalidOperationException($"Environment variable {secretEnv} not found");

        // Create Alpaca news streaming client
        _newsStreamingClient = alpacaClientFactory.CreateDataStreamingClient();

        // Limit concurrent sentiment analysis requests to avoid overwhelming Gemini API
        _processingLimiter = new SemaphoreSlim(5, 5);

        _logger.LogInformation("NewsSentimentService initialized");
    }

    protected override async Task ExecuteAsync(CancellationToken stoppingToken)
    {
        try
        {
            _logger.LogInformation("Starting news sentiment analysis service...");

            // Connect and authenticate to Alpaca news stream
            await ConnectToNewsStreamAsync(stoppingToken);

            // Subscribe to news updates
            await SubscribeToNewsAsync(stoppingToken);

            // Process news updates
            await ProcessNewsUpdatesAsync(stoppingToken);
        }
        catch (OperationCanceledException)
        {
            _logger.LogInformation("News sentiment service was cancelled");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Fatal error in news sentiment service");
            throw;
        }
    }

    private async Task ConnectToNewsStreamAsync(CancellationToken cancellationToken)
    {
        try
        {
            _logger.LogInformation("Connecting to Alpaca news stream...");
            await _newsStreamingClient.ConnectAndAuthenticateAsync(cancellationToken);
            _logger.LogInformation("Successfully connected to Alpaca news stream");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to connect to Alpaca news stream");
            throw;
        }
    }

    private async Task SubscribeToNewsAsync(CancellationToken cancellationToken)
    {
        try
        {
            // Get current universe symbols for cost-gating
            var universeSymbols = _universeProvider.TodaySymbols?.ToHashSet() ?? new HashSet<string>();
            _logger.LogInformation("Subscribing to news for {Count} universe symbols", universeSymbols.Count);

            // Subscribe to news for all symbols (we'll filter in the handler)
            var newsSubscription = _newsStreamingClient.GetNewsSubscription();
            
            // Note: Alpaca news subscription typically covers all symbols, 
            // so we don't need to specify individual symbols here
            
            _logger.LogInformation("Successfully subscribed to Alpaca news stream");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to subscribe to news stream");
            throw;
        }
    }

    private async Task ProcessNewsUpdatesAsync(CancellationToken cancellationToken)
    {
        try
        {
            var newsSubscription = _newsStreamingClient.GetNewsSubscription();
            
            await foreach (var newsUpdate in newsSubscription.EnumerateUpdatesAsync(cancellationToken))
            {
                // Process news update in background to avoid blocking the stream
                _ = Task.Run(async () => await ProcessSingleNewsUpdateAsync(newsUpdate, cancellationToken), 
                           cancellationToken);
            }
        }
        catch (OperationCanceledException)
        {
            _logger.LogDebug("News processing was cancelled");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing news updates");
            throw;
        }
    }

    private async Task ProcessSingleNewsUpdateAsync(INewsArticle newsUpdate, CancellationToken cancellationToken)
    {
        try
        {
            // Cost-gate: Only process news for symbols in today's universe
            var universeSymbols = _universeProvider.TodaySymbols?.ToHashSet() ?? new HashSet<string>();
            
            // Check if any of the news symbols are in our universe
            var relevantSymbols = newsUpdate.Symbols?.Where(s => universeSymbols.Contains(s)).ToList() ?? new List<string>();
            
            if (!relevantSymbols.Any())
            {
                _logger.LogDebug("Skipping news for symbols not in universe: {Symbols}", 
                    string.Join(", ", newsUpdate.Symbols ?? new List<string>()));
                return;
            }

            // Limit concurrent processing
            await _processingLimiter.WaitAsync(cancellationToken);
            
            try
            {
                // Get sentiment score from Gemini
                var sentimentScore = await _geminiClient.GetSentimentAsync(newsUpdate.Headline, cancellationToken);
                
                if (sentimentScore == null)
                {
                    _logger.LogWarning("Failed to get sentiment score for headline: {Headline}", newsUpdate.Headline);
                    return;
                }

                // Store sentiment for each relevant symbol
                var today = DateTime.UtcNow.ToString("yyyyMMdd");
                
                foreach (var symbol in relevantSymbols)
                {
                    var redisKey = $"Sentiment:{symbol}:{today}";
                    
                    // Store sentiment score with news ID as field
                    await _redisDatabase.HashSetAsync(redisKey, newsUpdate.Id.ToString(), sentimentScore.Value);
                    
                    // Set expiration to 24 hours
                    await _redisDatabase.KeyExpireAsync(redisKey, TimeSpan.FromHours(24));
                    
                    _logger.LogInformation("Stored sentiment for {Symbol}: {Score:F3} (News: {NewsId})", 
                        symbol, sentimentScore.Value, newsUpdate.Id);
                }

                // Also store a "latest" entry for quick access
                foreach (var symbol in relevantSymbols)
                {
                    var latestKey = $"Sentiment:{symbol}:{today}";
                    await _redisDatabase.HashSetAsync(latestKey, "latest", sentimentScore.Value);
                }
            }
            finally
            {
                _processingLimiter.Release();
            }
        }
        catch (OperationCanceledException)
        {
            _logger.LogDebug("News processing was cancelled for news ID: {NewsId}", newsUpdate.Id);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing news update {NewsId}: {Headline}", 
                newsUpdate.Id, newsUpdate.Headline);
        }
    }

    public override async Task StopAsync(CancellationToken cancellationToken)
    {
        _logger.LogInformation("Stopping news sentiment service...");
        
        try
        {
            await _newsStreamingClient.DisconnectAsync(cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Error disconnecting from news stream");
        }

        await base.StopAsync(cancellationToken);
        _logger.LogInformation("News sentiment service stopped");
    }

    public override void Dispose()
    {
        _processingLimiter?.Dispose();
        _newsStreamingClient?.Dispose();
        base.Dispose();
    }
}
