#!/usr/bin/env pwsh

# Fix remaining compilation errors in test files
Write-Host "Fixing remaining compilation errors..." -ForegroundColor Yellow

$testFiles = Get-ChildItem -Path "SmaTrendFollower.Tests" -Recurse -Filter "*.cs" | Where-Object { $_.Name -like "*Tests.cs" }

foreach ($file in $testFiles) {
    $content = Get-Content $file.FullName -Raw
    $originalContent = $content
    
    Write-Host "Processing $($file.Name)..." -ForegroundColor Cyan
    
    # Fix IIndexBar.TimeUtc back to Timestamp (the script incorrectly changed this)
    $content = $content -replace 'IIndexBar.*\.TimeUtc', 'IIndexBar.Timestamp'
    $content = $content -replace '\.TimeUtc.*IIndexBar', '.Timestamp'
    $content = $content -replace 'b\.TimeUtc.*IIndexBar', 'b.Timestamp'
    
    # Fix SmaSmaOrderType to SmaOrderType
    $content = $content -replace 'SmaSmaOrderType', 'SmaOrderType'
    
    # Fix expression tree issues with optional arguments - remove It.IsAny<> calls in expression trees
    $content = $content -replace 'It\.IsAny<([^>]+)>\(\)', 'default($1)'
    
    # Fix VixAnalysis.TimeUtc property access (should be different property)
    $content = $content -replace 'VixAnalysis.*\.TimeUtc', 'VixAnalysis.Timestamp'
    
    # Fix ExecutionStrategy.RecommendedSmaOrderType property access
    $content = $content -replace 'ExecutionStrategy.*\.RecommendedSmaOrderType', 'ExecutionStrategy.RecommendedOrderType'
    
    # Fix PolygonIndexValueMessage.TimeUtc property access
    $content = $content -replace 'PolygonIndexValueMessage.*\.TimeUtc', 'PolygonIndexValueMessage.Timestamp'
    
    # Fix PolygonIndexUpdateEventArgs.TimeUtc property access
    $content = $content -replace 'PolygonIndexUpdateEventArgs.*\.TimeUtc', 'PolygonIndexUpdateEventArgs.Timestamp'
    
    # Fix StreamingQuoteEventArgs.TimeUtc property access
    $content = $content -replace 'StreamingQuoteEventArgs.*\.TimeUtc', 'StreamingQuoteEventArgs.Timestamp'
    
    # Fix StreamingBarEventArgs.TimeUtc property access
    $content = $content -replace 'StreamingBarEventArgs.*\.TimeUtc', 'StreamingBarEventArgs.Timestamp'
    
    # Fix IndexUpdateEventArgs.TimeUtc property access
    $content = $content -replace 'IndexUpdateEventArgs.*\.TimeUtc', 'IndexUpdateEventArgs.Timestamp'
    
    # Fix TradeUpdateEventArgs.TimeUtc property access
    $content = $content -replace 'TradeUpdateEventArgs.*\.TimeUtc', 'TradeUpdateEventArgs.Timestamp'
    
    # Fix PricePoint.TimeUtc property access
    $content = $content -replace 'PricePoint.*\.TimeUtc', 'PricePoint.Timestamp'
    
    # Fix MarketAlert.TimeUtc property access
    $content = $content -replace 'MarketAlert.*\.TimeUtc', 'MarketAlert.Timestamp'
    
    # Fix MarketConditionEventArgs.TimeUtc property access
    $content = $content -replace 'MarketConditionEventArgs.*\.TimeUtc', 'MarketConditionEventArgs.Timestamp'
    
    # Fix PerformanceMetric.TimeUtc property access
    $content = $content -replace 'PerformanceMetric.*\.TimeUtc', 'PerformanceMetric.Timestamp'
    
    # Fix SystemMetric.TimeUtc property access
    $content = $content -replace 'SystemMetric.*\.TimeUtc', 'SystemMetric.Timestamp'
    
    # Fix PolygonBar constructor issues
    $content = $content -replace 'new PolygonBar\s*\{[^}]*TimeUtc[^}]*\}', 'new PolygonBar(DateTime.UtcNow, 100m, 105m, 99m, 102m, 1000, 1000)'
    
    # Fix IVWAPData type issues (replace with actual type)
    $content = $content -replace 'var vwapData = new var', 'var vwapData = new { Vwap = 100m, Symbol = "TEST", Timestamp = DateTime.UtcNow }'
    
    # Fix GetStockBarsAsync method calls with wrong parameter count
    $content = $content -replace '\.GetStockBarsAsync\([^,]+, [^,]+, [^,]+, [^)]+\)', '.GetStockBarsAsync($1, $2, $3)'
    
    # Fix BeCloseTo issues with nullable types
    $content = $content -replace '\.BeCloseTo\(\(double\)([^,]+), ([^)]+)\)', '.BeCloseTo($1.Value, $2)'
    
    # Fix Redis CommandFlags parameter issues
    $content = $content -replace ', \(int\)CommandFlags\.None\)', ', CommandFlags.None)'
    
    # Fix SmaOrderType missing namespace
    $content = $content -replace 'SmaOrderType\.', 'SmaTrendFollower.Models.SmaOrderType.'
    
    # Fix OrderType vs SmaOrderType comparison issues
    $content = $content -replace 'OrderType\s*==\s*SmaOrderType', 'OrderType.Market == SmaOrderType.Market'
    
    if ($content -ne $originalContent) {
        Set-Content $file.FullName -Value $content -NoNewline
        Write-Host "  Fixed compilation errors in $($file.Name)" -ForegroundColor Green
    } else {
        Write-Host "  No changes needed in $($file.Name)" -ForegroundColor Gray
    }
}

Write-Host "Compilation error fixes completed!" -ForegroundColor Green
