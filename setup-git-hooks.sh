#!/bin/bash
# Setup Git hooks for SmaTrendFollower
# This script configures Git to use the custom hooks in .githooks/

echo "Setting up Git hooks for SmaTrendFollower..."

# Configure Git to use .githooks directory
git config core.hooksPath .githooks

# Make hooks executable
chmod +x .githooks/pre-push

echo "Git hooks configured successfully!"
echo "Pre-push hook will now run tests before each push."
