using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Microsoft.ML;
using Microsoft.ML.Data;
using SmaTrendFollower.Models;
using SmaTrendFollower.Monitoring;
using SmaTrendFollower.Services;
using StackExchange.Redis;
using System.Text.Json;

namespace SmaTrendFollower.MachineLearning.Prediction;

/// <summary>
/// Interface for slippage forecasting service
/// </summary>
public interface ISlippageForecasterService
{
    /// <summary>
    /// Predicts slippage in basis points for a given trade context
    /// </summary>
    float PredictBps(SlippageSignalFeatures features, QuoteContext quote);

    /// <summary>
    /// Checks if the service is ready for predictions
    /// </summary>
    bool IsReady { get; }

    /// <summary>
    /// Gets the current model version
    /// </summary>
    long ModelVersion { get; }

    /// <summary>
    /// Forces a model reload from disk
    /// </summary>
    Task ReloadModelAsync();
}

/// <summary>
/// Production-ready slippage forecasting service with ML model prediction and Redis hot-reload capability.
/// Predicts execution slippage in basis points for dynamic limit order pricing.
/// </summary>
public sealed class SlippageForecasterService : ISlippageForecasterService, IDisposable
{
    private readonly ILogger<SlippageForecasterService> _logger;
    private readonly IOptimizedRedisConnectionService? _redisService;
    private readonly IConfiguration _configuration;
    private readonly MLContext _mlContext;

    private PredictionEngine<SlippageInput, SlippageOutput>? _predictionEngine;
    private long _modelVersion = 0;
    private DateTime _lastModelCheck = DateTime.MinValue;
    private readonly SemaphoreSlim _reloadLock = new(1, 1);
    private bool _disposed;

    // Redis key for model version tracking
    private const string ModelVersionKey = "model:slip:version";
    private const int ModelCheckIntervalSeconds = 30; // Check for model updates every 30 seconds

    public bool IsReady => _predictionEngine != null;
    public long ModelVersion => _modelVersion;

    public SlippageForecasterService(
        ILogger<SlippageForecasterService> logger,
        IOptimizedRedisConnectionService? redisService,
        IConfiguration configuration)
    {
        _logger = logger;
        _redisService = redisService;
        _configuration = configuration;
        _mlContext = new MLContext(seed: 42);

        // Load initial model
        _ = Task.Run(async () => await LoadInitialModelAsync());
    }

    public float PredictBps(SlippageSignalFeatures features, QuoteContext quote)
    {
        try
        {
            // Check for model updates periodically
            if (DateTime.UtcNow - _lastModelCheck > TimeSpan.FromSeconds(30))
            {
                _ = Task.Run(async () => await ReloadIfChangedAsync());
            }

            if (_predictionEngine == null)
            {
                _logger.LogWarning("Slippage forecaster not ready - returning default prediction");
                return 5.0f; // Default 0.5 bps slippage
            }

            var input = new SlippageInput
            {
                SpreadPct = quote.SpreadPct,
                RankProb = features.RankProb,
                ATR_Pct = features.ATR_Pct,
                VolumePct10d = features.VolumePct10d,
                Regime = features.Regime,
                Side = (uint)features.Side,
                Hour = quote.TimestampUtc.Hour
            };

            var prediction = _predictionEngine.Predict(input);
            var slippageBps = prediction.Score;

            // Clamp to reasonable bounds (-50 to +50 bps)
            slippageBps = Math.Max(-50f, Math.Min(50f, slippageBps));

            _logger.LogDebug("Predicted slippage: {SlippageBps:F2} bps for {Symbol}", 
                slippageBps, features.Symbol ?? "Unknown");

            return slippageBps;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error predicting slippage - returning default");
            return 5.0f; // Default fallback
        }
    }

    public async Task ReloadModelAsync()
    {
        await _reloadLock.WaitAsync();
        try
        {
            await LoadModelFromDiskAsync();
        }
        finally
        {
            _reloadLock.Release();
        }
    }

    private async Task LoadInitialModelAsync()
    {
        try
        {
            await LoadModelFromDiskAsync();
            _logger.LogInformation("Slippage forecaster service initialized successfully");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to load initial slippage model");
        }
    }

    private async Task ReloadIfChangedAsync()
    {
        if (_redisService == null) return;

        try
        {
            _lastModelCheck = DateTime.UtcNow;

            var database = await _redisService.GetDatabaseAsync();
            var versionValue = await database.StringGetAsync(ModelVersionKey);

            if (versionValue.HasValue && long.TryParse(versionValue, out var version))
            {
                if (version != _modelVersion)
                {
                    _logger.LogInformation("Model version changed from {OldVersion} to {NewVersion} - reloading", 
                        _modelVersion, version);

                    await _reloadLock.WaitAsync();
                    try
                    {
                        await LoadModelFromDiskAsync();
                        _modelVersion = version;

                        // Update model version metric
                        MetricsRegistry.SlippageModelVersion.Set(_modelVersion);
                    }
                    finally
                    {
                        _reloadLock.Release();
                    }
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Error checking for model version updates");
        }
    }

    private async Task LoadModelFromDiskAsync()
    {
        try
        {
            var modelPath = _configuration.GetValue("SlippageTraining:ModelOutputPath", "Model/slippage_model.zip");

            if (!File.Exists(modelPath))
            {
                _logger.LogWarning("Slippage model file not found: {Path}", modelPath);
                return;
            }

            var model = _mlContext.Model.Load(modelPath, out var modelInputSchema);
            var newPredictionEngine = _mlContext.Model.CreatePredictionEngine<SlippageInput, SlippageOutput>(model);

            // Atomic replacement
            var oldEngine = _predictionEngine;
            _predictionEngine = newPredictionEngine;
            oldEngine?.Dispose();

            _logger.LogInformation("Slippage model loaded successfully from: {Path}", modelPath);

            // Load metadata if available
            var metadataPath = Path.ChangeExtension(modelPath, ".json");
            if (File.Exists(metadataPath))
            {
                var metadataJson = await File.ReadAllTextAsync(metadataPath);
                var metadata = JsonSerializer.Deserialize<SlippageModelMetadata>(metadataJson);
                if (metadata != null)
                {
                    _logger.LogInformation("Model metadata - Training Date: {Date}, Samples: {Samples}, MAE: {MAE:F4} bps",
                        metadata.TrainingDate, metadata.TrainingSamples, metadata.MeanAbsoluteError);

                    // Update model accuracy metric
                    MetricsRegistry.SlippageModelAccuracy.Set(metadata.RSquared);
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error loading slippage model from disk");
            throw;
        }
    }

    public void Dispose()
    {
        if (_disposed) return;

        _predictionEngine?.Dispose();
        _reloadLock?.Dispose();
        _disposed = true;
    }
}

/// <summary>
/// Input for slippage prediction model
/// </summary>
public class SlippageInput
{
    public float SpreadPct { get; set; }
    public float RankProb { get; set; }
    public float ATR_Pct { get; set; }
    public float VolumePct10d { get; set; }
    public float Regime { get; set; }
    public uint Side { get; set; }
    public int Hour { get; set; }
}

/// <summary>
/// Output from slippage prediction model
/// </summary>
public class SlippageOutput
{
    [ColumnName("Score")]
    public float Score { get; set; } // Predicted slippage in basis points
}

/// <summary>
/// Extended signal features for slippage prediction
/// </summary>
public record SlippageSignalFeatures(
    string? Symbol,
    float RankProb,
    float ATR_Pct,
    float VolumePct10d,
    float Regime,
    SmaOrderSide Side
) : SignalFeatures(0, 0, 0, 0, 0, 0, 0, 0);

/// <summary>
/// Metadata about trained slippage model
/// </summary>
public record SlippageModelMetadata
{
    public DateTime TrainingDate { get; init; }
    public int TrainingSamples { get; init; }
    public float MeanAbsoluteError { get; init; }
    public float RootMeanSquaredError { get; init; }
    public float RSquared { get; init; }
    public int NumberOfTrees { get; init; }
    public int NumberOfLeaves { get; init; }
    public int TrainingDataDays { get; init; }
}
