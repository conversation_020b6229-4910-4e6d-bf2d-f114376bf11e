using System.Runtime.Serialization;

namespace SmaTrendFollower.Services.ErrorHandling;

/// <summary>
/// Base exception class for all trading-related errors with enhanced error categorization
/// </summary>
[Serializable]
public abstract class TradingException : Exception
{
    public ErrorCategory Category { get; }
    public ErrorSeverity Severity { get; }
    public string? OperationContext { get; }
    public Dictionary<string, object> ErrorData { get; }
    public bool IsRetriable { get; }
    public TimeSpan? SuggestedRetryDelay { get; }

    protected TradingException(
        string message,
        ErrorCategory category,
        ErrorSeverity severity,
        bool isRetriable = false,
        TimeSpan? suggestedRetryDelay = null,
        string? operationContext = null,
        Exception? innerException = null)
        : base(message, innerException)
    {
        Category = category;
        Severity = severity;
        IsRetriable = isRetriable;
        SuggestedRetryDelay = suggestedRetryDelay;
        OperationContext = operationContext;
        ErrorData = new Dictionary<string, object>();
    }

    [Obsolete("This API supports obsolete formatter-based serialization. It should not be called or extended by application code.", DiagnosticId = "SYSLIB0051")]
    protected TradingException(SerializationInfo info, StreamingContext context)
        : base(info, context)
    {
        Category = (ErrorCategory)info.GetValue(nameof(Category), typeof(ErrorCategory))!;
        Severity = (ErrorSeverity)info.GetValue(nameof(Severity), typeof(ErrorSeverity))!;
        IsRetriable = info.GetBoolean(nameof(IsRetriable));
        OperationContext = info.GetString(nameof(OperationContext));
        ErrorData = (Dictionary<string, object>)info.GetValue(nameof(ErrorData), typeof(Dictionary<string, object>))!;
        
        var retryDelayTicks = info.GetInt64(nameof(SuggestedRetryDelay));
        SuggestedRetryDelay = retryDelayTicks > 0 ? new TimeSpan(retryDelayTicks) : null;
    }

    [Obsolete("This API supports obsolete formatter-based serialization. It should not be called or extended by application code.")]
    public override void GetObjectData(SerializationInfo info, StreamingContext context)
    {
        base.GetObjectData(info, context);
        info.AddValue(nameof(Category), Category);
        info.AddValue(nameof(Severity), Severity);
        info.AddValue(nameof(IsRetriable), IsRetriable);
        info.AddValue(nameof(OperationContext), OperationContext);
        info.AddValue(nameof(ErrorData), ErrorData);
        info.AddValue(nameof(SuggestedRetryDelay), SuggestedRetryDelay?.Ticks ?? 0);
    }

    public TradingException WithData(string key, object value)
    {
        ErrorData[key] = value;
        return this;
    }

    public T? GetData<T>(string key) where T : class
    {
        return ErrorData.TryGetValue(key, out var value) ? value as T : null;
    }
}

/// <summary>
/// Exception for market data related errors
/// </summary>
[Serializable]
public sealed class MarketDataException : TradingException
{
    public string? Symbol { get; }
    public string? DataProvider { get; }

    public MarketDataException(
        string message,
        string? symbol = null,
        string? dataProvider = null,
        bool isRetriable = true,
        TimeSpan? suggestedRetryDelay = null,
        Exception? innerException = null)
        : base(message, ErrorCategory.MarketData, ErrorSeverity.Medium, isRetriable, suggestedRetryDelay, $"Symbol: {symbol}, Provider: {dataProvider}", innerException)
    {
        Symbol = symbol;
        DataProvider = dataProvider;
    }

    [Obsolete("This API supports obsolete formatter-based serialization. It should not be called or extended by application code.", DiagnosticId = "SYSLIB0051")]
    private MarketDataException(SerializationInfo info, StreamingContext context)
        : base(info, context)
    {
        Symbol = info.GetString(nameof(Symbol));
        DataProvider = info.GetString(nameof(DataProvider));
    }

    [Obsolete("This API supports obsolete formatter-based serialization. It should not be called or extended by application code.")]
    public override void GetObjectData(SerializationInfo info, StreamingContext context)
    {
        base.GetObjectData(info, context);
        info.AddValue(nameof(Symbol), Symbol);
        info.AddValue(nameof(DataProvider), DataProvider);
    }
}

/// <summary>
/// Exception for trade execution related errors
/// </summary>
[Serializable]
public sealed class TradeExecutionException : TradingException
{
    public string? Symbol { get; }
    public decimal? Quantity { get; }
    public string? OrderType { get; }

    public TradeExecutionException(
        string message,
        string? symbol = null,
        decimal? quantity = null,
        string? orderType = null,
        bool isRetriable = false,
        Exception? innerException = null)
        : base(message, ErrorCategory.TradeExecution, ErrorSeverity.High, isRetriable, null, $"Symbol: {symbol}, Qty: {quantity}, Type: {orderType}", innerException)
    {
        Symbol = symbol;
        Quantity = quantity;
        OrderType = orderType;
    }

    [Obsolete("This API supports obsolete formatter-based serialization. It should not be called or extended by application code.", DiagnosticId = "SYSLIB0051")]
    private TradeExecutionException(SerializationInfo info, StreamingContext context)
        : base(info, context)
    {
        Symbol = info.GetString(nameof(Symbol));
        Quantity = info.GetDecimal(nameof(Quantity));
        OrderType = info.GetString(nameof(OrderType));
    }

    [Obsolete("This API supports obsolete formatter-based serialization. It should not be called or extended by application code.")]
    public override void GetObjectData(SerializationInfo info, StreamingContext context)
    {
        base.GetObjectData(info, context);
        info.AddValue(nameof(Symbol), Symbol);
        info.AddValue(nameof(Quantity), Quantity);
        info.AddValue(nameof(OrderType), OrderType);
    }
}

/// <summary>
/// Exception for risk management related errors
/// </summary>
[Serializable]
public sealed class RiskManagementException : TradingException
{
    public string? RiskRule { get; }
    public decimal? RiskAmount { get; }

    public RiskManagementException(
        string message,
        string? riskRule = null,
        decimal? riskAmount = null,
        bool isRetriable = false,
        Exception? innerException = null)
        : base(message, ErrorCategory.RiskManagement, ErrorSeverity.Critical, isRetriable, null, $"Rule: {riskRule}, Amount: {riskAmount}", innerException)
    {
        RiskRule = riskRule;
        RiskAmount = riskAmount;
    }

    [Obsolete("This API supports obsolete formatter-based serialization. It should not be called or extended by application code.", DiagnosticId = "SYSLIB0051")]
    private RiskManagementException(SerializationInfo info, StreamingContext context)
        : base(info, context)
    {
        RiskRule = info.GetString(nameof(RiskRule));
        RiskAmount = info.GetDecimal(nameof(RiskAmount));
    }

    [Obsolete("This API supports obsolete formatter-based serialization. It should not be called or extended by application code.")]
    public override void GetObjectData(SerializationInfo info, StreamingContext context)
    {
        base.GetObjectData(info, context);
        info.AddValue(nameof(RiskRule), RiskRule);
        info.AddValue(nameof(RiskAmount), RiskAmount);
    }
}

/// <summary>
/// Exception for external API related errors
/// </summary>
[Serializable]
public sealed class ExternalApiException : TradingException
{
    public string? ApiName { get; }
    public string? Endpoint { get; }
    public int? StatusCode { get; }

    public ExternalApiException(
        string message,
        string? apiName = null,
        string? endpoint = null,
        int? statusCode = null,
        bool isRetriable = true,
        TimeSpan? suggestedRetryDelay = null,
        Exception? innerException = null)
        : base(message, ErrorCategory.ExternalApi, ErrorSeverity.Medium, isRetriable, suggestedRetryDelay, $"API: {apiName}, Endpoint: {endpoint}, Status: {statusCode}", innerException)
    {
        ApiName = apiName;
        Endpoint = endpoint;
        StatusCode = statusCode;
    }

    [Obsolete("This API supports obsolete formatter-based serialization. It should not be called or extended by application code.", DiagnosticId = "SYSLIB0051")]
    private ExternalApiException(SerializationInfo info, StreamingContext context)
        : base(info, context)
    {
        ApiName = info.GetString(nameof(ApiName));
        Endpoint = info.GetString(nameof(Endpoint));
        StatusCode = info.GetInt32(nameof(StatusCode));
    }

    [Obsolete("This API supports obsolete formatter-based serialization. It should not be called or extended by application code.")]
    public override void GetObjectData(SerializationInfo info, StreamingContext context)
    {
        base.GetObjectData(info, context);
        info.AddValue(nameof(ApiName), ApiName);
        info.AddValue(nameof(Endpoint), Endpoint);
        info.AddValue(nameof(StatusCode), StatusCode);
    }
}

/// <summary>
/// Exception for configuration related errors
/// </summary>
[Serializable]
public sealed class ConfigurationException : TradingException
{
    public string? ConfigurationKey { get; }

    public ConfigurationException(
        string message,
        string? configurationKey = null,
        Exception? innerException = null)
        : base(message, ErrorCategory.Configuration, ErrorSeverity.Critical, false, null, $"Key: {configurationKey}", innerException)
    {
        ConfigurationKey = configurationKey;
    }

    [Obsolete("This API supports obsolete formatter-based serialization. It should not be called or extended by application code.", DiagnosticId = "SYSLIB0051")]
    private ConfigurationException(SerializationInfo info, StreamingContext context)
        : base(info, context)
    {
        ConfigurationKey = info.GetString(nameof(ConfigurationKey));
    }

    [Obsolete("This API supports obsolete formatter-based serialization. It should not be called or extended by application code.")]
    public override void GetObjectData(SerializationInfo info, StreamingContext context)
    {
        base.GetObjectData(info, context);
        info.AddValue(nameof(ConfigurationKey), ConfigurationKey);
    }
}
