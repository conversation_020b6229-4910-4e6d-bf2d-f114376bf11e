# CI/CD Setup Guide for SmaTrendFollower

This guide covers the continuous integration and deployment setup for the SmaTrendFollower trading bot.

## GitHub Actions CI Pipeline

### Overview
The project uses GitHub Actions for automated builds, testing, and deployment. The pipeline is defined in `.github/workflows/build.yml`.

### Pipeline Features
- **Automated Builds**: Triggers on every push and pull request
- **Test Execution**: Runs full test suite with code coverage
- **Artifact Publishing**: Creates deployable build artifacts
- **Multi-Platform Support**: Currently Ubuntu-based, configurable for self-hosted runners

### Pipeline Steps
1. **Checkout**: Downloads the repository code
2. **Setup .NET**: Installs .NET 8 SDK
3. **Restore**: Downloads NuGet dependencies
4. **Test**: Runs test suite with coverage collection
5. **Publish**: Creates release build artifacts
6. **Upload**: Stores build artifacts for deployment

## Self-Hosted Runner Setup

### Why Use Self-Hosted Runners?
- **Local Control**: Run CI/CD on your own infrastructure
- **Custom Environment**: Access to local resources (Redis, databases)
- **Cost Efficiency**: No GitHub Actions minutes consumption
- **Security**: Keep sensitive operations within your network

### Docker-Based Runner Setup

```bash
# Create runner directories
sudo mkdir -p /opt/gh-runner/{work,config}

# Run GitHub Actions runner container
docker run -d --name sma-runner \
  -e RUNNER_NAME=sma-trend \
  -e GITHUB_URL=https://github.com/patco1/SmaTrendFollower \
  -e RUNNER_TOKEN=<YOUR_REGISTRATION_TOKEN> \
  -v /opt/gh-runner/work:/runner/work \
  -v /opt/gh-runner/config:/runner/config \
  --restart unless-stopped \
  myoung34/github-runner:latest
```

### Getting Registration Token
1. Go to your GitHub repository
2. Navigate to Settings → Actions → Runners
3. Click "New self-hosted runner"
4. Copy the registration token from the configuration commands

### Switching to Self-Hosted
Update `.github/workflows/build.yml`:
```yaml
jobs:
  ci:
    runs-on: [self-hosted, local]  # Changed from ubuntu-latest
```

## Git Hooks Setup

### Pre-Push Hook
Automatically runs tests before pushing to prevent broken builds:

```bash
# Windows
.\setup-git-hooks.ps1

# Linux/macOS
./setup-git-hooks.sh
```

### Manual Hook Setup
```bash
# Configure Git to use custom hooks directory
git config core.hooksPath .githooks

# Make hooks executable (Unix-like systems)
chmod +x .githooks/pre-push
```

## Secrets Management

### Required Secrets
Add these secrets in GitHub repository settings (Settings → Secrets and variables → Actions):

- `POLYGON_API_KEY`: Your Polygon.io API key
- `ALPACA_KEY_ID`: Your Alpaca Markets API key ID
- `ALPACA_SECRET`: Your Alpaca Markets secret key
- `OPENAI_API_KEY`: OpenAI API key (if using ML features)
- `DISCORD_BOT_TOKEN`: Discord bot token (for notifications)

### Using Secrets in Workflows
```yaml
env:
  POLYGON_API_KEY: ${{ secrets.POLYGON_API_KEY }}
  ALPACA_KEY_ID: ${{ secrets.ALPACA_KEY_ID }}
  ALPACA_SECRET: ${{ secrets.ALPACA_SECRET }}
```

## Alternative CI Systems

### Woodpecker CI Migration
To migrate to Woodpecker CI:

1. Copy `.github/workflows/build.yml` to `.woodpecker.yml`
2. Update syntax for Woodpecker format:
```yaml
pipeline:
  build:
    image: mcr.microsoft.com/dotnet/sdk:8.0
    commands:
      - dotnet restore
      - dotnet test --configuration Release
      - dotnet publish SmaTrendFollower.Console -c Release -o publish
```

### Jenkins Pipeline
For Jenkins, create a `Jenkinsfile`:
```groovy
pipeline {
    agent any
    stages {
        stage('Build') {
            steps {
                sh 'dotnet restore'
                sh 'dotnet build --configuration Release'
            }
        }
        stage('Test') {
            steps {
                sh 'dotnet test --configuration Release'
            }
        }
        stage('Publish') {
            steps {
                sh 'dotnet publish SmaTrendFollower.Console -c Release -o publish'
                archiveArtifacts artifacts: 'publish/**', fingerprint: true
            }
        }
    }
}
```

## Deployment Automation

### Automated Deployment
The CI pipeline creates deployment artifacts that can be automatically deployed:

```bash
# Download and deploy latest build
curl -H "Authorization: token $GITHUB_TOKEN" \
  -L https://api.github.com/repos/patco1/SmaTrendFollower/actions/artifacts/latest/zip \
  -o latest-build.zip

unzip latest-build.zip
./deploy-to-production.sh
```

### Production Deployment Checklist
- [ ] Secrets configured in CI/CD system
- [ ] Self-hosted runner connected (if using)
- [ ] Git hooks configured for development team
- [ ] Deployment scripts tested
- [ ] Monitoring and alerting configured
- [ ] Rollback procedures documented

## Troubleshooting

### Common Issues

**Runner Connection Issues**
- Verify registration token is valid
- Check network connectivity to GitHub
- Ensure Docker container has proper permissions

**Build Failures**
- Check .NET SDK version compatibility
- Verify all NuGet packages are accessible
- Review test failures in CI logs

**Secret Access Issues**
- Confirm secrets are properly configured
- Check secret names match workflow references
- Verify repository permissions for secrets access

### Monitoring CI/CD Health
- Monitor build success rates
- Track test execution times
- Review artifact sizes and deployment times
- Set up alerts for failed builds

## Best Practices

1. **Keep Builds Fast**: Optimize test execution and dependency restoration
2. **Fail Fast**: Run quick tests first, expensive tests later
3. **Secure Secrets**: Never log or expose sensitive information
4. **Version Artifacts**: Tag builds with version numbers or commit hashes
5. **Monitor Performance**: Track build times and resource usage
6. **Document Changes**: Update this guide when modifying CI/CD setup

## Support

For CI/CD issues:
1. Check GitHub Actions logs for detailed error messages
2. Review this documentation for configuration guidance
3. Consult GitHub Actions documentation for advanced features
4. Consider self-hosted runners for complex requirements
