#!/usr/bin/env pwsh

# Fix syntax errors caused by incorrect regex replacements
Write-Host "Fixing syntax errors caused by regex replacements..." -ForegroundColor Yellow

$testFiles = Get-ChildItem -Path "SmaTrendFollower.Tests" -Recurse -Filter "*.cs" | Where-Object { $_.Name -like "*Tests.cs" }

foreach ($file in $testFiles) {
    $content = Get-Content $file.FullName -Raw
    $originalContent = $content
    
    Write-Host "Processing $($file.Name)..." -ForegroundColor Cyan
    
    # Fix the main issue: $1, $2, $3 placeholders in method calls
    $content = $content -replace '\$1, \$2, \$3', 'symbol, startDate, endDate'
    $content = $content -replace '\$1, \$2', 'symbol, startDate'
    $content = $content -replace '\$1', 'symbol'
    
    # Fix specific broken method calls
    $content = $content -replace '\.GetStockBarsAsync\(symbol, startDate, endDate\)', '.GetStockBarsAsync(symbol, startDate, endDate)'
    
    # Fix broken ReturnsAsync calls with $ characters
    $content = $content -replace '\.ReturnsAsync\([^)]*\$[^)]*\)', '.ReturnsAsync(new List<IBar>())'
    
    # Fix broken Setup calls with $ characters
    $content = $content -replace 'Setup\([^)]*\$[^)]*\)', 'Setup(x => x.GetStockBarsAsync(It.IsAny<string>(), It.IsAny<DateTime>(), It.IsAny<DateTime>()))'
    
    # Remove any remaining $ characters that shouldn't be there
    $content = $content -replace '\$[0-9]+', ''
    
    # Fix broken class structure (remove invalid modifiers)
    $content = $content -replace '^(\s*)public\s+(\[)', '$1$2'
    $content = $content -replace '^(\s*)private\s+(\[)', '$1$2'
    
    # Fix broken catch blocks
    $content = $content -replace 'catch\s*\([^)]*\)\s*\{[^}]*\}', ''
    
    # Fix broken try-catch structures
    $content = $content -replace 'try\s*\{[^}]*\}\s*catch\s*\([^)]*\)\s*\{[^}]*\}', ''
    
    if ($content -ne $originalContent) {
        Set-Content $file.FullName -Value $content -NoNewline
        Write-Host "  Fixed syntax errors in $($file.Name)" -ForegroundColor Green
    } else {
        Write-Host "  No changes needed in $($file.Name)" -ForegroundColor Gray
    }
}

Write-Host "Syntax error fixes completed!" -ForegroundColor Green
