#!/usr/bin/env bash
# SmaTrendFollower Docker Deployment Script
# Usage: ./scripts/deploy.sh [options]

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Default values
PULL_LATEST=true
BUILD_FRESH=false
SHOW_LOGS=true
ENVIRONMENT="production"

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to show usage
show_usage() {
    echo "SmaTrendFollower Docker Deployment Script"
    echo ""
    echo "Usage: $0 [options]"
    echo ""
    echo "Options:"
    echo "  --no-pull          Don't pull latest images"
    echo "  --build-fresh      Force rebuild of bot image"
    echo "  --no-logs          Don't show logs after deployment"
    echo "  --env ENV          Set environment (production|development)"
    echo "  --help             Show this help message"
    echo ""
    echo "Examples:"
    echo "  $0                 # Standard deployment"
    echo "  $0 --build-fresh   # Force rebuild and deploy"
    echo "  $0 --no-logs       # Deploy without showing logs"
}

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        --no-pull)
            PULL_LATEST=false
            shift
            ;;
        --build-fresh)
            BUILD_FRESH=true
            shift
            ;;
        --no-logs)
            SHOW_LOGS=false
            shift
            ;;
        --env)
            ENVIRONMENT="$2"
            shift 2
            ;;
        --help)
            show_usage
            exit 0
            ;;
        *)
            print_error "Unknown option: $1"
            show_usage
            exit 1
            ;;
    esac
done

# Check if Docker is running
if ! docker info > /dev/null 2>&1; then
    print_error "Docker is not running. Please start Docker and try again."
    exit 1
fi

# Check if docker-compose is available
if ! command -v docker-compose > /dev/null 2>&1; then
    print_error "docker-compose is not installed. Please install docker-compose and try again."
    exit 1
fi

print_status "Starting SmaTrendFollower deployment..."
print_status "Environment: $ENVIRONMENT"

# Pull latest code if in a git repository
if [ -d ".git" ]; then
    print_status "Pulling latest code from repository..."
    git pull origin main || print_warning "Failed to pull latest code. Continuing with local version."
else
    print_warning "Not in a git repository. Using local code."
fi

# Pull latest images if requested
if [ "$PULL_LATEST" = true ]; then
    print_status "Pulling latest Docker images..."
    docker-compose pull vault redis prometheus grafana
fi

# Build bot image
if [ "$BUILD_FRESH" = true ]; then
    print_status "Building bot image from scratch..."
    docker-compose build --no-cache bot
else
    print_status "Building bot image..."
    docker-compose build bot
fi

# Stop existing containers
print_status "Stopping existing containers..."
docker-compose down

# Start services
print_status "Starting services..."
docker-compose up -d

# Wait for services to be healthy
print_status "Waiting for services to be healthy..."
sleep 10

# Check service health
print_status "Checking service health..."
services=("vault" "redis" "prometheus" "grafana" "bot")
all_healthy=true

for service in "${services[@]}"; do
    if docker-compose ps "$service" | grep -q "healthy\|Up"; then
        print_success "$service is running"
    else
        print_error "$service is not healthy"
        all_healthy=false
    fi
done

if [ "$all_healthy" = true ]; then
    print_success "All services are running successfully!"
    echo ""
    print_status "Service URLs:"
    echo "  🤖 Trading Bot:     http://localhost:5000"
    echo "  🔐 Vault:           http://localhost:8200"
    echo "  📊 Grafana:         http://localhost:3000 (admin/admin)"
    echo "  📈 Prometheus:      http://localhost:9090"
    echo "  🗄️  Redis:           localhost:6379"
    echo ""
    print_status "To view logs: docker-compose logs -f"
    print_status "To stop:      docker-compose down"
else
    print_error "Some services failed to start properly. Check logs with: docker-compose logs"
fi

# Show logs if requested
if [ "$SHOW_LOGS" = true ]; then
    echo ""
    print_status "Showing bot logs (Ctrl+C to exit)..."
    docker-compose logs -f bot
fi
