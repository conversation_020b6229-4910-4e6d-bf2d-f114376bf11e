using System.Collections.Concurrent;
using System.Net.WebSockets;
using System.Text;
using System.Text.Json;

namespace SmaTrendFollower.Tests.TestHelpers;

/// <summary>
/// Mock WebSocket implementation for testing WebSocket functionality.
/// Records all sent messages and allows simulation of received messages.
/// </summary>
public class MockWebSocket : WebSocket
{
    private readonly ConcurrentQueue<string> _sentMessages = new();
    private readonly ConcurrentQueue<byte[]> _receivedMessages = new();
    private WebSocketState _state = WebSocketState.None;
    private WebSocketCloseStatus? _closeStatus;
    private string? _closeStatusDescription;
    private readonly CancellationTokenSource _cancellationTokenSource = new();

    public override WebSocketCloseStatus? CloseStatus => _closeStatus;
    public override string? CloseStatusDescription => _closeStatusDescription;
    public override WebSocketState State => _state;
    public override string? SubProtocol => null;

    /// <summary>
    /// All messages that were sent through this WebSocket
    /// </summary>
    public IReadOnlyList<string> MessagesSent => _sentMessages.ToList();

    /// <summary>
    /// Number of messages sent
    /// </summary>
    public int SentMessageCount => _sentMessages.Count;

    /// <summary>
    /// Simulates opening the WebSocket connection
    /// </summary>
    public void SimulateOpen()
    {
        _state = WebSocketState.Open;
    }

    /// <summary>
    /// Simulates closing the WebSocket connection
    /// </summary>
    public void SimulateClose(WebSocketCloseStatus closeStatus = WebSocketCloseStatus.NormalClosure, 
        string? description = null)
    {
        _state = WebSocketState.Closed;
        _closeStatus = closeStatus;
        _closeStatusDescription = description;
    }

    /// <summary>
    /// Simulates receiving a message from the server
    /// </summary>
    public void SimulateReceiveMessage(string message)
    {
        var bytes = Encoding.UTF8.GetBytes(message);
        _receivedMessages.Enqueue(bytes);
    }

    /// <summary>
    /// Simulates receiving a JSON message from the server
    /// </summary>
    public void SimulateReceiveJson<T>(T obj)
    {
        var json = JsonSerializer.Serialize(obj);
        SimulateReceiveMessage(json);
    }

    /// <summary>
    /// Gets the last sent message
    /// </summary>
    public string? GetLastSentMessage()
    {
        return _sentMessages.TryPeek(out var message) ? message : null;
    }

    /// <summary>
    /// Checks if a specific message was sent
    /// </summary>
    public bool WasMessageSent(string message)
    {
        return _sentMessages.Contains(message);
    }

    /// <summary>
    /// Checks if a message containing specific text was sent
    /// </summary>
    public bool WasMessageSentContaining(string text)
    {
        return _sentMessages.Any(m => m.Contains(text));
    }

    /// <summary>
    /// Clears all recorded messages
    /// </summary>
    public void ClearMessages()
    {
        while (_sentMessages.TryDequeue(out _)) { }
        while (_receivedMessages.TryDequeue(out _)) { }
    }

    public override void Abort()
    {
        _state = WebSocketState.Aborted;
        _cancellationTokenSource.Cancel();
    }

    public override Task CloseAsync(WebSocketCloseStatus closeStatus, string? statusDescription, 
        CancellationToken cancellationToken)
    {
        _state = WebSocketState.CloseSent;
        _closeStatus = closeStatus;
        _closeStatusDescription = statusDescription;
        return Task.CompletedTask;
    }

    public override Task CloseOutputAsync(WebSocketCloseStatus closeStatus, string? statusDescription, 
        CancellationToken cancellationToken)
    {
        _state = WebSocketState.CloseSent;
        _closeStatus = closeStatus;
        _closeStatusDescription = statusDescription;
        return Task.CompletedTask;
    }

    public override void Dispose()
    {
        _state = WebSocketState.Closed;
        _cancellationTokenSource.Dispose();
    }

    public override async Task<WebSocketReceiveResult> ReceiveAsync(ArraySegment<byte> buffer,
        CancellationToken cancellationToken)
    {
        if (_state != WebSocketState.Open)
        {
            throw new WebSocketException("WebSocket is not in Open state");
        }

        byte[]? messageBytes = null;

        // Wait for a message to be available or cancellation
        while (!_receivedMessages.TryDequeue(out messageBytes) && !cancellationToken.IsCancellationRequested)
        {
            await Task.Delay(10, cancellationToken);
        }

        if (cancellationToken.IsCancellationRequested)
        {
            throw new OperationCanceledException();
        }

        if (messageBytes != null)
        {
            var bytesToCopy = Math.Min(messageBytes.Length, buffer.Count);
            Array.Copy(messageBytes, 0, buffer.Array!, buffer.Offset, bytesToCopy);

            return new WebSocketReceiveResult(
                bytesToCopy,
                WebSocketMessageType.Text,
                bytesToCopy == messageBytes.Length);
        }

        // Return empty result if no message
        return new WebSocketReceiveResult(0, WebSocketMessageType.Text, true);
    }

    public override Task SendAsync(ArraySegment<byte> buffer, WebSocketMessageType messageType, 
        bool endOfMessage, CancellationToken cancellationToken)
    {
        if (_state != WebSocketState.Open)
        {
            throw new WebSocketException("WebSocket is not in Open state");
        }

        var message = Encoding.UTF8.GetString(buffer.Array!, buffer.Offset, buffer.Count);
        _sentMessages.Enqueue(message);
        
        return Task.CompletedTask;
    }
}

/// <summary>
/// Factory for creating MockWebSocket instances in tests
/// </summary>
public class MockWebSocketFactory
{
    private readonly List<MockWebSocket> _createdSockets = new();

    /// <summary>
    /// Creates a new MockWebSocket instance
    /// </summary>
    public MockWebSocket CreateWebSocket()
    {
        var socket = new MockWebSocket();
        _createdSockets.Add(socket);
        return socket;
    }

    /// <summary>
    /// Gets all WebSockets created by this factory
    /// </summary>
    public IReadOnlyList<MockWebSocket> CreatedSockets => _createdSockets.AsReadOnly();

    /// <summary>
    /// Simulates opening all created WebSockets
    /// </summary>
    public void OpenAllSockets()
    {
        foreach (var socket in _createdSockets)
        {
            socket.SimulateOpen();
        }
    }

    /// <summary>
    /// Simulates closing all created WebSockets
    /// </summary>
    public void CloseAllSockets()
    {
        foreach (var socket in _createdSockets)
        {
            socket.SimulateClose();
        }
    }

    /// <summary>
    /// Gets total number of messages sent across all sockets
    /// </summary>
    public int TotalMessagesSent => _createdSockets.Sum(s => s.SentMessageCount);

    /// <summary>
    /// Checks if any socket sent a message containing the specified text
    /// </summary>
    public bool AnySocketSentMessageContaining(string text)
    {
        return _createdSockets.Any(s => s.WasMessageSentContaining(text));
    }
}

/// <summary>
/// Extension methods for MockWebSocket testing
/// </summary>
public static class MockWebSocketExtensions
{
    /// <summary>
    /// Sends a text message to the mock WebSocket
    /// </summary>
    public static async Task SendTextAsync(this MockWebSocket webSocket, string message)
    {
        var buffer = Encoding.UTF8.GetBytes(message);
        await webSocket.SendAsync(new ArraySegment<byte>(buffer), WebSocketMessageType.Text, true, 
            CancellationToken.None);
    }

    /// <summary>
    /// Sends a JSON object to the mock WebSocket
    /// </summary>
    public static async Task SendJsonAsync<T>(this MockWebSocket webSocket, T obj)
    {
        var json = JsonSerializer.Serialize(obj);
        await webSocket.SendTextAsync(json);
    }

    /// <summary>
    /// Receives a text message from the mock WebSocket
    /// </summary>
    public static async Task<string> ReceiveTextAsync(this MockWebSocket webSocket)
    {
        var buffer = new byte[4096];
        var result = await webSocket.ReceiveAsync(new ArraySegment<byte>(buffer), CancellationToken.None);
        return Encoding.UTF8.GetString(buffer, 0, result.Count);
    }
}
