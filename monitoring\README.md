# 📊 SmaTrendFollower Monitoring Pack

Complete Grafana & Prometheus monitoring infrastructure for the SmaTrendFollower trading system.

## 🚀 Quick Start

```powershell
# Start the monitoring stack
.\start-monitoring.ps1

# Start SmaTrendFollower with metrics
cd ..
dotnet run --project SmaTrendFollower.Console -- metrics-api
```

## 📁 Directory Structure

```
monitoring/
├── grafana/
│   ├── dashboards/
│   │   ├── trading_overview.json      # Main trading dashboard
│   │   └── execution_quality.json     # Execution quality metrics
│   └── provisioning/
│       └── dashboards/
│           └── sma-dashboards.yml     # Auto-provisioning config
├── prometheus/
│   ├── prometheus.yml                 # Prometheus configuration
│   └── alerts.yml                     # Alert rules
├── alertmanager/
│   └── alertmanager.yml              # AlertManager configuration
├── docker-compose.yml                # Complete monitoring stack
├── start-monitoring.ps1              # Startup script
└── README.md                         # This file
```

## 🌐 Access URLs

| Service | URL | Credentials |
|---------|-----|-------------|
| Grafana | http://localhost:3000 | admin/admin |
| Prometheus | http://localhost:9090 | - |
| AlertManager | http://localhost:9093 | - |
| Node Exporter | http://localhost:9100 | - |
| Redis Exporter | http://localhost:9121 | - |

## 📈 Dashboards

### Trading Overview
- **Cumulative P&L**: Portfolio value and daily P&L tracking
- **Universe Size**: Number of symbols being monitored
- **Current Regime**: Market regime indicator
- **Trading Activity**: Trade and signal rates
- **Current Positions**: Open position count
- **Current VIX**: Real-time VIX monitoring

### Execution Quality
- **Average Slippage**: 15-minute rolling slippage in bps
- **Symbols Halted**: Anomaly-halted symbol count
- **Order Success Rate**: Order execution success percentage
- **Signal Generation Latency**: Performance metrics
- **Error Rates**: System error monitoring

## 🚨 Alert Rules

| Alert | Threshold | Severity |
|-------|-----------|----------|
| BotDown | >2 minutes | Critical |
| HighDrawdown | <-$5,000 daily P&L | Warning |
| HighSlippage | >10 bps average | Warning |
| SignalLatency | >30 seconds (95th percentile) | Warning |
| HighErrorRate | >0.1 errors/minute | Critical |
| OrderFailures | >5% failure rate | Warning |
| WebSocketReconnects | >0.5/minute | Warning |
| UniverseSize | <50 symbols | Warning |
| HighVIX | >35 | Warning |
| TooManyPositions | >10 positions | Warning |

## 🔧 Management Commands

```powershell
# Start monitoring stack
.\start-monitoring.ps1

# Stop monitoring stack
.\start-monitoring.ps1 -Stop

# Restart monitoring stack
.\start-monitoring.ps1 -Restart

# Check status
.\start-monitoring.ps1 -Status

# View logs
.\start-monitoring.ps1 -Logs
```

## 📊 Key Metrics

### Trading Metrics
- `trades_total`: Total trades executed
- `signals_total`: Trading signals generated
- `signal_latency_ms`: Signal generation latency
- `portfolio_value_usd`: Current portfolio value
- `daily_pnl_usd`: Daily profit and loss
- `current_positions`: Number of open positions

### System Metrics
- `websocket_reconnect_total`: WebSocket reconnections
- `universe_size`: Trading universe size
- `application_errors_total`: Application errors
- `order_failures_total`: Order execution failures

### Market Metrics
- `current_vix`: Current VIX value
- `market_regime`: Market regime indicator
- `slippage_actual_bps`: Execution slippage

## 🔄 Data Sources

The monitoring stack collects data from:
- **SmaTrendFollower**: Trading metrics via `/metrics` endpoint
- **Node Exporter**: System performance metrics
- **Redis Exporter**: Redis performance metrics
- **Prometheus**: Self-monitoring metrics

## 🛠️ Customization

### Adding New Dashboards
1. Create JSON file in `grafana/dashboards/`
2. Restart Grafana: `docker-compose restart grafana`
3. Dashboard auto-loads within 30 seconds

### Modifying Alert Rules
1. Edit `prometheus/alerts.yml`
2. Reload Prometheus: `curl -X POST http://localhost:9090/-/reload`

### Changing Scrape Intervals
1. Edit `prometheus/prometheus.yml`
2. Restart Prometheus: `docker-compose restart prometheus`

## 📚 Documentation

- [Complete Setup Guide](../docs/monitoring.md)
- [Prometheus Observability Guide](../SmaTrendFollower.Console/Documentation/PrometheusObservabilityGuide.md)
- [Performance Monitoring](../PERFORMANCE_MONITORING.md)

## 🔐 Security Notes

- Default Grafana credentials: admin/admin (change immediately)
- Services bind to localhost only
- No external authentication configured
- Consider reverse proxy with SSL for production

## 🐛 Troubleshooting

### Common Issues

1. **Services won't start**: Check Docker is running
2. **No metrics in Prometheus**: Verify SmaTrendFollower is running with `metrics-api`
3. **Dashboards not loading**: Check Grafana logs with `docker-compose logs grafana`
4. **Alerts not firing**: Verify AlertManager configuration at http://localhost:9093

### Getting Help

```powershell
# View service logs
docker-compose logs [service-name]

# Check service status
docker-compose ps

# Restart specific service
docker-compose restart [service-name]
```
