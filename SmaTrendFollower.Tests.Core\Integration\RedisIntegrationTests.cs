using System.Threading;
using System.Threading.Tasks;
using FluentAssertions;
using StackExchange.Redis;
using SmaTrendFollower.Tests.Core.TestHelpers;
using Xunit;

namespace SmaTrendFollower.Tests.Core.Integration;

public class RedisIntegrationTests
{
    private readonly IDatabase _db = InMemoryRedis.Create();

    [Fact(Timeout = 3000)]
    public async Task Redis_Should_Handle_TTL_Correctly()
    {
        // Arrange – set key with 1-second TTL
        await _db.StringSetAsync("temp:key", "x", expiry: System.TimeSpan.FromSeconds(1));

        // Act – wait a bit over 1 second
        Thread.Sleep(1200);

        // Assert – key is gone
        var val = await _db.StringGetAsync("temp:key");
        val.HasValue.Should().BeFalse();
    }
}
