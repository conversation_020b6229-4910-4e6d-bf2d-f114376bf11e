using FluentAssertions;
using Microsoft.Extensions.Logging;
using NSubstitute;
using SmaTrendFollower.Models;
using SmaTrendFollower.Services;
using Alpaca.Markets;

namespace SmaTrendFollower.Tests.Core.Services;

public class SignalGeneratorTests
{
    private readonly IMarketDataService _marketDataService;
    private readonly IUniverseProvider _universeProvider;
    private readonly IMomentumCache _momentumCache;
    private readonly ILogger<SignalGenerator> _logger;
    private readonly SignalGenerator _signalGenerator;

    public SignalGeneratorTests()
    {
        _marketDataService = Substitute.For<IMarketDataService>();
        _universeProvider = Substitute.For<IUniverseProvider>();
        _momentumCache = Substitute.For<IMomentumCache>();
        _logger = Substitute.For<ILogger<SignalGenerator>>();

        _signalGenerator = new SignalGenerator(
            _marketDataService,
            _universeProvider,
            _logger,
            _momentumCache);
    }

    [Fact]
    public async Task RunAsync_WithNoSymbols_ReturnsEmptySignals()
    {
        // Arrange
        _universeProvider.GetSymbolsAsync().Returns(Enumerable.Empty<string>());

        // Act
        var result = await _signalGenerator.RunAsync(10);

        // Assert
        result.Should().BeEmpty();
    }

    [Fact]
    public async Task RunAsync_WithValidSymbols_GeneratesSignals()
    {
        // Arrange
        var symbols = new[] { "AAPL", "MSFT" };
        _universeProvider.GetSymbolsAsync().Returns(symbols);

        // Create mock bars with sufficient data for calculations (need 200+ for SMA200)
        var bars = CreateMockBars(250);
        var mockPage = CreateMockPage(bars);
        _marketDataService.GetStockBarsAsync(Arg.Any<string>(), Arg.Any<DateTime>(), Arg.Any<DateTime>())
            .Returns(mockPage);

        // Mock cached RSI and MACD values
        _momentumCache.GetRsiAsync(Arg.Any<string>()).Returns(60.0); // Above 55 threshold
        _momentumCache.GetMacdAsync(Arg.Any<string>()).Returns(new Models.MacdResult(0.5, 0.3, 0.2)); // Positive histogram

        // Act
        var result = await _signalGenerator.RunAsync(10);

        // Assert
        result.Should().NotBeEmpty();
        result.Should().HaveCountLessOrEqualTo(10);
        result.All(s => s.Price > 0).Should().BeTrue();
        result.All(s => s.Atr > 0).Should().BeTrue();
    }

    [Fact]
    public async Task RunAsync_WithCachedIndicators_UsesCachedValues()
    {
        // Arrange
        var symbols = new[] { "AAPL" };
        _universeProvider.GetSymbolsAsync().Returns(symbols);

        var bars = CreateMockBars(250);
        var mockPage = CreateMockPage(bars);
        _marketDataService.GetStockBarsAsync(Arg.Any<string>(), Arg.Any<DateTime>(), Arg.Any<DateTime>())
            .Returns(mockPage);

        // Mock cached values
        _momentumCache.GetRsiAsync("AAPL").Returns(65.0);
        _momentumCache.GetMacdAsync("AAPL").Returns(new Models.MacdResult(1.0, 0.8, 0.2));

        // Act
        await _signalGenerator.RunAsync(10);

        // Assert
        await _momentumCache.Received(1).GetRsiAsync("AAPL");
        await _momentumCache.Received(1).GetMacdAsync("AAPL");
        await _momentumCache.DidNotReceive().SetRsiAsync(Arg.Any<string>(), Arg.Any<double>(), Arg.Any<TimeSpan?>());
        await _momentumCache.DidNotReceive().SetMacdAsync(Arg.Any<string>(), Arg.Any<MacdResult>(), Arg.Any<TimeSpan?>());
    }

    [Fact]
    public async Task RunAsync_WithoutCachedIndicators_CalculatesAndCaches()
    {
        // Arrange
        var symbols = new[] { "AAPL" };
        _universeProvider.GetSymbolsAsync().Returns(symbols);

        var bars = CreateMockBars(250);
        var mockPage = CreateMockPage(bars);
        _marketDataService.GetStockBarsAsync(Arg.Any<string>(), Arg.Any<DateTime>(), Arg.Any<DateTime>())
            .Returns(mockPage);

        // Mock no cached values
        _momentumCache.GetRsiAsync("AAPL").Returns((double?)null);
        _momentumCache.GetMacdAsync("AAPL").Returns((Models.MacdResult?)null);

        // Act
        await _signalGenerator.RunAsync(10);

        // Assert
        await _momentumCache.Received(1).GetRsiAsync("AAPL");
        await _momentumCache.Received(1).GetMacdAsync("AAPL");
        await _momentumCache.Received(1).SetRsiAsync("AAPL", Arg.Any<double>(), Arg.Any<TimeSpan?>());
        await _momentumCache.Received(1).SetMacdAsync("AAPL", Arg.Any<Models.MacdResult>(), Arg.Any<TimeSpan?>());
    }

    private static List<IBar> CreateMockBars(int count)
    {
        var bars = new List<IBar>();
        var baseDate = DateTime.UtcNow.AddDays(-count);
        var basePrice = 80m; // Start lower to create uptrend

        for (int i = 0; i < count; i++)
        {
            var bar = Substitute.For<IBar>();
            // Create an uptrending price pattern that will satisfy SMA conditions
            var trendComponent = (decimal)i * 0.3m; // Gradual uptrend
            var noiseComponent = (decimal)(Math.Sin(i * 0.1) * 2); // Small noise
            var price = basePrice + trendComponent + noiseComponent;

            bar.TimeUtc.Returns(baseDate.AddDays(i));
            bar.Open.Returns(price - 0.3m);
            bar.High.Returns(price + 0.5m);
            bar.Low.Returns(price - 0.5m);
            bar.Close.Returns(price);
            bar.Volume.Returns(1000000 + (ulong)(i * 10000)); // Increasing volume

            bars.Add(bar);
        }

        return bars;
    }

    private static IPage<IBar> CreateMockPage(List<IBar> bars)
    {
        var page = Substitute.For<IPage<IBar>>();
        page.Items.Returns(bars);
        page.NextPageToken.Returns((string?)null);
        return page;
    }
}
