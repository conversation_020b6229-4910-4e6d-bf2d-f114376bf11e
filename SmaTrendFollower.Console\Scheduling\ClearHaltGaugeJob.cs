using Microsoft.Extensions.Logging;
using Prometheus;
using Quartz;
using StackExchange.Redis;

namespace SmaTrendFollower.Scheduling;

/// <summary>
/// Quartz job that clears expired halt gauge metrics to prevent stale metrics
/// from accumulating in Prometheus. Runs daily at 2:05 AM ET to clean up
/// halt indicators for symbols that are no longer halted.
/// </summary>
[DisallowConcurrentExecution]
public sealed class ClearHaltGaugeJob : IJob
{
    private readonly IConnectionMultiplexer _connectionMultiplexer;
    private readonly ILogger<ClearHaltGaugeJob> _logger;

    // Reference to the halt gauge metric from AnomalyDetectorService
    private static readonly Gauge HaltedSymbols =
        Metrics.CreateGauge("anomaly_halted", "Trading halt active for symbol",
            new GaugeConfiguration { LabelNames = new[] { "symbol" } });

    public ClearHaltGaugeJob(
        IConnectionMultiplexer connectionMultiplexer,
        ILogger<ClearHaltGaugeJob> logger)
    {
        _connectionMultiplexer = connectionMultiplexer ?? throw new ArgumentNullException(nameof(connectionMultiplexer));
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
    }

    /// <summary>
    /// Executes the halt gauge cleanup job
    /// </summary>
    public async Task Execute(IJobExecutionContext context)
    {
        var jobStartTime = DateTime.UtcNow;
        _logger.LogInformation("Starting halt gauge cleanup job at {StartTime}", jobStartTime);

        try
        {
            var database = _connectionMultiplexer.GetDatabase();
            var server = _connectionMultiplexer.GetServer(_connectionMultiplexer.GetEndPoints().First());

            // Find all halt keys in Redis
            var haltKeys = new List<string>();
            await foreach (var key in server.KeysAsync(pattern: "halt:*"))
            {
                haltKeys.Add(key!);
            }

            _logger.LogInformation("Found {Count} halt keys to check", haltKeys.Count);

            var clearedCount = 0;
            var activeCount = 0;

            // Check each halt key and clear gauge for expired ones
            foreach (var haltKey in haltKeys)
            {
                try
                {
                    var exists = await database.KeyExistsAsync(haltKey);
                    var symbol = ExtractSymbolFromHaltKey(haltKey);

                    if (!exists && !string.IsNullOrEmpty(symbol))
                    {
                        // Key has expired, clear the gauge
                        HaltedSymbols.WithLabels(symbol).Set(0);
                        clearedCount++;
                        _logger.LogDebug("Cleared halt gauge for expired symbol: {Symbol}", symbol);
                    }
                    else if (exists && !string.IsNullOrEmpty(symbol))
                    {
                        // Key still exists, ensure gauge is set
                        HaltedSymbols.WithLabels(symbol).Set(1);
                        activeCount++;
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogWarning(ex, "Error processing halt key {Key}", haltKey);
                }
            }

            var duration = DateTime.UtcNow - jobStartTime;
            _logger.LogInformation(
                "Halt gauge cleanup completed in {Duration:F2}s. Cleared: {Cleared}, Active: {Active}",
                duration.TotalSeconds, clearedCount, activeCount);

            // Record job execution metrics
            RecordJobMetrics(clearedCount, activeCount, duration);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during halt gauge cleanup job execution");
            throw; // Re-throw to let Quartz handle the failure
        }
    }

    /// <summary>
    /// Extracts the symbol name from a halt key (e.g., "halt:AAPL" -> "AAPL")
    /// </summary>
    private static string ExtractSymbolFromHaltKey(string haltKey)
    {
        if (string.IsNullOrEmpty(haltKey) || !haltKey.StartsWith("halt:"))
            return string.Empty;

        return haltKey.Substring(5); // Remove "halt:" prefix
    }

    /// <summary>
    /// Records job execution metrics for monitoring
    /// </summary>
    private void RecordJobMetrics(int clearedCount, int activeCount, TimeSpan duration)
    {
        try
        {
            // Create metrics for job monitoring
            var jobExecutions = Metrics.CreateCounter(
                "halt_gauge_cleanup_executions_total",
                "Total halt gauge cleanup job executions",
                new CounterConfiguration { LabelNames = new[] { "status" } });

            var gaugesCleared = Metrics.CreateCounter(
                "halt_gauges_cleared_total",
                "Total halt gauges cleared by cleanup job");

            var activeHalts = Metrics.CreateGauge(
                "active_halts_count",
                "Current number of active trading halts");

            var jobDuration = Metrics.CreateHistogram(
                "halt_gauge_cleanup_duration_seconds",
                "Duration of halt gauge cleanup job execution");

            // Record metrics
            jobExecutions.WithLabels("success").Inc();
            gaugesCleared.Inc(clearedCount);
            activeHalts.Set(activeCount);
            jobDuration.Observe(duration.TotalSeconds);
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Error recording job metrics");
        }
    }
}
