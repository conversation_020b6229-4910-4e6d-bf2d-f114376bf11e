using Microsoft.Extensions.Logging;
using Quartz;
using SmaTrendFollower.MachineLearning.DataPrep;
using SmaTrendFollower.MachineLearning.ModelTraining;
using SmaTrendFollower.Models;
using SmaTrendFollower.Monitoring;
using SmaTrendFollower.Services;
using StackExchange.Redis;

namespace SmaTrendFollower.Scheduling;

/// <summary>
/// Daily job to run regime classification at 08:35 ET
/// </summary>
[DisallowConcurrentExecution]
public class RegimeClassifierRunner : IJob
{
    private readonly IRegimeClassifierService _regimeClassifier;
    private readonly ILogger<RegimeClassifierRunner> _logger;

    public RegimeClassifierRunner(
        IRegimeClassifierService regimeClassifier,
        ILogger<RegimeClassifierRunner> logger)
    {
        _regimeClassifier = regimeClassifier ?? throw new ArgumentNullException(nameof(regimeClassifier));
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
    }

    public async Task Execute(IJobExecutionContext context)
    {
        var stopwatch = System.Diagnostics.Stopwatch.StartNew();
        
        try
        {
            _logger.LogInformation("Starting daily regime classification...");

            var regime = await _regimeClassifier.DetectTodayAsync(context.CancellationToken);
            
            stopwatch.Stop();
            _logger.LogInformation("Daily regime classification completed in {ElapsedMs}ms. Regime: {Regime}", 
                stopwatch.ElapsedMilliseconds, regime);

            // Update metrics
            MetricsRegistry.BackgroundServiceExecutions
                .WithLabels("RegimeClassifierRunner", "success")
                .Inc();
        }
        catch (Exception ex)
        {
            stopwatch.Stop();
            _logger.LogError(ex, "Error during daily regime classification after {ElapsedMs}ms", 
                stopwatch.ElapsedMilliseconds);

            MetricsRegistry.BackgroundServiceExecutions
                .WithLabels("RegimeClassifierRunner", "error")
                .Inc();
            
            throw;
        }
    }
}

/// <summary>
/// Weekly job to retrain regime classification model on Sunday 18:45 ET
/// </summary>
[DisallowConcurrentExecution]
public class RegimeClassifierRetrainer : IJob
{
    private readonly IFeatureExportService _featureExportService;
    private readonly IRegimeClassifierService _regimeClassifier;
    private readonly IOptimizedRedisConnectionService _redisService;
    private readonly ILogger<RegimeClassifierRetrainer> _logger;

    private const string ModelPath = "Model/regime_model.zip";
    private const string CsvPath = "Model/regime.csv";
    private const string ModelVersionKey = "model:regime:version";

    public RegimeClassifierRetrainer(
        IFeatureExportService featureExportService,
        IRegimeClassifierService regimeClassifier,
        IOptimizedRedisConnectionService redisService,
        ILogger<RegimeClassifierRetrainer> logger)
    {
        _featureExportService = featureExportService ?? throw new ArgumentNullException(nameof(featureExportService));
        _regimeClassifier = regimeClassifier ?? throw new ArgumentNullException(nameof(regimeClassifier));
        _redisService = redisService ?? throw new ArgumentNullException(nameof(redisService));
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
    }

    public async Task Execute(IJobExecutionContext context)
    {
        var stopwatch = System.Diagnostics.Stopwatch.StartNew();
        
        try
        {
            _logger.LogInformation("Starting weekly regime classifier retraining...");

            // Step 1: Export training data
            var endDate = DateTime.UtcNow.Date;
            var startDate = endDate.AddDays(-365); // Use 1 year of data for training

            _logger.LogInformation("Exporting regime training data from {StartDate} to {EndDate}", 
                startDate.ToString("yyyy-MM-dd"), endDate.ToString("yyyy-MM-dd"));

            await _featureExportService.ExportRegimeCsvAsync(CsvPath, startDate, endDate);

            // Step 2: Train the model
            _logger.LogInformation("Training regime classification model...");
            var trainingResult = await TrainRegimeClassifier.TrainModelAsync(CsvPath, ModelPath);

            if (!trainingResult.Success)
            {
                throw new InvalidOperationException($"Model training failed: {trainingResult.ErrorMessage}");
            }

            _logger.LogInformation("Model training completed successfully. Accuracy: {Accuracy:P2}, Samples: {Samples}", 
                trainingResult.Accuracy, trainingResult.TotalSamples);

            // Step 3: Update model version in Redis for hot-reload
            var database = await _redisService.GetDatabaseAsync();
            var newVersion = DateTimeOffset.UtcNow.ToUnixTimeSeconds();
            await database.StringSetAsync(ModelVersionKey, newVersion, RedisKeyConstants.RedisKeyTTL.MLModel);

            // Step 4: Force model reload
            await _regimeClassifier.ReloadModelAsync();

            stopwatch.Stop();
            _logger.LogInformation("Weekly regime classifier retraining completed successfully in {ElapsedMs}ms. " +
                                 "New model version: {Version}", stopwatch.ElapsedMilliseconds, newVersion);

            // Update metrics
            MetricsRegistry.BackgroundServiceExecutions
                .WithLabels("RegimeClassifierRetrainer", "success")
                .Inc();

            MetricsRegistry.MLRetrainRuns
                .WithLabels("success")
                .Inc();
        }
        catch (Exception ex)
        {
            stopwatch.Stop();
            _logger.LogError(ex, "Error during weekly regime classifier retraining after {ElapsedMs}ms", 
                stopwatch.ElapsedMilliseconds);

            MetricsRegistry.BackgroundServiceExecutions
                .WithLabels("RegimeClassifierRetrainer", "error")
                .Inc();

            MetricsRegistry.MLRetrainRuns
                .WithLabels("error")
                .Inc();
            
            throw;
        }
    }
}

/// <summary>
/// Manual trigger job for regime classification testing
/// </summary>
public class ManualRegimeClassifierTrigger : IJob
{
    private readonly IRegimeClassifierService _regimeClassifier;
    private readonly ILogger<ManualRegimeClassifierTrigger> _logger;

    public ManualRegimeClassifierTrigger(
        IRegimeClassifierService regimeClassifier,
        ILogger<ManualRegimeClassifierTrigger> logger)
    {
        _regimeClassifier = regimeClassifier ?? throw new ArgumentNullException(nameof(regimeClassifier));
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
    }

    public async Task Execute(IJobExecutionContext context)
    {
        try
        {
            _logger.LogInformation("Manual regime classification triggered...");

            var regime = await _regimeClassifier.DetectTodayAsync(context.CancellationToken);
            var modelVersion = await _regimeClassifier.GetModelVersionAsync();

            _logger.LogInformation("Manual regime classification completed. Regime: {Regime}, Model Version: {Version}", 
                regime, modelVersion);

            // Store result in job data for CLI retrieval
            context.Result = new
            {
                Regime = regime.ToString(),
                ModelVersion = modelVersion,
                Timestamp = DateTime.UtcNow
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during manual regime classification");
            throw;
        }
    }
}
