#!/usr/bin/env bash
set -e
echo "Stopping old containers…"
docker compose down --volumes --remove-orphans || true

echo "Pruning dangling volumes named redis-data prometheus-data grafana-data gitea-data actions-runner-data vault-data"
docker volume rm redis-data prometheus-data grafana-data gitea-data actions-runner-data vault-data 2>/dev/null || true

echo "Building bot image…"
docker compose build bot

echo "Starting new stack…"
docker compose up -d

echo "Tailing bot logs (Ctrl-C to detach)…"
docker compose logs -f bot
