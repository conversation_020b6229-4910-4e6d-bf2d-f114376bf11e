# Wheel Options Strategy Implementation Guide

## 🎡 Overview

The Wheel Strategy is a systematic options income strategy that combines cash-secured puts and covered calls to generate consistent premium income while potentially acquiring quality stocks at attractive prices.

## 🎯 Strategy Mechanics

### Phase 1: Cash-Secured Puts
1. **Sell cash-secured puts** on quality stocks you'd be willing to own
2. **Collect premium** immediately
3. **Wait for expiration** or assignment

### Phase 2: Stock Assignment (If Assigned)
1. **Receive shares** at the put strike price
2. **Hold the stock** temporarily
3. **Prepare for covered calls**

### Phase 3: Covered Calls
1. **Sell covered calls** against the assigned shares
2. **Collect additional premium**
3. **Wait for expiration** or assignment

### Phase 4: Share Assignment (If Called Away)
1. **Shares are sold** at the call strike price
2. **Realize any capital gains/losses**
3. **Return to Phase 1** with new cash

## 🏗️ Implementation Architecture

### Core Components

```csharp
// Main engine
IWheelStrategyEngine wheelEngine;

// Background service for automated execution
WheelStrategyEngine : BackgroundService

// Configuration
WheelStrategyConfig config;

// Position tracking
WheelPosition position;
```

### Service Integration

```
WheelStrategyEngine
├── IMarketDataService (quotes, positions, account)
├── IMarketSessionGuard (trading hours)
├── ITradeExecutor (order execution)
├── IVIXResolverService (volatility regime)
└── IDiscordNotificationService (alerts)
```

## ⚙️ Configuration

### Basic Configuration (appsettings.json)

```json
{
  "WheelStrategy": {
    "Enabled": true,
    "MaxAllocationPercent": 0.20,
    "MinPremiumPercent": 0.01,
    "MinDaysToExpiration": 7,
    "MaxDaysToExpiration": 45,
    "MaxDeltaForPuts": 0.30,
    "MaxDeltaForCalls": 0.30,
    "MinLiquidity": 100,
    "MaxBidAskSpreadPercent": 0.05,
    "EnableRolling": true,
    "RollThreshold": 0.50,
    "MaxRollAttempts": 2,
    "RequireHighIV": true,
    "MinIVPercentile": 30,
    "AllowedSymbols": ["SPY", "QQQ", "AAPL", "MSFT"],
    "ExcludedSymbols": []
  }
}
```

### Advanced Configuration

```json
{
  "WheelStrategy": {
    "Timing": {
      "EntryWindowStart": "09:35:00",
      "EntryWindowEnd": "15:30:00",
      "CycleInterval": "00:15:00",
      "EnableExtendedHours": false
    },
    "Risk": {
      "MaxDrawdownPercent": 0.10,
      "MaxDailyLossPercent": 0.05,
      "MaxActivePositions": 10,
      "MaxSinglePositionPercent": 0.05,
      "EnableEmergencyStop": true,
      "EmergencyStopThreshold": 0.15
    }
  }
}
```

## 🎯 Selection Criteria

### Symbol Suitability
- **Price**: Minimum $10 per share
- **Liquidity**: Minimum 100 contracts daily volume
- **Options**: Active options chain with tight spreads
- **Quality**: Blue-chip stocks or major ETFs
- **Volatility**: Sufficient implied volatility for premium

### Strike Selection
- **Cash-Secured Puts**: 5% out-of-the-money (OTM)
- **Covered Calls**: 5% out-of-the-money (OTM)
- **Delta Limits**: Maximum 0.30 delta for both puts and calls

### Expiration Selection
- **Minimum**: 7 days to expiration
- **Maximum**: 45 days to expiration
- **Sweet Spot**: 14-30 days for optimal time decay

## 📊 Risk Management

### Position Sizing
- **Maximum Allocation**: 20% of account per strategy
- **Single Position**: 5% of account maximum
- **Diversification**: Multiple symbols, staggered expirations

### Stop Loss & Rolling
- **Profit Target**: Close at 50% of premium collected
- **Rolling**: Roll positions 7 days before expiration if unprofitable
- **Maximum Rolls**: 2 attempts per position

### Emergency Procedures
- **Drawdown Limit**: Stop at 10% account drawdown
- **Daily Loss**: Stop at 5% daily loss
- **Market Conditions**: Pause during extreme volatility

## 🚀 Usage Examples

### Basic Usage

```csharp
// Initialize wheel strategy
var wheelEngine = serviceProvider.GetRequiredService<IWheelStrategyEngine>();

// Run wheel cycle for a symbol
var result = await wheelEngine.RunWheelCycleAsync("AAPL");

if (result.Success)
{
    Console.WriteLine($"Action: {result.Action}");
    Console.WriteLine($"Premium: ${result.Premium:F2}");
}
```

### Position Management

```csharp
// Get current positions
var positions = await wheelEngine.GetCurrentPositionsAsync();

foreach (var position in positions)
{
    Console.WriteLine($"{position.Symbol}: {position.Type} - ${position.UnrealizedPnL:F2}");
}

// Get performance metrics
var metrics = await wheelEngine.GetPerformanceMetricsAsync();
Console.WriteLine($"Total Premium: ${metrics.TotalPremiumCollected:F2}");
Console.WriteLine($"Win Rate: {metrics.WinRate:P1}");
Console.WriteLine($"Annualized Return: {metrics.AnnualizedReturn:P2}");
```

### Configuration Updates

```csharp
// Update configuration dynamically
var newConfig = config with { MaxAllocationPercent = 0.15m };
await wheelEngine.UpdateConfigurationAsync(newConfig);
```

## 📈 Performance Expectations

### Target Metrics
- **Annual Return**: 12-24% from premium collection
- **Win Rate**: 70-85% of positions expire worthless
- **Maximum Drawdown**: <15% during normal market conditions
- **Sharpe Ratio**: 1.0-1.5 with proper risk management

### Market Conditions
- **Bull Markets**: Higher assignment rates, capital gains
- **Bear Markets**: Lower assignment rates, higher premiums
- **Sideways Markets**: Optimal conditions for wheel strategy
- **High Volatility**: Higher premiums, increased risk

## 🔧 Monitoring & Alerts

### Discord Notifications
- Position entries and exits
- Assignment notifications
- Performance milestones
- Risk limit breaches

### Key Metrics to Monitor
- **Premium Collection Rate**: Weekly/monthly totals
- **Assignment Frequency**: Track assignment rates by symbol
- **Rolling Success**: Effectiveness of position rolling
- **Opportunity Cost**: Compare to buy-and-hold returns

## ⚠️ Important Considerations

### Tax Implications
- **Short-term gains**: Premium income taxed as ordinary income
- **Assignment**: May trigger capital gains/losses
- **Wash sale rules**: Be aware of 30-day wash sale rules

### Market Risks
- **Gap Risk**: Overnight gaps can cause significant losses
- **Earnings Risk**: Avoid positions through earnings announcements
- **Dividend Risk**: Consider ex-dividend dates for covered calls

### Operational Risks
- **Liquidity**: Ensure sufficient cash for assignments
- **Margin**: Understand margin requirements for cash-secured puts
- **Exercise**: Early exercise risk, especially near ex-dividend dates

## 🎯 Best Practices

1. **Start Small**: Begin with 1-2 positions to learn the mechanics
2. **Quality Stocks**: Only wheel stocks you'd be happy to own
3. **Diversification**: Spread risk across multiple symbols and sectors
4. **Patience**: Don't chase premium; wait for good setups
5. **Record Keeping**: Track all trades for tax and performance analysis
6. **Continuous Learning**: Monitor and adjust based on market conditions

## 📚 Additional Resources

- **Options Education**: Understand Greeks (Delta, Theta, Vega)
- **Tax Planning**: Consult with tax professional for strategy optimization
- **Risk Management**: Regular portfolio reviews and adjustments
- **Market Analysis**: Stay informed about market conditions and volatility regimes
