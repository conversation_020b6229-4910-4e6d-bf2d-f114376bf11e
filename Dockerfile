# syntax=docker/dockerfile:1
FROM mcr.microsoft.com/dotnet/sdk:8.0 AS build
WORKDIR /src

# Copy solution and project files
COPY SmaTrendFollower.sln .
COPY SmaTrendFollower.Console/SmaTrendFollower.Console.csproj SmaTrendFollower.Console/
#COPY SmaTrendFollower.Tests/SmaTrendFollower.Tests.csproj SmaTrendFollower.Tests/
#COPY SmaTrendFollower.Tests.Core/SmaTrendFollower.Tests.Core.csproj SmaTrendFollower.Tests.Core/

# Restore dependencies
RUN dotnet restore SmaTrendFollower.Console/SmaTrendFollower.Console.csproj

# Copy source code
COPY SmaTrendFollower.Console/ SmaTrendFollower.Console/

# Build and publish the application
RUN dotnet publish SmaTrendFollower.Console/SmaTrendFollower.Console.csproj \
    -c Release \
    -o /app \
    --no-restore \
    --verbosity minimal

# Runtime stage
FROM mcr.microsoft.com/dotnet/aspnet:8.0 AS runtime
WORKDIR /app

# Install required packages for health monitoring
RUN apt-get update && apt-get install -y \
    curl \
    && rm -rf /var/lib/apt/lists/*

# Copy published application
COPY --from=build /app .

# Create directories for logs and data
RUN mkdir -p /app/logs /app/data /app/cache

# Set environment variables
ENV ASPNETCORE_URLS=http://0.0.0.0:5000
ENV ASPNETCORE_ENVIRONMENT=Production
ENV DOTNET_RUNNING_IN_CONTAINER=true

# Expose ports
EXPOSE 5000

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
    CMD curl -f http://localhost:5000/health || exit 1

# Set the entry point
ENTRYPOINT ["dotnet", "SmaTrendFollower.Console.dll"]
