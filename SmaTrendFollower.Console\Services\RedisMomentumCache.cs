using SmaTrendFollower.Models;
using StackExchange.Redis;
using System.Text.Json;
using Microsoft.Extensions.Logging;

namespace SmaTrendFollower.Services;

/// <summary>
/// Redis-based implementation of momentum cache for technical indicators
/// </summary>
public sealed class RedisMomentumCache : IMomentumCache
{
    private readonly IConnectionMultiplexer _redis;
    private readonly IDatabase _database;
    private readonly ILogger<RedisMomentumCache>? _logger;
    private readonly TimeSpan _defaultExpiry = TimeSpan.FromMinutes(10);

    // Redis key prefixes
    private const string RsiPrefix = "momentum:rsi:";
    private const string MacdPrefix = "momentum:macd:";
    private const string AnalysisPrefix = "momentum:analysis:";

    public RedisMomentumCache(string connectionString, ILogger<RedisMomentumCache>? logger = null)
    {
        _redis = ConnectionMultiplexer.Connect(connectionString);
        _database = _redis.GetDatabase();
        _logger = logger;
    }

    public RedisMomentumCache(IConnectionMultiplexer redis, ILogger<RedisMomentumCache>? logger = null)
    {
        _redis = redis;
        _database = _redis.GetDatabase();
        _logger = logger;
    }

    public async Task SetRsiAsync(string symbol, double rsi, TimeSpan? expiry = null)
    {
        try
        {
            var key = RsiPrefix + symbol.ToUpperInvariant();
            var value = rsi.ToString("F4");
            var ttl = expiry ?? _defaultExpiry;

            await _database.StringSetAsync(key, value, ttl);
            _logger?.LogDebug("Cached RSI for {Symbol}: {Rsi:F2}", symbol, rsi);
        }
        catch (Exception ex)
        {
            _logger?.LogWarning(ex, "Failed to cache RSI for {Symbol}", symbol);
        }
    }

    public async Task<double?> GetRsiAsync(string symbol)
    {
        try
        {
            var key = RsiPrefix + symbol.ToUpperInvariant();
            var value = await _database.StringGetAsync(key);

            if (!value.HasValue)
                return null;

            if (double.TryParse(value, out var rsi))
            {
                _logger?.LogDebug("Retrieved cached RSI for {Symbol}: {Rsi:F2}", symbol, rsi);
                return rsi;
            }

            _logger?.LogWarning("Invalid RSI value in cache for {Symbol}: {Value}", symbol, value);
            return null;
        }
        catch (Exception ex)
        {
            _logger?.LogWarning(ex, "Failed to retrieve RSI for {Symbol}", symbol);
            return null;
        }
    }

    public async Task SetMacdAsync(string symbol, Models.MacdResult macd, TimeSpan? expiry = null)
    {
        try
        {
            var key = MacdPrefix + symbol.ToUpperInvariant();
            var value = JsonSerializer.Serialize(macd);
            var ttl = expiry ?? _defaultExpiry;

            await _database.StringSetAsync(key, value, ttl);
            _logger?.LogDebug("Cached MACD for {Symbol}: {Macd:F4}, {Signal:F4}, {Histogram:F4}",
                symbol, macd.Macd, macd.Signal, macd.Histogram);
        }
        catch (Exception ex)
        {
            _logger?.LogWarning(ex, "Failed to cache MACD for {Symbol}", symbol);
        }
    }

    public async Task<Models.MacdResult?> GetMacdAsync(string symbol)
    {
        try
        {
            var key = MacdPrefix + symbol.ToUpperInvariant();
            var value = await _database.StringGetAsync(key);

            if (!value.HasValue)
                return null;

            var macd = JsonSerializer.Deserialize<Models.MacdResult>(value!);
            _logger?.LogDebug("Retrieved cached MACD for {Symbol}: {Macd:F4}, {Signal:F4}, {Histogram:F4}",
                symbol, macd.Macd, macd.Signal, macd.Histogram);
            return macd;
        }
        catch (Exception ex)
        {
            _logger?.LogWarning(ex, "Failed to retrieve MACD for {Symbol}", symbol);
            return null;
        }
    }

    public async Task SetMomentumAnalysisAsync(string symbol, MomentumAnalysis analysis, TimeSpan? expiry = null)
    {
        try
        {
            var key = AnalysisPrefix + symbol.ToUpperInvariant();
            var value = JsonSerializer.Serialize(analysis);
            var ttl = expiry ?? _defaultExpiry;

            await _database.StringSetAsync(key, value, ttl);
            _logger?.LogDebug("Cached momentum analysis for {Symbol}", symbol);
        }
        catch (Exception ex)
        {
            _logger?.LogWarning(ex, "Failed to cache momentum analysis for {Symbol}", symbol);
        }
    }

    public async Task<MomentumAnalysis?> GetMomentumAnalysisAsync(string symbol)
    {
        try
        {
            var key = AnalysisPrefix + symbol.ToUpperInvariant();
            var value = await _database.StringGetAsync(key);

            if (!value.HasValue)
                return null;

            var analysis = JsonSerializer.Deserialize<MomentumAnalysis>(value!);
            _logger?.LogDebug("Retrieved cached momentum analysis for {Symbol}", symbol);
            return analysis;
        }
        catch (Exception ex)
        {
            _logger?.LogWarning(ex, "Failed to retrieve momentum analysis for {Symbol}", symbol);
            return null;
        }
    }

    public async Task RemoveAsync(string symbol)
    {
        try
        {
            var symbolUpper = symbol.ToUpperInvariant();
            var keys = new RedisKey[]
            {
                RsiPrefix + symbolUpper,
                MacdPrefix + symbolUpper,
                AnalysisPrefix + symbolUpper
            };

            await _database.KeyDeleteAsync(keys);
            _logger?.LogDebug("Removed cached momentum data for {Symbol}", symbol);
        }
        catch (Exception ex)
        {
            _logger?.LogWarning(ex, "Failed to remove cached momentum data for {Symbol}", symbol);
        }
    }

    public async Task ClearAllAsync()
    {
        try
        {
            var server = _redis.GetServer(_redis.GetEndPoints().First());
            var keys = server.Keys(pattern: "momentum:*");
            
            if (keys.Any())
            {
                await _database.KeyDeleteAsync(keys.ToArray());
                _logger?.LogInformation("Cleared all momentum cache data");
            }
        }
        catch (Exception ex)
        {
            _logger?.LogWarning(ex, "Failed to clear momentum cache");
        }
    }

    public async Task<MomentumCacheStats> GetStatsAsync()
    {
        try
        {
            var server = _redis.GetServer(_redis.GetEndPoints().First());

            // Use async enumeration to avoid blocking
            var rsiKeysTask = Task.Run(() => server.Keys(pattern: RsiPrefix + "*").Count());
            var macdKeysTask = Task.Run(() => server.Keys(pattern: MacdPrefix + "*").Count());
            var analysisKeysTask = Task.Run(() => server.Keys(pattern: AnalysisPrefix + "*").Count());

            await Task.WhenAll(rsiKeysTask, macdKeysTask, analysisKeysTask);

            var rsiKeys = await rsiKeysTask;
            var macdKeys = await macdKeysTask;
            var analysisKeys = await analysisKeysTask;
            var totalKeys = rsiKeys + macdKeys + analysisKeys;

            return new MomentumCacheStats(totalKeys, rsiKeys, macdKeys, analysisKeys, DateTime.UtcNow);
        }
        catch (Exception ex)
        {
            _logger?.LogWarning(ex, "Failed to get momentum cache stats");
            return new MomentumCacheStats(0, 0, 0, 0, DateTime.UtcNow);
        }
    }

    public void Dispose()
    {
        _redis?.Dispose();
        GC.SuppressFinalize(this);
    }
}
