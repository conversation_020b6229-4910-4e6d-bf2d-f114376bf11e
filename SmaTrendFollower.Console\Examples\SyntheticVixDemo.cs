using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using StackExchange.Redis;
using SmaTrendFollower.Services;
using SmaTrendFollower.Configuration;
using System.Text.Json;

namespace SmaTrendFollower.Examples;

/// <summary>
/// Simple demonstration of the SyntheticVixService functionality
/// Shows service registration, weight management, and estimation capabilities
/// </summary>
public static class SyntheticVixDemo
{
    public static Task RunDemoAsync()
    {
        System.Console.WriteLine("🚀 SyntheticVixService Demo");
        System.Console.WriteLine("===========================");

        try
        {
            // Create minimal service collection for demo
            var services = new ServiceCollection();
            
            // Add configuration
            var configuration = new ConfigurationBuilder()
                .AddInMemoryCollection(new Dictionary<string, string?>
                {
                    ["POLYGON_API_KEY"] = "demo-key",
                    ["REDIS_CONNECTION"] = "localhost:6379"
                })
                .Build();
            
            services.AddSingleton<IConfiguration>(configuration);
            
            // Add logging
            services.AddLogging(builder => 
            {
                builder.AddConsole();
                builder.SetMinimumLevel(LogLevel.Information);
            });
            
            // Add HTTP client
            services.AddHttpClient();
            
            // Mock Redis for demo
            services.AddSingleton<ConnectionMultiplexer>(provider =>
            {
                throw new InvalidOperationException("Redis not available in demo");
            });
            
            // Add our services
            services.AddSingleton<ISyntheticVixService, SyntheticVixService>();
            services.AddSingleton<SyntheticVixTrainer>();
            
            var serviceProvider = services.BuildServiceProvider();
            
            System.Console.WriteLine("✅ Services registered successfully");
            
            // Test service resolution
            var syntheticVixService = serviceProvider.GetService<ISyntheticVixService>();
            var trainer = serviceProvider.GetService<SyntheticVixTrainer>();
            
            if (syntheticVixService != null && trainer != null)
            {
                System.Console.WriteLine("✅ SyntheticVixService and SyntheticVixTrainer resolved successfully");
            }
            else
            {
                System.Console.WriteLine("❌ Failed to resolve services");
                return Task.CompletedTask;
            }
            
            // Test data model serialization
            var testWeights = new SyntheticVixWeights
            {
                VxxCoefficient = 1.45m,
                UvxyCoefficient = 0.30m,
                SvxyCoefficient = -0.20m,
                SpyCoefficient = -0.05m,
                Intercept = 8.2m,
                TrainedAt = DateTime.UtcNow,
                RSquared = 0.85m,
                SampleSize = 252
            };
            
            var json = JsonSerializer.Serialize(testWeights);
            var deserialized = JsonSerializer.Deserialize<SyntheticVixWeights>(json);
            
            if (deserialized != null && deserialized.VxxCoefficient == testWeights.VxxCoefficient)
            {
                System.Console.WriteLine("✅ Data model serialization works correctly");
            }
            else
            {
                System.Console.WriteLine("❌ Data model serialization failed");
            }
            
            // Test ETF price snapshot
            var priceSnapshot = new EtfPriceSnapshot
            {
                VxxPrice = 25.50m,
                UvxyPrice = 12.75m,
                SvxyPrice = 45.20m,
                SpyPrice = 485.30m,
                Timestamp = DateTime.UtcNow.AddMinutes(-5),
                DataAge = TimeSpan.FromMinutes(5)
            };
            
            if (priceSnapshot.DataAge.TotalMinutes == 5)
            {
                System.Console.WriteLine("✅ ETF price snapshot model works correctly");
            }
            else
            {
                System.Console.WriteLine("❌ ETF price snapshot model failed");
            }
            
            // Show integration points
            System.Console.WriteLine("\n🔗 Integration Points:");
            System.Console.WriteLine("   • VIXResolverService.GetSyntheticVixFromAlpacaAsync() now uses SyntheticVixService");
            System.Console.WriteLine("   • Enhanced calculation with ML-trained regression weights");
            System.Console.WriteLine("   • Weekly training via Quartz.NET on Sunday 6:00 PM ET");
            System.Console.WriteLine("   • Redis caching with appropriate TTLs");
            System.Console.WriteLine("   • Data freshness validation (15-minute threshold)");
            
            // Show production features
            System.Console.WriteLine("\n🏭 Production Features:");
            System.Console.WriteLine("   • Thread-safe implementation with semaphore locks");
            System.Console.WriteLine("   • Comprehensive error handling and logging");
            System.Console.WriteLine("   • Fallback to static coefficients when training unavailable");
            System.Console.WriteLine("   • Model quality validation (R² > 0.5)");
            System.Console.WriteLine("   • Bounds checking (VIX 8-80 range)");
            
            // Show mathematical formula
            System.Console.WriteLine("\n🧮 Regression Formula:");
            System.Console.WriteLine("   VIX = a*VXX + b*UVXY + c*SVXY + d*SPY + e");
            System.Console.WriteLine("   Where a,b,c,d,e are trained coefficients");
            System.Console.WriteLine($"   Example: VIX = {testWeights.VxxCoefficient:F4}*VXX + {testWeights.UvxyCoefficient:F4}*UVXY + {testWeights.SvxyCoefficient:F4}*SVXY + {testWeights.SpyCoefficient:F4}*SPY + {testWeights.Intercept:F4}");
            
            // Calculate example
            decimal exampleVix = testWeights.VxxCoefficient * priceSnapshot.VxxPrice +
                               testWeights.UvxyCoefficient * priceSnapshot.UvxyPrice +
                               testWeights.SvxyCoefficient * priceSnapshot.SvxyPrice +
                               testWeights.SpyCoefficient * priceSnapshot.SpyPrice +
                               testWeights.Intercept;
            
            System.Console.WriteLine($"   With current prices: VIX ≈ {exampleVix:F2}");
            
            System.Console.WriteLine("\n✅ SyntheticVixService demo completed successfully!");
            return Task.CompletedTask;
        }
        catch (Exception ex)
        {
            System.Console.WriteLine($"❌ Demo failed: {ex.Message}");
            System.Console.WriteLine("   This is expected without proper Redis configuration");
            return Task.CompletedTask;
        }
    }
}

/// <summary>
/// Console entry point for the demo
/// Usage: dotnet run synthetic-vix-demo
/// </summary>
public static class SyntheticVixDemoCommand
{
    public static async Task<int> ExecuteAsync(string[] args)
    {
        if (args.Length > 0 && args[0].Equals("synthetic-vix-demo", StringComparison.OrdinalIgnoreCase))
        {
            await SyntheticVixDemo.RunDemoAsync();
            return 0;
        }
        
        return -1; // Not handled
    }
}
