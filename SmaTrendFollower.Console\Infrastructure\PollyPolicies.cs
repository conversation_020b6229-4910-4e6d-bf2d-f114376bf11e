using Microsoft.Extensions.Logging;
using <PERSON>;
using Polly.Extensions.Http;
using System.Net;

namespace SmaTrendFollower.Infrastructure;

/// <summary>
/// Centralized Polly resilience policies for HTTP clients
/// Provides robust retry and circuit-breaker patterns for external API calls
/// </summary>
public static class PollyPolicies
{
    /// <summary>
    /// Retry policy with exponential back-off for transient failures
    /// - 3 attempts with exponential back-off (2s, 4s, 8s)
    /// - Handles 5xx errors, network failures, and 429 rate limiting
    /// </summary>
    public static IAsyncPolicy<HttpResponseMessage> RetryPolicy =>
        HttpPolicyExtensions
            .HandleTransientHttpError()               // 5xx + network failures
            .OrResult(msg => msg.StatusCode == (HttpStatusCode)429) // Rate limiting
            .WaitAndRetryAsync(3, retryAttempt =>
                TimeSpan.FromSeconds(Math.Pow(2, retryAttempt))); // 2s, 4s, 8s

    /// <summary>
    /// Circuit breaker policy to prevent cascading failures
    /// - Opens after 5 consecutive failures
    /// - Stays open for 30 seconds before allowing test requests
    /// </summary>
    public static IAsyncPolicy<HttpResponseMessage> CircuitBreakerPolicy =>
        HttpPolicyExtensions
            .HandleTransientHttpError()
            .OrResult(msg => msg.StatusCode == (HttpStatusCode)429)
            .CircuitBreakerAsync(5, TimeSpan.FromSeconds(30));

    /// <summary>
    /// Enhanced retry policy with comprehensive logging
    /// - 3 attempts with exponential back-off and jitter
    /// - Detailed logging of retry attempts with URL and failure reasons
    /// </summary>
    public static IAsyncPolicy<HttpResponseMessage> GetRetryPolicyWithLogging(ILogger logger, string apiName)
    {
        return HttpPolicyExtensions
            .HandleTransientHttpError()
            .OrResult(msg => msg.StatusCode == (HttpStatusCode)429)
            .WaitAndRetryAsync(
                retryCount: 3,
                sleepDurationProvider: retryAttempt => 
                    TimeSpan.FromSeconds(Math.Pow(2, retryAttempt)) + 
                    TimeSpan.FromMilliseconds(Random.Shared.Next(0, 1000)), // Add jitter
                onRetry: (outcome, timespan, retry, ctx) =>
                {
                    var url = outcome.Result?.RequestMessage?.RequestUri?.ToString() ?? "Unknown URL";
                    var reason = outcome.Exception?.Message ?? outcome.Result?.StatusCode.ToString() ?? "Unknown";
                    
                    logger.LogWarning("Retry #{Retry} for {ApiName} {Url} due to {Reason}. Waiting {Delay}ms",
                        retry, apiName, url, reason, timespan.TotalMilliseconds);
                });
    }

    /// <summary>
    /// Enhanced circuit breaker policy with comprehensive logging
    /// - Opens after 5 consecutive failures for 30 seconds
    /// - Logs circuit breaker state changes for monitoring
    /// </summary>
    public static IAsyncPolicy<HttpResponseMessage> GetCircuitBreakerPolicyWithLogging(ILogger logger, string apiName)
    {
        return HttpPolicyExtensions
            .HandleTransientHttpError()
            .OrResult(msg => msg.StatusCode == (HttpStatusCode)429)
            .CircuitBreakerAsync(
                handledEventsAllowedBeforeBreaking: 5,
                durationOfBreak: TimeSpan.FromSeconds(30),
                onBreak: (result, duration) =>
                {
                    var reason = result.Exception?.Message ?? result.Result?.StatusCode.ToString() ?? "Unknown";
                    logger.LogError("{ApiName} circuit breaker OPENED for {Duration}s. Last error: {Reason}",
                        apiName, duration.TotalSeconds, reason);
                },
                onReset: () =>
                {
                    logger.LogInformation("{ApiName} circuit breaker RESET - service recovered", apiName);
                },
                onHalfOpen: () =>
                {
                    logger.LogInformation("{ApiName} circuit breaker HALF-OPEN - testing service", apiName);
                });
    }

    /// <summary>
    /// Timeout policy to prevent hanging requests
    /// - 25 second timeout (slightly less than HttpClient default)
    /// </summary>
    public static IAsyncPolicy<HttpResponseMessage> TimeoutPolicy =>
        Policy.TimeoutAsync<HttpResponseMessage>(TimeSpan.FromSeconds(25));

    /// <summary>
    /// Combined policy wrapper for complete resilience
    /// - Combines timeout, retry, and circuit breaker policies
    /// - Applies policies in the correct order for optimal behavior
    /// </summary>
    public static IAsyncPolicy<HttpResponseMessage> GetCombinedPolicy(ILogger logger, string apiName)
    {
        var timeout = TimeoutPolicy;
        var retry = GetRetryPolicyWithLogging(logger, apiName);
        var circuitBreaker = GetCircuitBreakerPolicyWithLogging(logger, apiName);

        // Apply policies in order: Timeout -> Retry -> Circuit Breaker
        return Policy.WrapAsync(timeout, retry, circuitBreaker);
    }
}
