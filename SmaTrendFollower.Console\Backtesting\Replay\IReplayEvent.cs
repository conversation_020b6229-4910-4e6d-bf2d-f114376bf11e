namespace SmaTrendFollower.Backtesting.Replay;

/// <summary>
/// Base interface for all replay events in backtesting
/// </summary>
public interface IReplayEvent
{
    /// <summary>
    /// UTC timestamp of the event
    /// </summary>
    DateTime TimestampUtc { get; }
    
    /// <summary>
    /// Symbol associated with the event
    /// </summary>
    string Symbol { get; }
}

/// <summary>
/// Represents a trade event for replay backtesting
/// </summary>
public record TradeEvent(
    string Symbol,
    DateTime TimestampUtc,
    decimal Price,
    ulong Size
) : IReplayEvent;

/// <summary>
/// Represents a quote event for replay backtesting
/// </summary>
public record QuoteEvent(
    string Symbol,
    DateTime TimestampUtc,
    decimal Bid,
    decimal Ask
) : IReplayEvent;

/// <summary>
/// Comprehensive backtest summary with performance metrics
/// </summary>
public record BacktestSummary(
    decimal TotalPnl,
    double Sharpe,
    double MaxDrawdown,
    int Trades,
    double WinRate,
    DateTime StartDate,
    DateTime EndDate,
    decimal InitialCapital,
    decimal FinalCapital,
    string[] Symbols
)
{
    /// <summary>
    /// Default empty summary for initialization
    /// </summary>
    public static BacktestSummary Empty => new(
        TotalPnl: 0m,
        Sharpe: 0.0,
        MaxDrawdown: 0.0,
        Trades: 0,
        WinRate: 0.0,
        StartDate: DateTime.MinValue,
        EndDate: DateTime.MinValue,
        InitialCapital: 0m,
        FinalCapital: 0m,
        Symbols: Array.Empty<string>()
    );
}

/// <summary>
/// Equity curve point for charting
/// </summary>
public record EquityCurvePoint(
    DateTime Date,
    decimal Equity,
    decimal DrawdownPercent
);

/// <summary>
/// Trade record for backtesting results
/// </summary>
public record BacktestTrade(
    DateTime EntryTime,
    DateTime ExitTime,
    string Symbol,
    decimal Quantity,
    decimal EntryPrice,
    decimal ExitPrice,
    decimal PnL,
    string EntryReason,
    string ExitReason
);
