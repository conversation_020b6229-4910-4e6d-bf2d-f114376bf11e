using Microsoft.ML;
using Microsoft.ML.AutoML;
using Microsoft.Extensions.Logging;
using System.Globalization;

namespace SmaTrendFollower.MachineLearning.ModelTraining;

/// <summary>
/// Console tool for training position sizing ML model using LightGBM regression.
/// Trains a model to predict optimal equity risk percentage based on signal quality and market conditions.
/// </summary>
public class TrainPositionSizer
{
    private static readonly ILogger<TrainPositionSizer> _logger = 
        LoggerFactory.Create(builder => builder.AddConsole()).CreateLogger<TrainPositionSizer>();

    /// <summary>
    /// Main entry point for position sizer training
    /// Usage: TrainPositionSizer.exe positions.csv [output_model_path]
    /// </summary>
    // Commented out to avoid multiple entry point warnings
    /*
    public static async Task Main(string[] args)
    {
        try
        {
            if (args.Length == 0)
            {
                System.Console.WriteLine("Usage: TrainPositionSizer <positions.csv> [output_model_path]");
                System.Console.WriteLine("Example: TrainPositionSizer positions.csv Model/position_model.zip");
                return;
            }

            string csvPath = args[0];
            string modelPath = args.Length > 1 ? args[1] : "Model/position_model.zip";

            if (!File.Exists(csvPath))
            {
                System.Console.WriteLine($"❌ Error: CSV file not found: {csvPath}");
                return;
            }

            System.Console.WriteLine("🤖 Training Position Sizing Model with LightGBM");
            System.Console.WriteLine($"📊 Input CSV: {csvPath}");
            System.Console.WriteLine($"💾 Output Model: {modelPath}");
            System.Console.WriteLine();

            await TrainModelAsync(csvPath, modelPath);
        }
        catch (Exception ex)
        {
            System.Console.WriteLine($"❌ Error: {ex.Message}");
            _logger.LogError(ex, "Position sizer training failed");
            Environment.Exit(1);
        }
    }
    */

    /// <summary>
    /// Trains the position sizing model using LightGBM regression
    /// </summary>
    private static async Task TrainModelAsync(string csvPath, string modelPath)
    {
        var mlContext = new MLContext(seed: 42);

        System.Console.WriteLine("📈 Loading training data...");
        
        // Load data from CSV
        var data = mlContext.Data.LoadFromTextFile<PositionSizingRow>(
            csvPath, 
            hasHeader: true, 
            separatorChar: ',');

        // Get data preview
        var preview = data.Preview(maxRows: 10);
        // Preview loaded successfully
        
        // Validate data
        var dataView = mlContext.Data.CreateEnumerable<PositionSizingRow>(data, reuseRowObject: false).ToList();
        System.Console.WriteLine($"✅ Loaded {dataView.Count} training samples");

        if (dataView.Count < 100)
        {
            System.Console.WriteLine("⚠️  Warning: Very small dataset. Consider collecting more training data.");
        }

        // Display data statistics
        DisplayDataStatistics(dataView);

        System.Console.WriteLine("🔧 Configuring LightGBM regression pipeline...");

        // Create training pipeline with LightGBM
        var pipeline = mlContext.Transforms.Concatenate("Features", 
                nameof(PositionSizingRow.RankProb),
                nameof(PositionSizingRow.ATR_Pct),
                nameof(PositionSizingRow.AvgSpreadPct))
            .Append(mlContext.Regression.Trainers.LightGbm(
                labelColumnName: nameof(PositionSizingRow.EquityPctRisk),
                featureColumnName: "Features",
                numberOfLeaves: 31,
                minimumExampleCountPerLeaf: 20,
                learningRate: 0.1,
                numberOfIterations: 100));

        System.Console.WriteLine("🎯 Training model...");
        var startTime = DateTime.UtcNow;

        // Split data for training and validation
        var trainTestSplit = mlContext.Data.TrainTestSplit(data, testFraction: 0.2, seed: 42);

        // Train the model
        var model = pipeline.Fit(trainTestSplit.TrainSet);

        var trainingTime = DateTime.UtcNow - startTime;
        System.Console.WriteLine($"✅ Training completed in {trainingTime.TotalSeconds:F1} seconds");

        System.Console.WriteLine("📊 Evaluating model performance...");

        // Evaluate the model
        var predictions = model.Transform(trainTestSplit.TestSet);
        var metrics = mlContext.Regression.Evaluate(predictions, 
            labelColumnName: nameof(PositionSizingRow.EquityPctRisk));

        // Display evaluation metrics
        System.Console.WriteLine();
        System.Console.WriteLine("📈 Model Performance Metrics:");
        System.Console.WriteLine($"   R² (Coefficient of Determination): {metrics.RSquared:F4}");
        System.Console.WriteLine($"   Mean Absolute Error (MAE):         {metrics.MeanAbsoluteError:F6}");
        System.Console.WriteLine($"   Mean Squared Error (MSE):          {metrics.MeanSquaredError:F6}");
        System.Console.WriteLine($"   Root Mean Squared Error (RMSE):    {metrics.RootMeanSquaredError:F6}");
        System.Console.WriteLine();

        // Quality assessment
        if (metrics.RSquared > 0.7)
            System.Console.WriteLine("🎉 Excellent model quality (R² > 0.7)");
        else if (metrics.RSquared > 0.5)
            System.Console.WriteLine("✅ Good model quality (R² > 0.5)");
        else if (metrics.RSquared > 0.3)
            System.Console.WriteLine("⚠️  Fair model quality (R² > 0.3) - consider more data or feature engineering");
        else
            System.Console.WriteLine("❌ Poor model quality (R² ≤ 0.3) - model may not be reliable");

        System.Console.WriteLine("💾 Saving model...");

        // Ensure model directory exists
        var modelDirectory = Path.GetDirectoryName(modelPath);
        if (!string.IsNullOrEmpty(modelDirectory) && !Directory.Exists(modelDirectory))
        {
            Directory.CreateDirectory(modelDirectory);
        }

        // Save the model
        mlContext.Model.Save(model, data.Schema, modelPath);

        // Save model metadata
        var metadataPath = Path.ChangeExtension(modelPath, ".metadata.json");
        var metadata = new
        {
            TrainedAt = DateTime.UtcNow,
            TrainingDuration = trainingTime,
            TrainingSamples = dataView.Count,
            TestSamples = (int)(dataView.Count * 0.2),
            RSquared = metrics.RSquared,
            MeanAbsoluteError = metrics.MeanAbsoluteError,
            RootMeanSquaredError = metrics.RootMeanSquaredError,
            ModelType = "LightGBM Regression",
            Features = new[] { "RankProb", "ATR_Pct", "AvgSpreadPct" },
            Label = "EquityPctRisk"
        };

        await File.WriteAllTextAsync(metadataPath, 
            System.Text.Json.JsonSerializer.Serialize(metadata, new System.Text.Json.JsonSerializerOptions 
            { 
                WriteIndented = true 
            }));

        System.Console.WriteLine($"✅ Model saved to: {modelPath}");
        System.Console.WriteLine($"📋 Metadata saved to: {metadataPath}");
        System.Console.WriteLine($"🎯 Final R² Score: {metrics.RSquared:F4}");
    }

    /// <summary>
    /// Displays statistics about the training data
    /// </summary>
    private static void DisplayDataStatistics(List<PositionSizingRow> data)
    {
        System.Console.WriteLine();
        System.Console.WriteLine("📊 Training Data Statistics:");
        System.Console.WriteLine($"   Total Samples: {data.Count:N0}");
        System.Console.WriteLine();

        System.Console.WriteLine("   RankProb:");
        System.Console.WriteLine($"     Min: {data.Min(d => d.RankProb):F4}");
        System.Console.WriteLine($"     Max: {data.Max(d => d.RankProb):F4}");
        System.Console.WriteLine($"     Avg: {data.Average(d => d.RankProb):F4}");

        System.Console.WriteLine("   ATR_Pct:");
        System.Console.WriteLine($"     Min: {data.Min(d => d.ATR_Pct):F4}");
        System.Console.WriteLine($"     Max: {data.Max(d => d.ATR_Pct):F4}");
        System.Console.WriteLine($"     Avg: {data.Average(d => d.ATR_Pct):F4}");

        System.Console.WriteLine("   AvgSpreadPct:");
        System.Console.WriteLine($"     Min: {data.Min(d => d.AvgSpreadPct):F6}");
        System.Console.WriteLine($"     Max: {data.Max(d => d.AvgSpreadPct):F6}");
        System.Console.WriteLine($"     Avg: {data.Average(d => d.AvgSpreadPct):F6}");

        System.Console.WriteLine("   EquityPctRisk (Target):");
        System.Console.WriteLine($"     Min: {data.Min(d => d.EquityPctRisk):F4}");
        System.Console.WriteLine($"     Max: {data.Max(d => d.EquityPctRisk):F4}");
        System.Console.WriteLine($"     Avg: {data.Average(d => d.EquityPctRisk):F4}");
        System.Console.WriteLine();
    }
}

/// <summary>
/// Data structure for position sizing training data
/// </summary>
public class PositionSizingRow
{
    public float RankProb { get; set; }
    public float ATR_Pct { get; set; }
    public float AvgSpreadPct { get; set; }
    public float EquityPctRisk { get; set; }

    // Parameterless constructor for ML.NET
    public PositionSizingRow() { }

    // Constructor with parameters for convenience
    public PositionSizingRow(float rankProb, float atrPct, float avgSpreadPct, float equityPctRisk)
    {
        RankProb = rankProb;
        ATR_Pct = atrPct;
        AvgSpreadPct = avgSpreadPct;
        EquityPctRisk = equityPctRisk;
    }
}
