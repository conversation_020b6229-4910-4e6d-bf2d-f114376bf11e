using Alpaca.Markets;
using Microsoft.Extensions.Logging;

namespace SmaTrendFollower.Services;

/// <summary>
/// Filters trading signals based on volatility and market conditions.
/// Blocks trades during high-risk volatility environments.
/// Uses VIXResolverService for robust VIX data retrieval with 7-level fallback.
/// </summary>
public sealed class VolatilityFilter : IVolatilityFilter
{
    private readonly IVIXResolverService _vixResolverService;
    private readonly ILogger<VolatilityFilter> _logger;
    private readonly VolatilityFilterConfig _config;

    public VolatilityFilter(
        IVIXResolverService vixResolverService,
        ILogger<VolatilityFilter> logger,
        VolatilityFilterConfig? config = null)
    {
        _vixResolverService = vixResolverService ?? throw new ArgumentNullException(nameof(vixResolverService));
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        _config = config ?? new VolatilityFilterConfig();
    }

    /// <summary>
    /// Determines if trading is allowed based on current volatility conditions
    /// </summary>
    public async Task<bool> IsEligibleAsync(string symbol, IReadOnlyList<IBar> bars)
    {
        try
        {
            var symbolVolatility = AnalyzeSymbolVolatility(symbol, bars);
            var marketVolatility = await GetMarketVolatilityAsync();

            var isEligible = symbolVolatility.IsEligible && marketVolatility.IsEligible;

            if (isEligible)
            {
                _logger.LogDebug("Volatility filter PASSED for {Symbol}: ATR={ATR:F2}, VIX={VIX:F1}, Market={Market}",
                    symbol, symbolVolatility.AtrPercent * 100, marketVolatility.VixLevel, marketVolatility.Regime);
            }
            else
            {
                _logger.LogDebug("Volatility filter FAILED for {Symbol}: ATR={ATR:F2}, VIX={VIX:F1}, Market={Market}",
                    symbol, symbolVolatility.AtrPercent * 100, marketVolatility.VixLevel, marketVolatility.Regime);
            }

            return isEligible;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error analyzing volatility for {Symbol}", symbol);
            return false; // Fail safe - block trades on error
        }
    }

    /// <summary>
    /// Analyzes symbol-specific volatility characteristics
    /// </summary>
    public SymbolVolatilityAnalysis AnalyzeSymbolVolatility(string symbol, IReadOnlyList<IBar> bars)
    {
        if (bars.Count < _config.MinimumBarsRequired)
        {
            return new SymbolVolatilityAnalysis(symbol, 0, 0, 0, 0, 0, false, "Insufficient data");
        }

        var currentPrice = bars.Last().Close;
        var atr14 = CalculateATR(bars, 14);
        var atr20 = CalculateATR(bars, 20);
        var atrPercent = currentPrice > 0 ? atr14 / currentPrice : 0;
        
        var volatilityRank = CalculateVolatilityRank(bars, 50);
        var priceStability = CalculatePriceStability(bars, 20);

        var isEligible = atrPercent <= _config.MaximumAtrPercent &&
                        atrPercent >= _config.MinimumAtrPercent &&
                        volatilityRank <= _config.MaximumVolatilityRank &&
                        priceStability >= _config.MinimumPriceStability;

        var reason = !isEligible ? GetRejectionReason(atrPercent, volatilityRank, priceStability) : "Eligible";

        return new SymbolVolatilityAnalysis(
            symbol,
            atr14,
            atr20,
            atrPercent,
            volatilityRank,
            priceStability,
            isEligible,
            reason
        );
    }

    /// <summary>
    /// Gets current market volatility conditions using VIXResolverService
    /// </summary>
    public async Task<MarketVolatilityAnalysis> GetMarketVolatilityAsync()
    {
        try
        {
            // Use VIXResolverService with 15-minute freshness requirement
            var vixResult = await _vixResolverService.GetVixWithFreshnessAsync(_config.VixCacheExpiry);

            if (vixResult.VixValue.HasValue)
            {
                var vixValue = vixResult.VixValue.Value;

                // Get historical context for percentile calculation
                // For now, use simple heuristics - could be enhanced with historical data
                var twentyDayAvg = vixValue * 0.9m; // Rough approximation
                var percentileRank = CalculateVixPercentileRank(vixValue);
                var isSpike = vixValue > twentyDayAvg * 1.5m;

                var regime = DetermineVolatilityRegime(vixValue);
                var isEligible = vixValue <= _config.MaximumVixLevel &&
                               regime != MarketVolatilityRegime.Crisis &&
                               !isSpike;

                var reason = !isEligible ? GetMarketRejectionReason(vixValue, regime, isSpike) : "Eligible";

                _logger.LogDebug("VIX analysis: Value={VIX:F1}, Regime={Regime}, Eligible={Eligible}, Source={Source}",
                    vixValue, regime, isEligible, vixResult.Source);

                return new MarketVolatilityAnalysis(
                    vixValue,
                    twentyDayAvg,
                    percentileRank,
                    regime,
                    isEligible,
                    reason
                );
            }
            else
            {
                _logger.LogWarning("VIX resolver returned no value after trying all fallback levels. Error: {Error}", vixResult.ErrorMessage);

                // Only use defaults as absolute last resort when all 7 fallback levels fail
                return new MarketVolatilityAnalysis(
                    25m, // Conservative VIX estimate
                    22m,
                    60m,
                    MarketVolatilityRegime.Elevated,
                    false, // Conservative: block trading when VIX unavailable
                    $"All VIX sources failed: {vixResult.ErrorMessage}"
                );
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Unexpected error in VIX analysis");

            // Conservative fallback on unexpected errors
            return new MarketVolatilityAnalysis(
                30m, // High VIX estimate for safety
                25m,
                80m,
                MarketVolatilityRegime.Crisis,
                false, // Block trading on errors
                $"VIX analysis error: {ex.Message}"
            );
        }
    }

    /// <summary>
    /// Calculate VIX percentile rank using simple heuristics
    /// </summary>
    private decimal CalculateVixPercentileRank(decimal vixValue)
    {
        // Simple heuristic mapping based on typical VIX ranges
        return vixValue switch
        {
            < 12 => 10m,   // Very low volatility
            < 16 => 25m,   // Low volatility
            < 20 => 50m,   // Normal volatility
            < 25 => 75m,   // Elevated volatility
            < 30 => 85m,   // High volatility
            _ => 95m       // Very high/crisis volatility
        };
    }

    /// <summary>
    /// Get market rejection reason for VIX-based filtering
    /// </summary>
    private string GetMarketRejectionReason(decimal vixValue, MarketVolatilityRegime regime, bool isSpike)
    {
        if (vixValue > _config.MaximumVixLevel)
            return $"VIX too high: {vixValue:F1} > {_config.MaximumVixLevel}";
        if (regime == MarketVolatilityRegime.Crisis)
            return $"Crisis regime detected (VIX: {vixValue:F1})";
        if (isSpike)
            return $"VIX spike detected: {vixValue:F1}";
        return "Unknown rejection reason";
    }

    /// <summary>
    /// Calculates Average True Range
    /// </summary>
    private decimal CalculateATR(IReadOnlyList<IBar> bars, int periods)
    {
        if (bars.Count <= periods) return 0;

        var trueRanges = new List<decimal>();
        
        for (int i = 1; i < bars.Count; i++)
        {
            var high = bars[i].High;
            var low = bars[i].Low;
            var prevClose = bars[i - 1].Close;
            
            var tr1 = high - low;
            var tr2 = Math.Abs(high - prevClose);
            var tr3 = Math.Abs(low - prevClose);
            
            trueRanges.Add(Math.Max(tr1, Math.Max(tr2, tr3)));
        }

        return trueRanges.TakeLast(periods).Average();
    }

    /// <summary>
    /// Calculates volatility rank (current ATR vs historical ATR)
    /// </summary>
    private decimal CalculateVolatilityRank(IReadOnlyList<IBar> bars, int lookbackPeriods)
    {
        if (bars.Count < lookbackPeriods + 14) return 0;

        var currentAtr = CalculateATR(bars, 14);
        var historicalAtrs = new List<decimal>();

        for (int i = 14; i <= lookbackPeriods; i++)
        {
            var endIndex = bars.Count - i;
            if (endIndex >= 14)
            {
                var historicalBars = bars.Take(endIndex).ToList();
                historicalAtrs.Add(CalculateATR(historicalBars, 14));
            }
        }

        if (!historicalAtrs.Any()) return 0;

        var rank = historicalAtrs.Count(atr => currentAtr > atr) / (decimal)historicalAtrs.Count;
        return rank;
    }

    /// <summary>
    /// Calculates price stability (inverse of price volatility)
    /// </summary>
    private decimal CalculatePriceStability(IReadOnlyList<IBar> bars, int periods)
    {
        if (bars.Count < periods) return 0;

        var recentBars = bars.TakeLast(periods).ToList();
        var returns = new List<decimal>();

        for (int i = 1; i < recentBars.Count; i++)
        {
            var prevClose = recentBars[i - 1].Close;
            var currentClose = recentBars[i].Close;
            
            if (prevClose > 0)
            {
                returns.Add((currentClose - prevClose) / prevClose);
            }
        }

        if (!returns.Any()) return 0;

        var avgReturn = returns.Average();
        var variance = returns.Sum(r => (r - avgReturn) * (r - avgReturn)) / returns.Count;
        var standardDeviation = (decimal)Math.Sqrt((double)variance);

        // Return inverse of volatility (higher = more stable)
        return standardDeviation > 0 ? 1 / (1 + standardDeviation * 100) : 1;
    }

    /// <summary>
    /// Determines volatility regime based on VIX level
    /// </summary>
    private MarketVolatilityRegime DetermineVolatilityRegime(decimal vixLevel)
    {
        return vixLevel switch
        {
            < 15 => MarketVolatilityRegime.Low,
            < 20 => MarketVolatilityRegime.Normal,
            < 25 => MarketVolatilityRegime.Elevated,
            < 35 => MarketVolatilityRegime.High,
            _ => MarketVolatilityRegime.Crisis
        };
    }

    /// <summary>
    /// Calculates percentile rank of current value vs historical values
    /// </summary>
    private decimal CalculatePercentileRank(decimal currentValue, List<decimal> historicalValues)
    {
        if (!historicalValues.Any()) return 50;

        var count = historicalValues.Count(v => currentValue > v);
        return (decimal)count / historicalValues.Count * 100;
    }

    /// <summary>
    /// Gets rejection reason for symbol volatility
    /// </summary>
    private string GetRejectionReason(decimal atrPercent, decimal volatilityRank, decimal priceStability)
    {
        var reasons = new List<string>();
        
        if (atrPercent > _config.MaximumAtrPercent)
            reasons.Add($"ATR too high ({atrPercent:P1} > {_config.MaximumAtrPercent:P1})");
        
        if (atrPercent < _config.MinimumAtrPercent)
            reasons.Add($"ATR too low ({atrPercent:P1} < {_config.MinimumAtrPercent:P1})");
        
        if (volatilityRank > _config.MaximumVolatilityRank)
            reasons.Add($"Volatility rank too high ({volatilityRank:P0} > {_config.MaximumVolatilityRank:P0})");
        
        if (priceStability < _config.MinimumPriceStability)
            reasons.Add($"Price instability ({priceStability:F2} < {_config.MinimumPriceStability:F2})");

        return string.Join("; ", reasons);
    }



    public void Dispose()
    {
        // No resources to dispose since we removed the cache lock
    }
}

/// <summary>
/// Interface for volatility filtering
/// </summary>
public interface IVolatilityFilter
{
    Task<bool> IsEligibleAsync(string symbol, IReadOnlyList<IBar> bars);
    SymbolVolatilityAnalysis AnalyzeSymbolVolatility(string symbol, IReadOnlyList<IBar> bars);
    Task<MarketVolatilityAnalysis> GetMarketVolatilityAsync();
}

/// <summary>
/// Configuration for volatility filter
/// </summary>
public record VolatilityFilterConfig(
    decimal MaximumAtrPercent = 0.05m,        // 5% maximum ATR/Price ratio
    decimal MinimumAtrPercent = 0.005m,       // 0.5% minimum ATR/Price ratio
    decimal MaximumVolatilityRank = 0.8m,     // 80th percentile maximum
    decimal MinimumPriceStability = 0.3m,     // Minimum stability score
    decimal MaximumVixLevel = 30m,            // Maximum VIX level
    TimeSpan VixCacheExpiry = default,        // VIX cache expiry
    int MinimumBarsRequired = 50              // Minimum bars for analysis
)
{
    public VolatilityFilterConfig() : this(0.05m, 0.005m, 0.8m, 0.3m, 30m, TimeSpan.FromMinutes(15), 50) { }
}

/// <summary>
/// Symbol-specific volatility analysis
/// </summary>
public record SymbolVolatilityAnalysis(
    string Symbol,
    decimal Atr14,
    decimal Atr20,
    decimal AtrPercent,
    decimal VolatilityRank,
    decimal PriceStability,
    bool IsEligible,
    string Reason
);

/// <summary>
/// Market-wide volatility analysis
/// </summary>
public record MarketVolatilityAnalysis(
    decimal VixLevel,
    decimal VixTwentyDayAverage,
    decimal VixPercentileRank,
    MarketVolatilityRegime Regime,
    bool IsEligible,
    string Reason
);



/// <summary>
/// Market volatility regime classification
/// </summary>
public enum MarketVolatilityRegime
{
    Unknown,
    Low,      // VIX < 15
    Normal,   // VIX 15-20
    Elevated, // VIX 20-25
    High,     // VIX 25-35
    Crisis    // VIX > 35
}
