using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using StackExchange.Redis;
using System.Collections.Concurrent;
using System.Diagnostics;

namespace SmaTrendFollower.Services;

/// <summary>
/// Service for optimized Redis connection management with connection pooling and health monitoring
/// </summary>
public interface IOptimizedRedisConnectionService : IDisposable
{
    /// <summary>
    /// Gets a database instance with connection pooling
    /// </summary>
    Task<IDatabase> GetDatabaseAsync(int database = 0);

    /// <summary>
    /// Gets connection health status
    /// </summary>
    Task<RedisHealthStatus> GetHealthStatusAsync();

    /// <summary>
    /// Gets connection performance metrics
    /// </summary>
    RedisPerformanceMetrics GetPerformanceMetrics();

    /// <summary>
    /// Warms up the connection pool
    /// </summary>
    Task WarmupConnectionPoolAsync();
}

/// <summary>
/// Optimized Redis connection service implementation
/// </summary>
public sealed class OptimizedRedisConnectionService : IOptimizedRedisConnectionService
{
    private readonly IConfiguration _configuration;
    private readonly ILogger<OptimizedRedisConnectionService> _logger;
    private readonly ConcurrentDictionary<int, IDatabase> _databaseCache;
    private readonly object _connectionLock = new();
    private ConnectionMultiplexer? _connectionMultiplexer;
    private readonly RedisPerformanceTracker _performanceTracker;
    private bool _disposed;

    public OptimizedRedisConnectionService(
        IConfiguration configuration,
        ILogger<OptimizedRedisConnectionService> logger)
    {
        _configuration = configuration;
        _logger = logger;
        _databaseCache = new ConcurrentDictionary<int, IDatabase>();
        _performanceTracker = new RedisPerformanceTracker();
    }

    public async Task<IDatabase> GetDatabaseAsync(int database = 0)
    {
        if (_disposed)
            throw new ObjectDisposedException(nameof(OptimizedRedisConnectionService));

        // Return cached database if available
        if (_databaseCache.TryGetValue(database, out var cachedDb))
        {
            return cachedDb;
        }

        // Ensure connection is established
        await EnsureConnectionAsync();

        // Get and cache the database
        var db = _connectionMultiplexer!.GetDatabase(database);
        _databaseCache.TryAdd(database, db);

        _logger.LogDebug("Created database connection for database {Database}", database);
        return db;
    }

    public async Task<RedisHealthStatus> GetHealthStatusAsync()
    {
        try
        {
            if (_connectionMultiplexer == null || !_connectionMultiplexer.IsConnected)
            {
                return new RedisHealthStatus(false, "Not connected", null, null);
            }

            var stopwatch = Stopwatch.StartNew();
            var db = await GetDatabaseAsync();
            var pingResult = await db.PingAsync();
            stopwatch.Stop();

            var latencyMs = stopwatch.Elapsed.TotalMilliseconds;
            var isHealthy = latencyMs < 100; // Consider healthy if ping < 100ms

            return new RedisHealthStatus(
                isHealthy,
                isHealthy ? "Healthy" : $"High latency: {latencyMs:F1}ms",
                latencyMs,
                null); // GetCounters() returns ServerCounters, not ConnectionCounters
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Redis health check failed");
            return new RedisHealthStatus(false, ex.Message, null, null);
        }
    }

    public RedisPerformanceMetrics GetPerformanceMetrics()
    {
        return _performanceTracker.GetMetrics();
    }

    public async Task WarmupConnectionPoolAsync()
    {
        try
        {
            _logger.LogInformation("Warming up Redis connection pool");

            // Pre-create connections for common databases
            var databases = new[] { 0, 1, 2 }; // Common database numbers
            var warmupTasks = databases.Select(async db =>
            {
                try
                {
                    var database = await GetDatabaseAsync(db);
                    await database.PingAsync();
                    _logger.LogDebug("Warmed up connection to database {Database}", db);
                }
                catch (Exception ex)
                {
                    _logger.LogWarning(ex, "Failed to warm up connection to database {Database}", db);
                }
            });

            await Task.WhenAll(warmupTasks);
            _logger.LogInformation("Redis connection pool warmup completed");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Redis connection pool warmup failed");
        }
    }

    private Task EnsureConnectionAsync()
    {
        if (_connectionMultiplexer?.IsConnected == true)
            return Task.CompletedTask;

        lock (_connectionLock)
        {
            if (_connectionMultiplexer?.IsConnected == true)
                return Task.CompletedTask;

            try
            {
                _connectionMultiplexer?.Dispose();

                var configOptions = GetOptimizedConnectionOptions();
                _connectionMultiplexer = ConnectionMultiplexer.Connect(configOptions);

                // Subscribe to connection events
                _connectionMultiplexer.ConnectionFailed += OnConnectionFailed;
                _connectionMultiplexer.ConnectionRestored += OnConnectionRestored;
                _connectionMultiplexer.InternalError += OnInternalError;

                _logger.LogInformation("Redis connection established successfully");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to establish Redis connection");
                throw;
            }
        }

        return Task.CompletedTask;
    }

    private ConfigurationOptions GetOptimizedConnectionOptions()
    {
        var redisUrl = _configuration["REDIS_URL"] ?? "localhost:6379";
        var redisPassword = _configuration["REDIS_PASSWORD"];

        var configOptions = ConfigurationOptions.Parse(redisUrl);

        // Optimized connection settings for high-performance trading
        configOptions.AbortOnConnectFail = false; // Don't fail immediately
        configOptions.ConnectTimeout = 5000; // 5 second connect timeout
        configOptions.SyncTimeout = 2000; // 2 second sync timeout
        configOptions.AsyncTimeout = 5000; // 5 second async timeout
        configOptions.ConnectRetry = 3; // Retry connection 3 times
        configOptions.ReconnectRetryPolicy = new ExponentialRetry(1000); // Exponential backoff
        
        // Connection pooling settings
        configOptions.KeepAlive = 60; // Keep alive every 60 seconds
        configOptions.DefaultDatabase = 0;
        
        // Performance optimizations
        configOptions.CommandMap = CommandMap.Create(new HashSet<string>
        {
            // Disable potentially slow commands for performance
            "FLUSHDB", "FLUSHALL", "KEYS", "SHUTDOWN"
        }, available: false);

        // SSL/TLS settings if needed
        if (redisUrl.StartsWith("rediss://"))
        {
            configOptions.Ssl = true;
            configOptions.SslProtocols = System.Security.Authentication.SslProtocols.Tls12;
        }

        if (!string.IsNullOrEmpty(redisPassword))
        {
            configOptions.Password = redisPassword;
        }

        return configOptions;
    }

    private void OnConnectionFailed(object? sender, ConnectionFailedEventArgs e)
    {
        _logger.LogWarning("Redis connection failed: {Exception}", e.Exception?.Message);
        _performanceTracker.RecordConnectionFailure();
    }

    private void OnConnectionRestored(object? sender, ConnectionFailedEventArgs e)
    {
        _logger.LogInformation("Redis connection restored");
        _performanceTracker.RecordConnectionRestored();
    }

    private void OnInternalError(object? sender, InternalErrorEventArgs e)
    {
        _logger.LogError(e.Exception, "Redis internal error: {Origin}", e.Origin);
        _performanceTracker.RecordInternalError();
    }

    public void Dispose()
    {
        if (_disposed)
            return;

        _disposed = true;

        try
        {
            _connectionMultiplexer?.Dispose();
            _logger.LogInformation("Redis connection disposed");
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Error disposing Redis connection");
        }
    }
}

/// <summary>
/// Redis health status information
/// </summary>
public readonly record struct RedisHealthStatus(
    bool IsHealthy,
    string Status,
    double? LatencyMs,
    ConnectionCounters? Counters
);

/// <summary>
/// Redis performance metrics
/// </summary>
public readonly record struct RedisPerformanceMetrics(
    int TotalConnections,
    int FailedConnections,
    int RestoredConnections,
    int InternalErrors,
    double AverageLatencyMs,
    DateTime LastConnectionTime
);

/// <summary>
/// Performance tracker for Redis operations
/// </summary>
internal class RedisPerformanceTracker
{
    private int _totalConnections;
    private int _failedConnections;
    private int _restoredConnections;
    private int _internalErrors;
    private readonly List<double> _latencies = new();
    private DateTime _lastConnectionTime = DateTime.UtcNow;

    public void RecordConnection()
    {
        Interlocked.Increment(ref _totalConnections);
    }

    public void RecordConnectionFailure()
    {
        Interlocked.Increment(ref _failedConnections);
    }

    public void RecordConnectionRestored()
    {
        Interlocked.Increment(ref _restoredConnections);
        _lastConnectionTime = DateTime.UtcNow;
    }

    public void RecordInternalError()
    {
        Interlocked.Increment(ref _internalErrors);
    }

    public RedisPerformanceMetrics GetMetrics()
    {
        var averageLatency = _latencies.Count > 0 ? _latencies.Average() : 0.0;
        
        return new RedisPerformanceMetrics(
            _totalConnections,
            _failedConnections,
            _restoredConnections,
            _internalErrors,
            averageLatency,
            _lastConnectionTime
        );
    }
}
