using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using SmaTrendFollower.Configuration;
using SmaTrendFollower.Monitoring;
using SmaTrendFollower.Services;
using Serilog;

namespace SmaTrendFollower.Examples;

/// <summary>
/// Demonstrates Prometheus metrics collection by exercising instrumented services
/// </summary>
public static class PrometheusMetricsDemo
{
    public static async Task RunAsync()
    {
        Log.Information("🔬 Starting Prometheus Metrics Demo...");

        try
        {
            using var host = Host.CreateDefaultBuilder()
                .UseSerilog()
                .ConfigureServices((context, services) =>
                {
                    services.AddFullTradingSystem(context.Configuration);
                    services.AddTradingServiceImplementation(useEnhanced: false); // Use simple services for demo
                })
                .Build();

            using var scope = host.Services.CreateScope();

            // Get services
            var signalGenerator = scope.ServiceProvider.GetRequiredService<ISignalGenerator>();
            var universeProvider = scope.ServiceProvider.GetRequiredService<IUniverseProvider>();
            // Note: PrometheusMetricsDemo is static, so we'll use a different logger type
            var loggerFactory = scope.ServiceProvider.GetRequiredService<ILoggerFactory>();
            var logger = loggerFactory.CreateLogger("PrometheusMetricsDemo");

            Log.Information("📊 Testing signal generation metrics...");

            // Test 1: Generate signals to trigger metrics
            Log.Information("🎯 Generating trading signals (this will update metrics)...");
            var signals = await signalGenerator.RunAsync(5);
            var signalsList = signals.ToList();
            
            Log.Information("✅ Generated {Count} signals", signalsList.Count);

            // Test 2: Get universe to trigger universe size metric
            Log.Information("🌍 Getting universe symbols (this will update universe size metric)...");
            var universe = await universeProvider.GetSymbolsAsync();
            var universeList = universe.ToList();
            
            Log.Information("✅ Universe contains {Count} symbols", universeList.Count);

            // Test 3: Manually increment some metrics for demonstration
            Log.Information("📈 Manually updating metrics for demonstration...");
            
            // Simulate some WebSocket reconnections
            MetricsRegistry.WsReconnects.WithLabels("Equity", "connection_lost").Inc();
            MetricsRegistry.WsReconnects.WithLabels("Index", "rate_limit").Inc();
            
            // Simulate some application errors
            MetricsRegistry.ApplicationErrors.WithLabels("TestComponent", "TestError").Inc();
            
            // Simulate some market data requests
            MetricsRegistry.MarketDataRequests.WithLabels("Alpaca", "success").Inc(10);
            MetricsRegistry.MarketDataRequests.WithLabels("Polygon", "success").Inc(15);
            MetricsRegistry.MarketDataRequests.WithLabels("Alpaca", "error").Inc(2);
            
            // Set some gauge values (convert decimal to double for Prometheus)
            MetricsRegistry.PortfolioValueUsd.Set((double)125000.50m);
            MetricsRegistry.CashBalanceUsd.Set((double)25000.00m);
            MetricsRegistry.DailyPnlUsd.Set((double)1250.75m);
            MetricsRegistry.CurrentPositions.Set(8);
            MetricsRegistry.CurrentExposurePercent.Set((double)80.5m);
            MetricsRegistry.CurrentVix.Set((double)16.75m);
            MetricsRegistry.MarketRegime.Set(1); // Bull market
            
            // Record some latencies
            MetricsRegistry.MarketDataLatencyMs.Observe(125.5);
            MetricsRegistry.MarketDataLatencyMs.Observe(89.2);
            MetricsRegistry.MarketDataLatencyMs.Observe(156.8);
            
            MetricsRegistry.RedisLatencyMs.Observe(2.1);
            MetricsRegistry.RedisLatencyMs.Observe(1.8);
            MetricsRegistry.RedisLatencyMs.Observe(3.2);

            Log.Information("✅ Metrics updated successfully");

            // Test 4: Display current metric values
            Log.Information("📋 Current Metric Summary:");
            Log.Information("  • Universe Size: {Size}", universeList.Count);
            Log.Information("  • Signals Generated: {Count}", signalsList.Count);
            Log.Information("  • Portfolio Value: $125,000.50");
            Log.Information("  • Cash Balance: $25,000.00");
            Log.Information("  • Daily P&L: $1,250.75");
            Log.Information("  • Current Positions: 8");
            Log.Information("  • Market Exposure: 80.5%");
            Log.Information("  • VIX Level: 16.75");
            Log.Information("  • Market Regime: Bull (1.0)");

            Log.Information("🌐 Metrics are now available at: http://localhost:5000/metrics");
            Log.Information("📊 You can view the Prometheus metrics in your browser");
            Log.Information("🔍 Look for metrics starting with:");
            Log.Information("  • trades_total");
            Log.Information("  • signals_total");
            Log.Information("  • signal_latency_ms");
            Log.Information("  • universe_size");
            Log.Information("  • websocket_reconnect_total");
            Log.Information("  • portfolio_value_usd");
            Log.Information("  • market_data_requests_total");
            Log.Information("  • application_errors_total");

            Log.Information("✅ Prometheus Metrics Demo completed successfully!");
        }
        catch (Exception ex)
        {
            Log.Error(ex, "❌ Error in Prometheus Metrics Demo");
            throw;
        }
    }
}
