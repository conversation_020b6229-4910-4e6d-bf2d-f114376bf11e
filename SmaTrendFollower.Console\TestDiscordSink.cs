using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Serilog;
using Serilog.Events;
using SmaTrendFollower.Infrastructure;

namespace SmaTrendFollower.Console;

/// <summary>
/// Simple test program to verify Discord sink functionality
/// </summary>
public static class TestDiscordSink
{
    public static async Task RunAsync()
    {
        // Configure Serilog with Discord sink
        var discordBotToken = Environment.GetEnvironmentVariable("DISCORD_BOT_TOKEN");
        var discordChannelId = Environment.GetEnvironmentVariable("DISCORD_CHANNEL_ID");

        var loggerConfig = new LoggerConfiguration()
            .MinimumLevel.Information()
            .WriteTo.Console()
            .WriteTo.File("logs/discord-sink-test-.log",
                          rollingInterval: RollingInterval.Day,
                          retainedFileCountLimit: 7);

        // Add Discord sink if configured
        if (!string.IsNullOrEmpty(discordBotToken) && !string.IsNullOrEmpty(discordChannelId))
        {
            loggerConfig.WriteTo.Discord(restrictedToMinimumLevel: LogEventLevel.Warning);
            System.Console.WriteLine("✅ Discord sink configured");
        }
        else
        {
            System.Console.WriteLine("❌ Discord not configured - missing DISCORD_BOT_TOKEN or DISCORD_CHANNEL_ID");
            return;
        }

        Log.Logger = loggerConfig.CreateLogger();

        try
        {
            Log.Information("🧪 Starting Discord sink test");
            
            // Test different log levels
            Log.Debug("This is a debug message - should NOT appear in Discord");
            Log.Information("This is an info message - should NOT appear in Discord");
            
            // These should appear in Discord
            Log.Warning("🟡 TEST WARNING: Discord sink test warning message");
            await Task.Delay(1000); // Give time for async Discord message
            
            Log.Error("🔴 TEST ERROR: Discord sink test error message");
            await Task.Delay(1000);
            
            // Test with exception
            try
            {
                throw new InvalidOperationException("Test exception for Discord sink");
            }
            catch (Exception ex)
            {
                Log.Error(ex, "🔥 TEST ERROR WITH EXCEPTION: Discord sink test with exception details");
            }
            
            await Task.Delay(2000); // Give time for all messages to be sent
            
            Log.Information("✅ Discord sink test completed - check Discord channel for messages");
        }
        catch (Exception ex)
        {
            Log.Fatal(ex, "💀 FATAL ERROR during Discord sink test");
        }
        finally
        {
            Log.CloseAndFlush();
        }
    }
}
