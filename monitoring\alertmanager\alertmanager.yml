global:
  smtp_smarthost: 'localhost:587'
  smtp_from: '<EMAIL>'

route:
  group_by: ['alertname']
  group_wait: 10s
  group_interval: 10s
  repeat_interval: 1h
  receiver: 'web.hook'
  routes:
  - match:
      severity: critical
    receiver: 'critical-alerts'
  - match:
      severity: warning
    receiver: 'warning-alerts'

receivers:
- name: 'web.hook'
  webhook_configs:
  - url: 'http://127.0.0.1:5001/'

- name: 'critical-alerts'
  webhook_configs:
  - url: 'http://127.0.0.1:5001/critical'
    send_resolved: true
  # Uncomment and configure for Discord notifications
  # discord_configs:
  # - webhook_url: 'YOUR_DISCORD_WEBHOOK_URL'
  #   title: '🚨 CRITICAL: {{ .GroupLabels.alertname }}'
  #   message: '{{ range .Alerts }}{{ .Annotations.summary }}{{ end }}'

- name: 'warning-alerts'
  webhook_configs:
  - url: 'http://127.0.0.1:5001/warning'
    send_resolved: true
  # Uncomment and configure for Discord notifications
  # discord_configs:
  # - webhook_url: 'YOUR_DISCORD_WEBHOOK_URL'
  #   title: '⚠️ WARNING: {{ .GroupLabels.alertname }}'
  #   message: '{{ range .Alerts }}{{ .Annotations.summary }}{{ end }}'

inhibit_rules:
  - source_match:
      severity: 'critical'
    target_match:
      severity: 'warning'
    equal: ['alertname', 'dev', 'instance']
