using System.Threading.Tasks;
using FluentAssertions;
using Microsoft.Extensions.Logging.Abstractions;
using Microsoft.ML;
using NSubstitute;
using StackExchange.Redis;
using SmaTrendFollower.Services;
using Xunit;

namespace SmaTrendFollower.Tests.Core.Ml;

public class RegimeClassifierReloadTests
{
    [Fact]
    public void RegimeClassifierService_CanBeCreated()
    {
        // Arrange
        var mockMarketDataService = Substitute.For<IMarketDataService>();
        var mockRedisService = Substitute.For<IOptimizedRedisConnectionService>();
        var mockBreadthService = Substitute.For<IBreadthService>();
        var mockDatabase = Substitute.For<IDatabase>();

        mockRedisService.GetDatabaseAsync().Returns(mockDatabase);

        // Act & Assert - should create without throwing
        var act = () => new RegimeClassifierService(
            mockMarketDataService,
            mockRedisService,
            mockBreadthService,
            NullLogger<RegimeClassifierService>.Instance);

        act.Should().NotThrow();
    }

    [Fact]
    public async Task DetectTodayAsync_DoesNotThrow()
    {
        // Arrange
        var mockMarketDataService = Substitute.For<IMarketDataService>();
        var mockRedisService = Substitute.For<IOptimizedRedisConnectionService>();
        var mockBreadthService = Substitute.For<IBreadthService>();
        var mockDatabase = Substitute.For<IDatabase>();

        mockRedisService.GetDatabaseAsync().Returns(mockDatabase);

        var svc = new RegimeClassifierService(
            mockMarketDataService,
            mockRedisService,
            mockBreadthService,
            NullLogger<RegimeClassifierService>.Instance);

        // Act & Assert - should not throw even without Redis data
        var act = async () => await svc.DetectTodayAsync();
        await act.Should().NotThrowAsync();
    }

    [Fact]
    public async Task ReloadModelAsync_DoesNotThrow()
    {
        // Arrange
        var mockMarketDataService = Substitute.For<IMarketDataService>();
        var mockRedisService = Substitute.For<IOptimizedRedisConnectionService>();
        var mockBreadthService = Substitute.For<IBreadthService>();
        var mockDatabase = Substitute.For<IDatabase>();

        mockRedisService.GetDatabaseAsync().Returns(mockDatabase);

        var svc = new RegimeClassifierService(
            mockMarketDataService,
            mockRedisService,
            mockBreadthService,
            NullLogger<RegimeClassifierService>.Instance);

        // Act & Assert - should handle model reload gracefully even if no model file exists
        var act = async () => await svc.ReloadModelAsync();
        await act.Should().NotThrowAsync();
    }

    [Fact]
    public void RegimeClassifierService_RequiresValidDependencies()
    {
        // Arrange
        var mockMarketDataService = Substitute.For<IMarketDataService>();
        var mockRedisService = Substitute.For<IOptimizedRedisConnectionService>();
        var mockBreadthService = Substitute.For<IBreadthService>();

        // Act & Assert - should throw when given null dependencies
        var act1 = () => new RegimeClassifierService(
            null!,
            mockRedisService,
            mockBreadthService,
            NullLogger<RegimeClassifierService>.Instance);

        var act2 = () => new RegimeClassifierService(
            mockMarketDataService,
            null!,
            mockBreadthService,
            NullLogger<RegimeClassifierService>.Instance);

        var act3 = () => new RegimeClassifierService(
            mockMarketDataService,
            mockRedisService,
            null!,
            NullLogger<RegimeClassifierService>.Instance);

        var act4 = () => new RegimeClassifierService(
            mockMarketDataService,
            mockRedisService,
            mockBreadthService,
            null!);

        act1.Should().Throw<ArgumentNullException>();
        act2.Should().Throw<ArgumentNullException>();
        act3.Should().Throw<ArgumentNullException>();
        act4.Should().Throw<ArgumentNullException>();
    }
}
