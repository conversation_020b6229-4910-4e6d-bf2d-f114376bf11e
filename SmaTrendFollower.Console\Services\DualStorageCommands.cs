using Microsoft.Extensions.Logging;
using SmaTrendFollower.Services;
using System.Text.Json;
using static SmaTrendFollower.Services.RedisKeyConstants;

namespace SmaTrendFollower.Services;

/// <summary>
/// Command-line interface for managing the dual storage system.
/// Provides utilities for monitoring, maintenance, and troubleshooting.
/// </summary>
public class DualStorageCommands
{
    private readonly ILiveStateStore _liveStateStore;
    private readonly IBarStore _historicalStore;
    private readonly ILogger<DualStorageCommands> _logger;

    public DualStorageCommands(
        ILiveStateStore liveStateStore,
        IBarStore historicalStore,
        ILogger<DualStorageCommands> logger)
    {
        _liveStateStore = liveStateStore;
        _historicalStore = historicalStore;
        _logger = logger;
    }

    /// <summary>
    /// Shows comprehensive status of both storage systems
    /// </summary>
    public async Task ShowStatusAsync()
    {
        _logger.LogInformation("=== Dual Storage System Status ===");

        try
        {
            // Redis status
            var isRedisHealthy = await _liveStateStore.IsHealthyAsync();
            var liveStats = await _liveStateStore.GetStatsAsync();

            _logger.LogInformation("--- Redis Live State Store ---");
            _logger.LogInformation("Health: {Status}", isRedisHealthy ? "✅ Healthy" : "❌ Unhealthy");
            _logger.LogInformation("Trailing Stops: {Count}", liveStats.TrailingStopCount);
            _logger.LogInformation("Signal Flags: {Count}", liveStats.SignalFlagCount);
            _logger.LogInformation("Position States: {Count}", liveStats.PositionStateCount);
            _logger.LogInformation("Retry Queue: {Count}", liveStats.RetryQueueLength);
            _logger.LogInformation("Market State Cache: {Count}", liveStats.MarketStateCacheCount);
            _logger.LogInformation("Last Health Check: {Time:yyyy-MM-dd HH:mm:ss} UTC", liveStats.LastHealthCheck);

            // SQLite status
            var storageStats = await _historicalStore.GetStatsAsync();

            _logger.LogInformation("--- SQLite Historical Store ---");
            _logger.LogInformation("Total Symbols: {Count}", storageStats.TotalSymbols);
            _logger.LogInformation("Total Bars: {Count:N0}", storageStats.TotalBars);
            _logger.LogInformation("Date Range: {Earliest:yyyy-MM-dd} to {Latest:yyyy-MM-dd}",
                storageStats.EarliestBar, storageStats.LatestBar);
            _logger.LogInformation("Storage Size: {Size:F1} MB", storageStats.StorageSizeBytes / 1024.0 / 1024.0);

            // Top symbols by bar count
            var topSymbols = storageStats.SymbolCounts
                .OrderByDescending(kvp => kvp.Value)
                .Take(10)
                .ToList();

            if (topSymbols.Any())
            {
                _logger.LogInformation("Top Symbols by Bar Count:");
                foreach (var kvp in topSymbols)
                {
                    _logger.LogInformation("  {Symbol}: {Count:N0} bars", kvp.Key, kvp.Value);
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get storage status");
        }
    }

    /// <summary>
    /// Shows detailed information about trailing stops
    /// </summary>
    public async Task ShowTrailingStopsAsync()
    {
        _logger.LogInformation("=== Trailing Stops ===");

        try
        {
            var stops = await _liveStateStore.GetAllTrailingStopsAsync();

            if (!stops.Any())
            {
                _logger.LogInformation("No trailing stops found");
                return;
            }

            _logger.LogInformation("Active Trailing Stops ({Count}):", stops.Count);
            foreach (var kvp in stops.OrderBy(x => x.Key))
            {
                var positionState = await _liveStateStore.GetPositionStateAsync(kvp.Key);
                if (positionState != null)
                {
                    var unrealizedPnL = (positionState.CurrentPrice - positionState.EntryPrice) * positionState.Quantity;
                    _logger.LogInformation("  {Symbol}: Stop={Stop:F2}, Entry={Entry:F2}, Current={Current:F2}, Qty={Qty}, P&L={PnL:F2}",
                        kvp.Key, kvp.Value, positionState.EntryPrice, positionState.CurrentPrice,
                        positionState.Quantity, unrealizedPnL);
                }
                else
                {
                    _logger.LogInformation("  {Symbol}: Stop={Stop:F2} (no position state)", kvp.Key, kvp.Value);
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to show trailing stops");
        }
    }

    /// <summary>
    /// Shows retry queue contents
    /// </summary>
    public async Task ShowRetryQueueAsync()
    {
        _logger.LogInformation("=== Retry Queue ===");

        try
        {
            var queueLength = await _liveStateStore.GetRetryQueueLengthAsync();
            _logger.LogInformation("Queue Length: {Length}", queueLength);

            if (queueLength == 0)
            {
                _logger.LogInformation("Retry queue is empty");
                return;
            }

            // Peek at items without removing them
            var items = new List<RetryItem>();
            for (int i = 0; i < Math.Min(queueLength, 10); i++)
            {
                var item = await _liveStateStore.DequeueRetryAsync();
                if (item != null)
                {
                    items.Add(item);
                    // Re-enqueue to maintain queue state
                    await _liveStateStore.EnqueueRetryAsync(item);
                }
            }

            _logger.LogInformation("Retry Items (showing first 10):");
            foreach (var item in items)
            {
                var timeUntilRetry = item.NextRetryTime - DateTime.UtcNow;
                _logger.LogInformation("  {Operation}: Attempt {Attempt}, Next retry in {Time:F1} minutes",
                    item.Operation, item.AttemptCount, timeUntilRetry.TotalMinutes);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to show retry queue");
        }
    }

    /// <summary>
    /// Cleans up old data from both storage systems
    /// </summary>
    public async Task CleanupAsync(int retainDays = 365)
    {
        _logger.LogInformation("=== Storage Cleanup ===");
        _logger.LogInformation("Retaining data for {Days} days", retainDays);

        try
        {
            var cutoffDate = DateTime.UtcNow.AddDays(-retainDays);

            // Cleanup historical bars
            await _historicalStore.CleanupOldBarsAsync(cutoffDate);
            _logger.LogInformation("✅ Historical bars cleanup completed");

            // Cleanup old signal flags
            await _liveStateStore.ClearSignalFlagsAsync(cutoffDate);
            _logger.LogInformation("✅ Signal flags cleanup completed");

            _logger.LogInformation("Cleanup completed successfully");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to perform cleanup");
        }
    }

    /// <summary>
    /// Tests connectivity and basic operations
    /// </summary>
    public async Task RunHealthCheckAsync()
    {
        _logger.LogInformation("=== Health Check ===");

        try
        {
            // Test Redis connectivity
            var isRedisHealthy = await _liveStateStore.IsHealthyAsync();
            _logger.LogInformation("Redis connectivity: {Status}", isRedisHealthy ? "✅ OK" : "❌ Failed");

            if (isRedisHealthy)
            {
                // Test basic Redis operations
                var testKey = "health_check_" + Guid.NewGuid().ToString("N")[..8];
                var testValue = DateTime.UtcNow;

                await _liveStateStore.SetMarketStateAsync(testKey, testValue, RedisKeyConstants.RedisKeyTTL.HealthCheck);
                var retrieved = await _liveStateStore.GetMarketStateAsync<object>(testKey);
                await _liveStateStore.RemoveMarketStateAsync(testKey);

                var redisOpsWorking = retrieved != null;
                _logger.LogInformation("Redis operations: {Status}", redisOpsWorking ? "✅ OK" : "❌ Failed");
            }

            // Test SQLite operations
            try
            {
                var stats = await _historicalStore.GetStatsAsync();
                _logger.LogInformation("SQLite operations: ✅ OK ({Symbols} symbols, {Bars:N0} bars)",
                    stats.TotalSymbols, stats.TotalBars);
            }
            catch (Exception ex)
            {
                _logger.LogError("SQLite operations: ❌ Failed - {Error}", ex.Message);
            }

            _logger.LogInformation("Health check completed");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Health check failed");
        }
    }

    /// <summary>
    /// Exports current state to JSON file for backup
    /// </summary>
    public async Task ExportStateAsync(string filePath)
    {
        _logger.LogInformation("=== Export State ===");
        _logger.LogInformation("Exporting to: {FilePath}", filePath);

        try
        {
            var stats = await _liveStateStore.GetStatsAsync();
            var trailingStops = await _liveStateStore.GetAllTrailingStopsAsync();

            var positionStates = new Dictionary<string, PositionState>();
            foreach (var symbol in trailingStops.Keys)
            {
                var state = await _liveStateStore.GetPositionStateAsync(symbol);
                if (state != null)
                {
                    positionStates[symbol] = state;
                }
            }

            var exportData = new
            {
                ExportTime = DateTime.UtcNow,
                Stats = stats,
                TrailingStops = trailingStops,
                PositionStates = positionStates
            };

            var json = JsonSerializer.Serialize(exportData, new JsonSerializerOptions
            {
                WriteIndented = true,
                PropertyNamingPolicy = JsonNamingPolicy.CamelCase
            });

            await File.WriteAllTextAsync(filePath, json);
            _logger.LogInformation("✅ State exported successfully");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to export state");
        }
    }

    /// <summary>
    /// Flushes all Redis data (use with caution)
    /// </summary>
    public async Task FlushRedisAsync(bool confirm = false)
    {
        if (!confirm)
        {
            _logger.LogWarning("⚠️  This will delete ALL Redis data. Use --confirm to proceed.");
            return;
        }

        _logger.LogInformation("=== Flush Redis ===");
        _logger.LogWarning("⚠️  Flushing ALL Redis data...");

        try
        {
            await _liveStateStore.FlushAllAsync();
            _logger.LogInformation("✅ Redis data flushed");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to flush Redis data");
        }
    }

    /// <summary>
    /// Tests the Redis hygiene and TTL standardization system
    /// </summary>
    public async Task TestRedisHygieneAsync()
    {
        _logger.LogInformation("=== Redis Hygiene & TTL Standardization Test ===");

        try
        {
            // Test TTL constants
            _logger.LogInformation("📋 Testing TTL Constants:");
            _logger.LogInformation("Signal TTL: {TTL}", RedisKeyTTL.Signal);
            _logger.LogInformation("Universe TTL: {TTL}", RedisKeyTTL.Universe);
            _logger.LogInformation("Stop TTL: {TTL}", RedisKeyTTL.Stop);
            _logger.LogInformation("Synthetic VIX TTL: {TTL}", RedisKeyTTL.SyntheticVix);
            _logger.LogInformation("VIX Data TTL: {TTL}", RedisKeyTTL.VixData);
            _logger.LogInformation("Regime TTL: {TTL}", RedisKeyTTL.Regime);

            // Test key pattern matching
            _logger.LogInformation("\n🔍 Testing Key Pattern Matching:");
            var testKeys = new[]
            {
                "signal:AAPL:20241227",
                "universe:today",
                "stop:MSFT",
                "vix:synthetic",
                "regime:today",
                "breadth:analysis:current",
                "health_check_test123",
                "unknown:pattern"
            };

            foreach (var key in testKeys)
            {
                var ttl = RedisKeyConstants.GetTTLForKey(key);
                if (ttl.HasValue)
                {
                    _logger.LogInformation("✅ Key '{Key}' → TTL: {TTL}", key, ttl.Value);
                }
                else
                {
                    _logger.LogWarning("❌ Key '{Key}' → No TTL pattern match", key);
                }
            }

            // Test pattern to TTL mapping
            _logger.LogInformation("\n🗺️ Testing Pattern to TTL Mapping:");
            _logger.LogInformation("Total patterns configured: {Count}", RedisKeyConstants.PatternToTTL.Count);

            var validMappings = 0;
            foreach (var (pattern, ttl) in RedisKeyConstants.PatternToTTL)
            {
                if (ttl > TimeSpan.Zero && ttl <= TimeSpan.FromDays(30))
                {
                    validMappings++;
                }
                else
                {
                    _logger.LogWarning("⚠️ Pattern '{Pattern}' has invalid TTL: {TTL}", pattern, ttl);
                }
            }

            _logger.LogInformation("Valid mappings: {Valid}/{Total}", validMappings, RedisKeyConstants.PatternToTTL.Count);

            // Test with actual Redis operations using standardized TTLs
            _logger.LogInformation("\n🧪 Testing Redis Operations with Standardized TTLs:");

            var testKey = "health_check_hygiene_test";
            var testValue = System.Text.Json.JsonSerializer.Serialize(new { timestamp = DateTime.UtcNow, test = "redis-hygiene" });

            await _liveStateStore.SetMarketStateAsync(testKey, testValue, RedisKeyTTL.HealthCheck);
            _logger.LogInformation("✅ Set test key with health check TTL: {TTL}", RedisKeyTTL.HealthCheck);

            var retrievedValue = await _liveStateStore.GetMarketStateAsync<string>(testKey);
            if (!string.IsNullOrEmpty(retrievedValue))
            {
                _logger.LogInformation("✅ Successfully retrieved test key value");
            }
            else
            {
                _logger.LogWarning("❌ Failed to retrieve test key value");
            }

            _logger.LogInformation("\n🎉 Redis hygiene test completed successfully!");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "❌ Redis hygiene test failed");
            throw;
        }
    }
}
