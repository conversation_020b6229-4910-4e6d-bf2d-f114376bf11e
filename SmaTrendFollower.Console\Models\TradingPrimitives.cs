namespace SmaTrendFollower.Models;

public readonly record struct TradingSignal(
    string Symbol,
    decimal Price,
    decimal Atr,
    decimal SixMonthReturn,
    decimal Momentum = 0m,
    DateTime Timestamp = default,
    float? RankingProbability = null)
{
    public decimal ATR => Atr; // Alias for backward compatibility
};
public readonly record struct PositionSizing(decimal Quantity, decimal StopLossPrice);

/// <summary>
/// Represents a real-time trade tick
/// </summary>
public readonly record struct TradeTick(
    string Symbol,
    decimal Price,
    long Size,
    DateTime Timestamp,
    string Exchange,
    string Conditions
);

/// <summary>
/// Represents a real-time quote tick
/// </summary>
public readonly record struct QuoteTick(
    string Symbol,
    decimal BidPrice,
    decimal AskPrice,
    long BidSize,
    long AskSize,
    DateTime Timestamp,
    string Exchange
);

/// <summary>
/// Represents a real-time aggregate (minute bar) tick
/// </summary>
public readonly record struct AggregateTick(
    string Symbol,
    decimal Open,
    decimal High,
    decimal Low,
    decimal Close,
    long Volume,
    decimal Vwap,
    DateTime Timestamp,
    long TradeCount
);

/// <summary>
/// MACD calculation result
/// </summary>
/// <param name="Macd">MACD line value</param>
/// <param name="Signal">Signal line value</param>
/// <param name="Histogram">MACD histogram value</param>
public readonly record struct MacdResult(double Macd, double Signal, double Histogram);
