using Microsoft.Extensions.Logging;
using SmaTrendFollower.Services;

namespace SmaTrendFollower.Examples;

/// <summary>
/// Demonstration of Redis hygiene and TTL standardization functionality
/// Shows how RedisKeyConstants provides standardized TTL values for different key types
/// </summary>
public class RedisHygieneDemo
{
    private readonly ILogger<RedisHygieneDemo> _logger;

    public RedisHygieneDemo(ILogger<RedisHygieneDemo> logger)
    {
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
    }

    /// <summary>
    /// Demonstrates the Redis key TTL standardization system
    /// </summary>
    public Task RunDemoAsync()
    {
        _logger.LogInformation("🧹 Redis Hygiene & TTL Standardization Demo");
        _logger.LogInformation("===========================================");

        // Demonstrate TTL constants
        DemonstrateStandardizedTTLs();

        // Demonstrate key pattern matching
        DemonstrateKeyPatternMatching();

        // Demonstrate pattern to TTL mapping
        DemonstratePatternToTTLMapping();

        _logger.LogInformation("✅ Redis hygiene demo completed successfully!");
        return Task.CompletedTask;
    }

    /// <summary>
    /// Shows all standardized TTL values
    /// </summary>
    private void DemonstrateStandardizedTTLs()
    {
        _logger.LogInformation("\n📋 Standardized TTL Values:");
        _logger.LogInformation("Signal TTL: {SignalTTL}", RedisKeyConstants.RedisKeyTTL.Signal);
        _logger.LogInformation("Universe TTL: {UniverseTTL}", RedisKeyConstants.RedisKeyTTL.Universe);
        _logger.LogInformation("Stop TTL: {StopTTL}", RedisKeyConstants.RedisKeyTTL.Stop);
        _logger.LogInformation("Synthetic VIX TTL: {SyntheticVixTTL}", RedisKeyConstants.RedisKeyTTL.SyntheticVix);
        _logger.LogInformation("VIX Data TTL: {VixDataTTL}", RedisKeyConstants.RedisKeyTTL.VixData);
        _logger.LogInformation("Regime TTL: {RegimeTTL}", RedisKeyConstants.RedisKeyTTL.Regime);
        _logger.LogInformation("Breadth TTL: {BreadthTTL}", RedisKeyConstants.RedisKeyTTL.Breadth);
        _logger.LogInformation("Execution TTL: {ExecutionTTL}", RedisKeyConstants.RedisKeyTTL.Execution);
        _logger.LogInformation("Health Check TTL: {HealthCheckTTL}", RedisKeyConstants.RedisKeyTTL.HealthCheck);
    }

    /// <summary>
    /// Demonstrates key pattern matching functionality
    /// </summary>
    private void DemonstrateKeyPatternMatching()
    {
        _logger.LogInformation("\n🔍 Key Pattern Matching:");

        var testKeys = new[]
        {
            "signal:AAPL:20241227",
            "universe:today",
            "stop:MSFT",
            "vix:synthetic",
            "vix:current",
            "regime:today",
            "breadth:analysis:current",
            "execution:log:20241227",
            "health_check_abc123",
            "unknown:key:pattern"
        };

        foreach (var key in testKeys)
        {
            var ttl = RedisKeyConstants.GetTTLForKey(key);
            if (ttl.HasValue)
            {
                _logger.LogInformation("✅ Key '{Key}' → TTL: {TTL}", key, ttl.Value);
            }
            else
            {
                _logger.LogWarning("❌ Key '{Key}' → No TTL pattern match", key);
            }
        }
    }

    /// <summary>
    /// Shows the pattern to TTL mapping system
    /// </summary>
    private void DemonstratePatternToTTLMapping()
    {
        _logger.LogInformation("\n🗺️ Pattern to TTL Mapping:");
        _logger.LogInformation("Total patterns configured: {Count}", RedisKeyConstants.PatternToTTL.Count);

        foreach (var (pattern, ttl) in RedisKeyConstants.PatternToTTL.Take(10))
        {
            _logger.LogInformation("Pattern '{Pattern}' → TTL: {TTL}", pattern, ttl);
        }

        if (RedisKeyConstants.PatternToTTL.Count > 10)
        {
            _logger.LogInformation("... and {More} more patterns", RedisKeyConstants.PatternToTTL.Count - 10);
        }
    }

    /// <summary>
    /// Validates that all TTL values are positive and reasonable
    /// </summary>
    public bool ValidateTTLValues()
    {
        _logger.LogInformation("\n🔬 Validating TTL Values:");

        var ttlProperties = typeof(RedisKeyConstants.RedisKeyTTL)
            .GetFields(System.Reflection.BindingFlags.Public | System.Reflection.BindingFlags.Static)
            .Where(f => f.FieldType == typeof(TimeSpan))
            .ToList();

        var allValid = true;

        foreach (var property in ttlProperties)
        {
            var ttlValue = (TimeSpan)property.GetValue(null)!;
            var isValid = ttlValue > TimeSpan.Zero && ttlValue <= TimeSpan.FromDays(30);

            if (isValid)
            {
                _logger.LogInformation("✅ {Name}: {Value} (valid)", property.Name, ttlValue);
            }
            else
            {
                _logger.LogError("❌ {Name}: {Value} (invalid - should be positive and <= 30 days)", property.Name, ttlValue);
                allValid = false;
            }
        }

        return allValid;
    }

    /// <summary>
    /// Validates that all key patterns have corresponding TTL mappings
    /// </summary>
    public bool ValidatePatternMappings()
    {
        _logger.LogInformation("\n🔗 Validating Pattern Mappings:");

        var patternProperties = typeof(RedisKeyConstants.KeyPatterns)
            .GetFields(System.Reflection.BindingFlags.Public | System.Reflection.BindingFlags.Static)
            .Where(f => f.FieldType == typeof(string))
            .ToList();

        var allMapped = true;

        foreach (var property in patternProperties)
        {
            var pattern = (string)property.GetValue(null)!;
            var hasTTL = RedisKeyConstants.PatternToTTL.ContainsKey(pattern);

            if (hasTTL)
            {
                var ttl = RedisKeyConstants.PatternToTTL[pattern];
                _logger.LogInformation("✅ Pattern '{Pattern}' → TTL: {TTL}", pattern, ttl);
            }
            else
            {
                _logger.LogError("❌ Pattern '{Pattern}' has no TTL mapping", pattern);
                allMapped = false;
            }
        }

        return allMapped;
    }

    /// <summary>
    /// Runs comprehensive validation of the Redis hygiene system
    /// </summary>
    public Task<bool> RunValidationAsync()
    {
        _logger.LogInformation("🧪 Running Redis Hygiene System Validation");
        _logger.LogInformation("==========================================");

        var ttlValid = ValidateTTLValues();
        var mappingValid = ValidatePatternMappings();

        var overallValid = ttlValid && mappingValid;

        if (overallValid)
        {
            _logger.LogInformation("\n🎉 All validations passed! Redis hygiene system is properly configured.");
        }
        else
        {
            _logger.LogError("\n💥 Some validations failed! Please review the Redis hygiene configuration.");
        }

        return Task.FromResult(overallValid);
    }
}
