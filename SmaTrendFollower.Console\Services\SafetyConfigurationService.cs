using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;

namespace SmaTrendFollower.Services;

/// <summary>
/// Service for managing safety configuration
/// </summary>
public sealed class SafetyConfigurationService : ISafetyConfigurationService
{
    private readonly ILogger<SafetyConfigurationService> _logger;
    private readonly IConfiguration _configuration;

    public SafetyConfigurationService(ILogger<SafetyConfigurationService> logger, IConfiguration configuration)
    {
        _logger = logger;
        _configuration = configuration;
    }

    public SafetyConfiguration LoadConfiguration()
    {
        try
        {
            // Try to load from configuration first (appsettings.json)
            var safetySection = _configuration.GetSection("Safety");
            if (safetySection.Exists())
            {
                _logger.LogInformation("Loading safety configuration from appsettings.json");
                var configFromSettings = new SafetyConfiguration();
                safetySection.Bind(configFromSettings);

                _logger.LogInformation("Safety configuration loaded: MaxDailyLoss={MaxDailyLoss}, Environment={Environment}",
                    configFromSettings.MaxDailyLoss, configFromSettings.AllowedEnvironment);
                return configFromSettings;
            }

            // Fallback to environment variables
            _logger.LogWarning("Safety configuration not found in appsettings.json, falling back to environment variables");
            var config = new SafetyConfiguration
            {
                MaxDailyLoss = GetDecimalFromEnv("SAFETY_MAX_DAILY_LOSS", 500m),
                MaxPositionSizePercent = GetDecimalFromEnv("SAFETY_MAX_POSITION_SIZE_PERCENT", 0.05m),
                MaxPositions = GetIntFromEnv("SAFETY_MAX_POSITIONS", 10),
                MaxDailyTrades = GetIntFromEnv("SAFETY_MAX_DAILY_TRADES", 20),
                MinAccountEquity = GetDecimalFromEnv("SAFETY_MIN_ACCOUNT_EQUITY", 1000m),
                MaxSingleTradeValue = GetDecimalFromEnv("SAFETY_MAX_SINGLE_TRADE_VALUE", 2000m),
                RequireConfirmation = GetBoolFromEnv("SAFETY_REQUIRE_CONFIRMATION", true),
                DryRunMode = GetBoolFromEnv("SAFETY_DRY_RUN_MODE", false),
                AllowedEnvironment = GetEnumFromEnv("SAFETY_ALLOWED_ENVIRONMENT", TradingEnvironment.Paper)
            };

            var (isValid, errors) = ValidateConfiguration(config);
            if (!isValid)
            {
                _logger.LogWarning("Invalid safety configuration loaded, using safe defaults. Errors: {Errors}", 
                    string.Join(", ", errors));
                return CreateSafeDefaults();
            }

            _logger.LogInformation("Safety configuration loaded: DryRun={DryRun}, MaxDailyLoss={MaxDailyLoss:C}, " +
                                 "Environment={Environment}, RequireConfirmation={RequireConfirmation}",
                config.DryRunMode, config.MaxDailyLoss, config.AllowedEnvironment, config.RequireConfirmation);

            return config;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error loading safety configuration, using safe defaults");
            return CreateSafeDefaults();
        }
    }

    public SafetyConfiguration CreateSafeDefaults()
    {
        return new SafetyConfiguration
        {
            MaxDailyLoss = 500m,
            MaxPositionSizePercent = 0.05m, // 5%
            MaxPositions = 10,
            MaxDailyTrades = 20,
            MinAccountEquity = 1000m,
            MaxSingleTradeValue = 2000m,
            RequireConfirmation = true,
            DryRunMode = false,
            AllowedEnvironment = TradingEnvironment.Paper
        };
    }

    public SafetyConfiguration CreatePaperTradingConfiguration()
    {
        return new SafetyConfiguration
        {
            MaxDailyLoss = 1000m,
            MaxPositionSizePercent = 0.10m, // 10% for paper trading
            MaxPositions = 20,
            MaxDailyTrades = 50,
            MinAccountEquity = 100m,
            MaxSingleTradeValue = 5000m,
            RequireConfirmation = false,
            DryRunMode = false,
            AllowedEnvironment = TradingEnvironment.Paper
        };
    }

    public SafetyConfiguration CreateLiveTradingConfiguration()
    {
        return new SafetyConfiguration
        {
            MaxDailyLoss = 250m, // More conservative for live trading
            MaxPositionSizePercent = 0.03m, // 3% max position size
            MaxPositions = 8,
            MaxDailyTrades = 15,
            MinAccountEquity = 5000m, // Higher minimum for live trading
            MaxSingleTradeValue = 1500m,
            RequireConfirmation = true,
            DryRunMode = false,
            AllowedEnvironment = TradingEnvironment.Live
        };
    }

    public (bool IsValid, string[] Errors) ValidateConfiguration(SafetyConfiguration config)
    {
        var errors = new List<string>();

        if (config.MaxDailyLoss <= 0)
            errors.Add("MaxDailyLoss must be greater than 0");

        if (config.MaxPositionSizePercent <= 0 || config.MaxPositionSizePercent > 1)
            errors.Add("MaxPositionSizePercent must be between 0 and 1");

        if (config.MaxPositions <= 0)
            errors.Add("MaxPositions must be greater than 0");

        if (config.MaxDailyTrades <= 0)
            errors.Add("MaxDailyTrades must be greater than 0");

        if (config.MinAccountEquity < 0)
            errors.Add("MinAccountEquity cannot be negative");

        if (config.MaxSingleTradeValue <= 0)
            errors.Add("MaxSingleTradeValue must be greater than 0");

        return (errors.Count == 0, errors.ToArray());
    }

    private decimal GetDecimalFromEnv(string key, decimal defaultValue)
    {
        var value = Environment.GetEnvironmentVariable(key);
        return decimal.TryParse(value, out var result) ? result : defaultValue;
    }

    private int GetIntFromEnv(string key, int defaultValue)
    {
        var value = Environment.GetEnvironmentVariable(key);
        return int.TryParse(value, out var result) ? result : defaultValue;
    }

    private bool GetBoolFromEnv(string key, bool defaultValue)
    {
        var value = Environment.GetEnvironmentVariable(key);
        return bool.TryParse(value, out var result) ? result : defaultValue;
    }

    private TradingEnvironment GetEnumFromEnv(string key, TradingEnvironment defaultValue)
    {
        var value = Environment.GetEnvironmentVariable(key);
        return Enum.TryParse<TradingEnvironment>(value, true, out var result) ? result : defaultValue;
    }
}
