using FluentAssertions;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using SmaTrendFollower.Configuration;
using SmaTrendFollower.Services;
using Xunit;
using SmaTrendFollower.Tests.TestHelpers;

namespace SmaTrendFollower.Tests.Integration;

/// <summary>
/// Integration tests for Phase 5 services
/// Verifies that all Phase 5 services can be properly instantiated and integrated
/// </summary>
public class Phase5ServicesIntegrationTests : IDisposable
{
    private readonly ServiceProvider _serviceProvider;

    public Phase5ServicesIntegrationTests()
    {
        var services = new ServiceCollection();
        
        // Add configuration
        var configuration = new ConfigurationBuilder()
            .AddInMemoryCollection(new Dictionary<string, string?>
            {
                ["Alpaca:ApiKey"] = "test-key",
                ["Alpaca:SecretKey"] = "test-secret",
                ["Alpaca:BaseUrl"] = "https://paper-api.alpaca.markets",
                ["Polygon:ApiKey"] = "test-polygon-key",
                ["Redis:ConnectionString"] = "localhost:6379",
                ["Discord:BotToken"] = "test-bot-token",
                ["Discord:ChannelId"] = "123456789",
                ["TickStream:CacheExpiry"] = "01:00:00",
                ["PreMarketFilter:MaxVolatilityPercent"] = "2.0",
                ["PreMarketFilter:MaxGapPercent"] = "4.0",
                ["BreadthService:CacheExpiry"] = "00:15:00",
                ["ExecutionQA:MaxAcceptableSlippagePercent"] = "0.5"
            })
            .Build();
        
        services.AddSingleton<IConfiguration>(configuration);
        
        // Add logging
        services.AddLogging(builder => builder.AddConsole());
        
        // Add HttpClient
        services.AddHttpClient();
        
        // Add all SmaTrendFollower services
        services.AddFullTradingSystem(configuration);
        
        _serviceProvider = services.BuildServiceProvider();
    }

    [Fact]
    public void ServiceProvider_ShouldResolveTickStreamService()
    {
        // Act
        var service = _serviceProvider.GetService<ITickStreamService>();

        // Assert
        service.Should().NotBeNull();
        service.Should().BeOfType<TickStreamService>();
    }

    [Fact]
    public void ServiceProvider_ShouldResolvePreMarketFilterService()
    {
        // Act
        var service = _serviceProvider.GetService<IPreMarketFilterService>();

        // Assert
        service.Should().NotBeNull();
        service.Should().BeOfType<PreMarketFilterService>();
    }

    [Fact]
    public void ServiceProvider_ShouldResolveBreadthService()
    {
        // Act
        var service = _serviceProvider.GetService<IBreadthService>();

        // Assert
        service.Should().NotBeNull();
        service.Should().BeOfType<BreadthService>();
    }

    [Fact]
    public void ServiceProvider_ShouldResolveExecutionQAService()
    {
        // Act
        var service = _serviceProvider.GetService<IExecutionQAService>();

        // Assert
        service.Should().NotBeNull();
        service.Should().BeOfType<ExecutionQAService>();
    }

    [Fact]
    public void ServiceProvider_ShouldResolveAllPhase5ServicesSimultaneously()
    {
        // Act
        var tickStreamService = _serviceProvider.GetService<ITickStreamService>();
        var preMarketFilterService = _serviceProvider.GetService<IPreMarketFilterService>();
        var breadthService = _serviceProvider.GetService<IBreadthService>();
        var executionQAService = _serviceProvider.GetService<IExecutionQAService>();

        // Assert
        tickStreamService.Should().NotBeNull();
        preMarketFilterService.Should().NotBeNull();
        breadthService.Should().NotBeNull();
        executionQAService.Should().NotBeNull();
    }

    [Fact]
    public void ServiceProvider_ShouldResolveDependenciesForPhase5Services()
    {
        // Act & Assert - These should not throw exceptions
        var polygonFactory = _serviceProvider.GetService<IPolygonClientFactory>();
        var redisService = _serviceProvider.GetService<IOptimizedRedisConnectionService>();
        var discordService = _serviceProvider.GetService<IDiscordNotificationService>();
        var universeProvider = _serviceProvider.GetService<IDynamicUniverseProvider>();
        var marketDataService = _serviceProvider.GetService<IMarketDataService>();
        var regimeService = _serviceProvider.GetService<IMarketRegimeService>();
        var sessionGuard = _serviceProvider.GetService<IMarketSessionGuard>();

        // Assert all dependencies are available
        polygonFactory.Should().NotBeNull();
        redisService.Should().NotBeNull();
        discordService.Should().NotBeNull();
        universeProvider.Should().NotBeNull();
        marketDataService.Should().NotBeNull();
        regimeService.Should().NotBeNull();
        sessionGuard.Should().NotBeNull();
    }

    [Fact]
    public void Phase5Services_ShouldHaveProperLifetime()
    {
        // Act - Get services multiple times from different scopes
        using var scope1 = _serviceProvider.CreateScope();
        using var scope2 = _serviceProvider.CreateScope();

        var tickStream1 = scope1.ServiceProvider.GetService<ITickStreamService>();
        var tickStream2 = scope2.ServiceProvider.GetService<ITickStreamService>();

        var preMarket1 = scope1.ServiceProvider.GetService<IPreMarketFilterService>();
        var preMarket2 = scope2.ServiceProvider.GetService<IPreMarketFilterService>();

        // Assert - Services should be scoped (different instances across scopes)
        tickStream1.Should().NotBeSameAs(tickStream2);
        preMarket1.Should().NotBeSameAs(preMarket2);
    }

    [Fact]
    public void Phase5Services_ShouldImplementIDisposable()
    {
        // Act
        var tickStreamService = _serviceProvider.GetService<ITickStreamService>();
        var preMarketFilterService = _serviceProvider.GetService<IPreMarketFilterService>();
        var breadthService = _serviceProvider.GetService<IBreadthService>();
        var executionQAService = _serviceProvider.GetService<IExecutionQAService>();

        // Assert
        tickStreamService.Should().BeAssignableTo<IDisposable>();
        preMarketFilterService.Should().BeAssignableTo<IDisposable>();
        breadthService.Should().BeAssignableTo<IDisposable>();
        executionQAService.Should().BeAssignableTo<IDisposable>();
    }

    [Fact]
    public void ServiceProvider_ShouldCreateServiceScopeWithPhase5Services()
    {
        // Act
        using var scope = _serviceProvider.CreateScope();
        var scopedProvider = scope.ServiceProvider;

        var tickStreamService = scopedProvider.GetService<ITickStreamService>();
        var preMarketFilterService = scopedProvider.GetService<IPreMarketFilterService>();
        var breadthService = scopedProvider.GetService<IBreadthService>();
        var executionQAService = scopedProvider.GetService<IExecutionQAService>();

        // Assert
        tickStreamService.Should().NotBeNull();
        preMarketFilterService.Should().NotBeNull();
        breadthService.Should().NotBeNull();
        executionQAService.Should().NotBeNull();
    }

    [Fact]
    public void Phase5Services_ShouldNotThrowOnInstantiation()
    {
        // Act & Assert - These should not throw exceptions during construction
        var act1 = () => _serviceProvider.GetService<ITickStreamService>();
        var act2 = () => _serviceProvider.GetService<IPreMarketFilterService>();
        var act3 = () => _serviceProvider.GetService<IBreadthService>();
        var act4 = () => _serviceProvider.GetService<IExecutionQAService>();

        act1.Should().NotThrow();
        act2.Should().NotThrow();
        act3.Should().NotThrow();
        act4.Should().NotThrow();
    }

    public void Dispose()
    {
        _serviceProvider?.Dispose();
    }
}
