using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using StackExchange.Redis;
using System.Text.Json;
using SmaTrendFollower.Backtesting.Replay;

namespace SmaTrendFollower.Controllers;

/// <summary>
/// API controller for backtesting results and dashboard data
/// </summary>
[ApiController]
[Route("[controller]")]
public class BacktestController : ControllerBase
{
    private readonly IConnectionMultiplexer _redis;
    private readonly ILogger<BacktestController> _logger;

    public BacktestController(
        IConnectionMultiplexer redis,
        ILogger<BacktestController> logger)
    {
        _redis = redis;
        _logger = logger;
    }

    /// <summary>
    /// Gets the latest backtest summary for dashboard display
    /// </summary>
    /// <returns>Backtest summary JSON</returns>
    [HttpGet("summary")]
    public async Task<IActionResult> GetSummaryAsync()
    {
        try
        {
            var database = _redis.GetDatabase();
            var summaryJson = await database.StringGetAsync("backtest:last");

            if (!summaryJson.HasValue)
            {
                _logger.LogWarning("No backtest results found in Redis");
                return Ok(BacktestSummary.Empty);
            }

            var summary = JsonSerializer.Deserialize<BacktestSummary>(summaryJson!);
            return Ok(summary);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving backtest summary");
            return StatusCode(500, new { error = "Failed to retrieve backtest summary" });
        }
    }

    /// <summary>
    /// Gets backtest status information
    /// </summary>
    /// <returns>Status information</returns>
    [HttpGet("status")]
    public async Task<IActionResult> GetStatusAsync()
    {
        try
        {
            var database = _redis.GetDatabase();
            var lastUpdate = await database.StringGetAsync("backtest:last_update");
            
            var status = new
            {
                hasResults = await database.KeyExistsAsync("backtest:last"),
                lastUpdate = lastUpdate.HasValue ? DateTime.Parse(lastUpdate!) : (DateTime?)null,
                serverTime = DateTime.UtcNow
            };

            return Ok(status);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving backtest status");
            return StatusCode(500, new { error = "Failed to retrieve backtest status" });
        }
    }

    /// <summary>
    /// Clears backtest results (for testing purposes)
    /// </summary>
    /// <returns>Success status</returns>
    [HttpDelete("clear")]
    public async Task<IActionResult> ClearResultsAsync()
    {
        try
        {
            var database = _redis.GetDatabase();
            await database.KeyDeleteAsync("backtest:last");
            await database.KeyDeleteAsync("backtest:last_update");

            _logger.LogInformation("Cleared backtest results from Redis");
            return Ok(new { message = "Backtest results cleared successfully" });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error clearing backtest results");
            return StatusCode(500, new { error = "Failed to clear backtest results" });
        }
    }
}
