using Microsoft.Extensions.Logging.Abstractions;
using Moq;
using SmaTrendFollower.Services;
using StackExchange.Redis;
using Xunit;
using Xunit.Abstractions;
using SmaTrendFollower.Tests.TestHelpers;

namespace SmaTrendFollower.Tests.Unit;

/// <summary>
/// Unit tests for VIX cache metrics service
/// </summary>
public class VixCacheMetricsServiceTests
{
    private readonly ITestOutputHelper _output;
    private readonly VixCacheMetricsService _metricsService;

    public VixCacheMetricsServiceTests(ITestOutputHelper output)
    {
        _output = output;

        var logger = new NullLogger<VixCacheMetricsService>();
        // Use null for Redis connection in tests - the service handles this gracefully
        _metricsService = new VixCacheMetricsService(logger, null);
    }

    [Fact]
    public async Task RecordCacheHitAsync_ShouldIncrementHitCounter()
    {
        // Act
        await _metricsService.RecordCacheHitAsync("TestSource", TimeSpan.FromMinutes(2), 0.9m);
        var metrics = await _metricsService.GetMetricsAsync();

        // Assert
        Assert.Equal(1, metrics.CacheHits);
        Assert.Equal(0, metrics.CacheMisses);
        Assert.Equal(1.0m, metrics.CacheHitRate);
        _output.WriteLine($"Cache hit recorded: {metrics.CacheHits} hits, {metrics.CacheHitRate:P} hit rate");
    }

    [Fact]
    public async Task RecordCacheMissAsync_ShouldIncrementMissCounter()
    {
        // Act
        await _metricsService.RecordCacheMissAsync("Cache expired");
        var metrics = await _metricsService.GetMetricsAsync();

        // Assert
        Assert.Equal(0, metrics.CacheHits);
        Assert.Equal(1, metrics.CacheMisses);
        Assert.Equal(0.0m, metrics.CacheHitRate);
        _output.WriteLine($"Cache miss recorded: {metrics.CacheMisses} misses, {metrics.CacheHitRate:P} hit rate");
    }

    [Fact]
    public async Task RecordRetrievalPerformanceAsync_ShouldTrackApiCalls()
    {
        // Act
        await _metricsService.RecordRetrievalPerformanceAsync("WebScraping", TimeSpan.FromSeconds(3), true, 5);
        await _metricsService.RecordRetrievalPerformanceAsync("Synthetic", TimeSpan.FromSeconds(1), true, 2);
        var metrics = await _metricsService.GetMetricsAsync();

        // Assert
        Assert.Equal(2, metrics.TotalRetrievals);
        Assert.Equal(7, metrics.TotalApiCalls);
        Assert.Equal(3.5m, metrics.AverageApiCallsPerRetrieval);
        _output.WriteLine($"Performance recorded: {metrics.TotalRetrievals} retrievals, {metrics.TotalApiCalls} API calls, {metrics.AverageApiCallsPerRetrieval:F1} avg calls per retrieval");
    }

    [Fact]
    public async Task GetMetricsAsync_ShouldCalculateCorrectRates()
    {
        // Act
        await _metricsService.RecordCacheHitAsync("Source1", TimeSpan.FromMinutes(1), 0.9m);
        await _metricsService.RecordCacheHitAsync("Source2", TimeSpan.FromMinutes(2), 0.8m);
        await _metricsService.RecordCacheMissAsync("Expired");
        await _metricsService.RecordRetrievalPerformanceAsync("WebScraping", TimeSpan.FromSeconds(2), true, 3);

        var metrics = await _metricsService.GetMetricsAsync();

        // Assert
        Assert.Equal(2, metrics.CacheHits);
        Assert.Equal(1, metrics.CacheMisses);
        Assert.Equal(3, metrics.TotalCacheRequests);
        Assert.Equal(2.0m / 3.0m, metrics.CacheHitRate, 2); // 66.67%
        Assert.Equal(1, metrics.TotalRetrievals);
        Assert.Equal(3, metrics.TotalApiCalls);
        Assert.Equal(3.0m, metrics.AverageApiCallsPerRetrieval);

        _output.WriteLine($"Comprehensive metrics: {metrics.CacheHits} hits, {metrics.CacheMisses} misses, {metrics.CacheHitRate:P} hit rate");
        _output.WriteLine($"Performance: {metrics.TotalRetrievals} retrievals, {metrics.TotalApiCalls} API calls");
    }

    [Fact]
    public async Task ResetMetricsAsync_ShouldClearAllCounters()
    {
        // Record some metrics first
        await _metricsService.RecordCacheHitAsync("Source1", TimeSpan.FromMinutes(1), 0.9m);
        await _metricsService.RecordCacheMissAsync("Expired");
        await _metricsService.RecordRetrievalPerformanceAsync("WebScraping", TimeSpan.FromSeconds(2), true, 3);

        // Act
        await _metricsService.ResetMetricsAsync();
        var metrics = await _metricsService.GetMetricsAsync();

        // Assert
        Assert.Equal(0, metrics.CacheHits);
        Assert.Equal(0, metrics.CacheMisses);
        Assert.Equal(0, metrics.TotalCacheRequests);
        Assert.Equal(0, metrics.TotalRetrievals);
        Assert.Equal(0, metrics.TotalApiCalls);
        Assert.Equal(0.0m, metrics.CacheHitRate);
        Assert.Equal(0.0m, metrics.AverageApiCallsPerRetrieval);

        _output.WriteLine("Metrics reset successfully - all counters are zero");
    }

    [Fact]
    public async Task GetMetricsAsync_WithoutRedis_ShouldStillWork()
    {
        // Arrange
        var logger = new NullLogger<VixCacheMetricsService>();
        var serviceWithoutRedis = new VixCacheMetricsService(logger, null);

        // Act
        await serviceWithoutRedis.RecordCacheHitAsync("Source1", TimeSpan.FromMinutes(1), 0.9m);
        await serviceWithoutRedis.RecordCacheMissAsync("No Redis");
        var metrics = await serviceWithoutRedis.GetMetricsAsync();

        // Assert
        Assert.Equal(1, metrics.CacheHits);
        Assert.Equal(1, metrics.CacheMisses);
        Assert.Equal(0.5m, metrics.CacheHitRate);
        _output.WriteLine("Metrics service works without Redis connection");
    }

    [Theory]
    [InlineData("Polygon", 0.95)]
    [InlineData("BraveSearch", 0.90)]
    [InlineData("WebScraping", 0.80)]
    [InlineData("Synthetic", 0.60)]
    public async Task RecordCacheHitAsync_WithDifferentSources_ShouldTrackCorrectly(string dataSource, decimal qualityScore)
    {
        // Act
        await _metricsService.RecordCacheHitAsync(dataSource, TimeSpan.FromMinutes(1), qualityScore);
        var metrics = await _metricsService.GetMetricsAsync();

        // Assert
        Assert.Equal(1, metrics.CacheHits);
        Assert.Equal(1.0m, metrics.CacheHitRate);
        _output.WriteLine($"Cache hit recorded for {dataSource} with quality {qualityScore:F2}");
    }

    [Theory]
    [InlineData("Cache not found")]
    [InlineData("Cache expired (default TTL)")]
    [InlineData("Cache expired (quality TTL)")]
    [InlineData("Invalid cached data")]
    public async Task RecordCacheMissAsync_WithDifferentReasons_ShouldTrackCorrectly(string reason)
    {
        // Act
        await _metricsService.RecordCacheMissAsync(reason);
        var metrics = await _metricsService.GetMetricsAsync();

        // Assert
        Assert.Equal(1, metrics.CacheMisses);
        Assert.Equal(0.0m, metrics.CacheHitRate);
        _output.WriteLine($"Cache miss recorded for reason: {reason}");
    }

    [Fact]
    public async Task ConcurrentMetricsOperations_ShouldBeThreadSafe()
    {
        var tasks = new List<Task>();

        // Act - Simulate concurrent operations
        for (int i = 0; i < 10; i++)
        {
            tasks.Add(_metricsService.RecordCacheHitAsync($"Source{i}", TimeSpan.FromMinutes(i), 0.9m));
            tasks.Add(_metricsService.RecordCacheMissAsync($"Miss{i}"));
            tasks.Add(_metricsService.RecordRetrievalPerformanceAsync($"Perf{i}", TimeSpan.FromSeconds(i), true, i));
        }

        await Task.WhenAll(tasks);
        var metrics = await _metricsService.GetMetricsAsync();

        // Assert
        Assert.Equal(10, metrics.CacheHits);
        Assert.Equal(10, metrics.CacheMisses);
        Assert.Equal(10, metrics.TotalRetrievals);
        Assert.Equal(0.5m, metrics.CacheHitRate);
        _output.WriteLine($"Concurrent operations completed: {metrics.CacheHits} hits, {metrics.CacheMisses} misses");
    }
}
